
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title if title else "SNR Application System" }}</title>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    
</head>
<body>









<!-- Navbar -->
<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('search') }}">
            <i class="fas fa-phone-alt mr-2"></i>NCA Numbering
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNavAltMarkup" aria-controls="navbarNavAltMarkup" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNavAltMarkup">
            <ul class="navbar-nav mr-auto"> {# Main navigation links #}
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                        {# Admin Links #}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('search') or request.endpoint == 'dashboard' and not request.endpoint.startswith('e164') %}active{% endif %}" href="{{ url_for('search') }}">SNR Management</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('e164.dashboard') or request.endpoint.startswith('e164.submit_audit') or request.endpoint.startswith('e164.operator_comparison') or request.endpoint.startswith('e164.review_pending_submissions') or request.endpoint.startswith('e164.analytics') %}active{% endif %}" href="{{ url_for('e164.dashboard') }}">E.164 Audit</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if request.endpoint == 'review_applications' or request.endpoint == 'generate_report' or request.endpoint == 'e164.list_users' %}active{% endif %}" href="#" id="adminToolsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                Admin Tools
                            </a>
                            <div class="dropdown-menu bg-dark" aria-labelledby="adminToolsDropdown">
                                <a class="dropdown-item text-light {% if request.endpoint == 'review_applications' %}active{% endif %}" href="{{ url_for('review_applications') }}">SNR Application Review</a>
                                <a class="dropdown-item text-light {% if request.endpoint == 'generate_report' %}active{% endif %}" href="{{ url_for('generate_report') }}">SNR Reports</a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-light {% if request.endpoint == 'e164.list_users' %}active{% endif %}" href="{{ url_for('e164.list_users') }}">User Management</a>
                                <a class="dropdown-item text-light {% if request.endpoint == 'e164.review_pending_submissions' %}active{% endif %}" href="{{ url_for('e164.dashboard') }}">Review E.164 Submissions</a>  
                                <a class="dropdown-item text-light {% if request.endpoint == 'e164.analytics' %}active{% endif %}" href="{{ url_for('e164.dashboard') }}">E.164 Analytics</a>
                            </div>
                        </li>
                    {% else %}
                        {# Non-Admin (Applicant/Operator) Links #}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'search' %}active{% endif %}" href="{{ url_for('search') }}">Find Numbers</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">My SNR Applications</a>
                        </li>
                        {% if current_user.operator_id %} {# Check if the user is an operator type #}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('e164.dashboard') or request.endpoint.startswith('e164.submit_audit') %}active{% endif %}" href="{{ url_for('e164.dashboard', tab='submission') }}">E.164 Submissions</a>
                        </li>
                        {% endif %}
                    {% endif %}
                {% endif %}
            </ul>

            <ul class="navbar-nav"> {# Right-aligned items #}
                {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications_count > 0 %}
                                <span class="badge badge-danger badge-pill">{{ unread_notifications_count }}</span>
                            {% endif %}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right bg-dark" aria-labelledby="notificationsDropdown" style="min-width: 300px; max-height: 400px; overflow-y: auto;">
                            {% if notifications %}
                                {% for notification in notifications %}
                                    <a class="dropdown-item text-light" href="{{ url_for('view_notification', notification_id=notification._id) }}">
                                        <small>{{ notification.message }}</small><br>
                                        <small class="text-muted">{{ notification.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </a>
                                {% endfor %}
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-light text-center" href="{{ url_for('view_notifications') }}">View all notifications</a>
                            {% else %}
                                <a class="dropdown-item text-light text-center text-muted" href="#">No new notifications</a>
                            {% endif %}
                        </div>
                    </li>

                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-user mr-1"></i>
                            {{ current_user.contact if current_user.contact else current_user.email }}
                        </a>
                        <div class="dropdown-menu dropdown-menu-right bg-dark" aria-labelledby="userDropdown">
                            <a class="dropdown-item text-light" href="{{ url_for('update_account') }}">My Account</a>
                            <div class="dropdown-divider"></div>
                            <a class="dropdown-item text-light" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt mr-2"></i>Logout</a>
                        </div>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">Register</a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>





<div class="container mt-3">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
</div>


<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"> </script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</body>
</html>
