{% extends "base.html" %}

{% block title %}Numbering Resource Audit Dashboard{% endblock %}

{% block styles %}
<style>
    /* Glassmorphic style for the dashboard */
    .glass-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .stat-card {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        overflow: hidden;
        border-radius: 15px;
        padding: 20px;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(31, 38, 135, 0.1);
        transition: all 0.3s ease;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 30px rgba(31, 38, 135, 0.2);
    }
    
    .stat-card .stat-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        font-size: 24px;
        opacity: 0.2;
    }
    
    .stat-card .stat-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 0;
    }
    
    .stat-card .stat-label {
        font-size: 1rem;
        color: #666;
        text-align: center;
    }
    
    .section-title {
        position: relative;
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 20px;
        border-bottom: 2px solid rgba(31, 38, 135, 0.2);
        padding-bottom: 10px;
    }
    
    .filter-label {
        font-weight: 500;
        margin-right: 10px;
    }
    
    .btn-glass {
        background: rgba(72, 149, 239, 0.6);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    .btn-glass:hover {
        background: rgba(72, 149, 239, 0.8);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(31, 38, 135, 0.2);
    }
    
    .header-underline {
        height: 3px;
        width: 50px;
        background: linear-gradient(90deg, rgba(72, 149, 239, 0.8), rgba(72, 149, 239, 0.2));
        margin-bottom: 20px;
    }
    
    .gauge-container {
        width: 200px;
        height: 100px;
        margin: 0 auto;
        position: relative;
    }
    
    .badge-glass {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border-radius: 10px;
        padding: 5px 10px;
        font-weight: 500;
    }
    
    .badge-glass.good {
        background: rgba(75, 192, 192, 0.2);
        color: rgba(75, 192, 192, 1);
        border: 1px solid rgba(75, 192, 192, 0.3);
    }
    
    .badge-glass.warning {
        background: rgba(255, 205, 86, 0.2);
        color: rgba(255, 159, 64, 1);
        border: 1px solid rgba(255, 205, 86, 0.3);
    }
    
    .badge-glass.critical {
        background: rgba(255, 99, 132, 0.2);
        color: rgba(255, 99, 132, 1);
        border: 1px solid rgba(255, 99, 132, 0.3);
    }
    
    body {
        background-color: #f8f9fc;
        background-image: 
            radial-gradient(at 47% 33%, rgba(72, 149, 239, 0.1) 0, transparent 59%), 
            radial-gradient(at 82% 65%, rgba(72, 149, 239, 0.15) 0, transparent 55%);
    }
    
    .table-glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .table-glass thead {
        background: rgba(72, 149, 239, 0.1);
    }
    
    .table-glass thead th {
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .table-glass tbody tr:hover {
        background: rgba(255, 255, 255, 0.15);
    }
    
    .chart-container {
        height: 400px;
        position: relative;
    }
    
    .viz-section {
        min-height: 500px;
    }
    
    /* Specific styling for the network graph */
    #networkGraph {
        width: 100%;
        height: 400px;
        background: rgba(255, 255, 255, 0.05);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .tooltip {
        position: absolute;
        padding: 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        border-radius: 5px;
        pointer-events: none;
        z-index: 1000;
    }
    
    .nav-tabs .nav-link {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: #495057;
    }
    
    .nav-tabs .nav-link.active {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.3);
        color: #495057;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="glass-card">
                <h2 class="text-center">Numbering Resource Audit Dashboard</h2>
                <div class="header-underline mx-auto"></div>
                <p class="text-center text-muted">Visualize and analyze telecom operator numbering resource utilization</p>
            </div>
        </div>
    </div>
    
    <!-- Filters Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="glass-card">
                <form method="GET" action="{{ url_for('audit.dashboard') }}" class="row align-items-center">
                    <div class="col-md-3 mb-2">
                        <label for="operator" class="filter-label">Operator</label>
                        <select class="form-select" id="operator" name="operator">
                            <option value="">All Operators</option>
                            {% for op in operators %}
                            <option value="{{ op }}" {% if selected_operator == op %}selected{% endif %}>{{ op }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="year" class="filter-label">Year</label>
                        <select class="form-select" id="year" name="year">
                            <option value="">All Years</option>
                            {% for yr in years %}
                            <option value="{{ yr }}" {% if selected_year == yr|string %}selected{% endif %}>{{ yr }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <label for="quarter" class="filter-label">Quarter</label>
                        <select class="form-select" id="quarter" name="quarter">
                            <option value="">All Quarters</option>
                            {% for q in quarters %}
                            <option value="{{ q }}" {% if selected_quarter == q %}selected{% endif %}>{{ q }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3 mb-2">
                        <button type="submit" class="btn btn-glass w-100">
                            <i class="fas fa-filter me-2"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Stats Summary Cards -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <i class="fas fa-phone-alt stat-icon"></i>
                <h3 class="stat-value">{{ summary.total_numbers|default(0)|format_number }}</h3>
                <p class="stat-label">Total Numbers</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <i class="fas fa-chart-pie stat-icon"></i>
                <h3 class="stat-value">{{ summary.avg_utilization|default(0) }}%</h3>
                <p class="stat-label">Average Utilization</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <i class="fas fa-power-off stat-icon"></i>
                <h3 class="stat-value">{{ summary.avg_inactive|default(0) }}%</h3>
                <p class="stat-label">Average Inactive</p>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <i class="fas fa-exchange-alt stat-icon"></i>
                <h3 class="stat-value">{{ summary.total_porting_balance|default(0)|format_number }}</h3>
                <p class="stat-label">Porting Balance</p>
            </div>
        </div>
    </div>
    
    <!-- Main Content Tabs -->
    <ul class="nav nav-tabs mb-4" id="dashboardTabs" role="tablist">
        <li class="nav-item" role="presentation">
            <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                Overview
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="utilization-tab" data-bs-toggle="tab" data-bs-target="#utilization" type="button" role="tab" aria-controls="utilization" aria-selected="false">
                Utilization Analysis
            </button>
        </li>
        <li class="nav-item" role="presentation">
            <button class="nav-link" id="porting-tab" data-bs-toggle="tab" data-bs-target="#porting" type="button" role="tab" aria-controls="porting" aria-selected="false">
                Porting Network
            </button>
        </li>
    </ul>
    
    <div class="tab-content" id="dashboardTabContent">
        <!-- Overview Tab -->
        <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="glass-card">
                        <h4 class="section-title">Audit Overview</h4>
                        
                        {% if audits %}
                        <div class="table-responsive">
                            <table class="table table-glass">
                                <thead>
                                    <tr>
                                        <th>Operator</th>
                                        <th>Period</th>
                                        <th>NDC</th>
                                        <th>Utilization</th>
                                        <th>Inactive %</th>
                                        <th>Porting Balance</th>
                                        <th>Overall Score</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for audit in audits %}
                                    <tr>
                                        <td>{{ audit.operator }}</td>
                                        <td>{{ audit.quarter }} {{ audit.year }}</td>
                                        <td>{{ audit.ndc }}</td>
                                        <td>
                                            {% set utilization = (audit.processed.utilization_rate * 100)|round(2) %}
                                            {% if utilization > 80 %}
                                            <span class="badge-glass good">{{ utilization }}%</span>
                                            {% elif utilization > 50 %}
                                            <span class="badge-glass warning">{{ utilization }}%</span>
                                            {% else %}
                                            <span class="badge-glass critical">{{ utilization }}%</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ audit.processed.percent_inactive|round(2) }}%</td>
                                        <td>{{ audit.processed.porting_balance }}</td>
                                        <td>
                                            {% set score = audit.performance_scores.overall|default(0) %}
                                            {% if score > 7 %}
                                            <span class="badge-glass good">{{ score }}/10</span>
                                            {% elif score > 5 %}
                                            <span class="badge-glass warning">{{ score }}/10</span>
                                            {% else %}
                                            <span class="badge-glass critical">{{ score }}/10</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary view-details" data-id="{{ audit._id }}">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i> No audit data found for the selected filters.
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="glass-card chart-container">
                        <h4 class="section-title">Utilization by Operator</h4>
                        <canvas id="utilizationChart"></canvas>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="glass-card chart-container">
                        <h4 class="section-title">Number Distribution by NDC</h4>
                        <canvas id="ndcDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Utilization Analysis Tab -->
        <div class="tab-pane fade" id="utilization" role="tabpanel" aria-labelledby="utilization-tab">
            <div class="row">
                <div class="col-md-8 mb-4">
                    <div class="glass-card chart-container">
                        <h4 class="section-title">Utilization Trends</h4>
                        <canvas id="utilizationTrendChart"></canvas>
                    </div>
                </div>
                <div class="col-md-4 mb-4">
                    <div class="glass-card chart-container">
                        <h4 class="section-title">Utilization vs. Inactive</h4>
                        <canvas id="utilizationVsInactiveChart"></canvas>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="glass-card">
                        <h4 class="section-title">Number Allocation Breakdown</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="allocationPieChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="table-responsive">
                                    <table class="table table-glass">
                                        <thead>
                                            <tr>
                                                <th>Category</th>
                                                <th>Count</th>
                                                <th>Percentage</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if audits %}
                                            {% set total_tan = summary.total_numbers|default(1) %}
                                            {% set total_tasn = audits|sum(attribute='tasn')|default(0) %}
                                            {% set total_tin = audits|sum(attribute='tin')|default(0) %}
                                            {% set total_trn = audits|sum(attribute='trn')|default(0) %}
                                            {% set total_other = total_tan - total_tasn - total_tin - total_trn %}
                                            
                                            <tr>
                                                <td>Assigned Numbers</td>
                                                <td>{{ total_tasn|format_number }}</td>
                                                <td>{{ ((total_tasn / total_tan) * 100)|round(2) }}%</td>
                                            </tr>
                                            <tr>
                                                <td>Inactive Numbers</td>
                                                <td>{{ total_tin|format_number }}</td>
                                                <td>{{ ((total_tin / total_tan) * 100)|round(2) }}%</td>
                                            </tr>
                                            <tr>
                                                <td>Reserved Numbers</td>
                                                <td>{{ total_trn|format_number }}</td>
                                                <td>{{ ((total_trn / total_tan) * 100)|round(2) }}%</td>
                                            </tr>
                                            <tr>
                                                <td>Other Numbers</td>
                                                <td>{{ total_other|format_number }}</td>
                                                <td>{{ ((total_other / total_tan) * 100)|round(2) }}%</td>
                                            </tr>
                                            {% else %}
                                            <tr>
                                                <td colspan="3" class="text-center">No data available</td>
                                            </tr>
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- SNR Statistics Section -->
            <div class="row">
                <div class="col-md-12 mb-4">
                    <div class="glass-card">
                        <h4 class="section-title">Special Numbering Resource (SNR) Statistics</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="snrDigitChart"></canvas>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="chart-container">
                                    <canvas id="snrServiceChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Porting Network Tab -->
        <div class="tab-pane fade" id="porting" role="tabpanel" aria-labelledby="porting-tab">
            <div class="row viz-section">
                <div class="col-md-12 mb-4">
                    <div class="glass-card">
                        <h4 class="section-title">Porting Network Analysis</h4>
                        <p class="text-muted mb-4">This force-directed graph visualizes the porting relationships between operators. Node size represents number allocation, and link thickness represents porting volume.</p>
                        
                        <div id="networkGraph"></div>
                        
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="glass-card">
                                        <h5 class="mb-3">Porting Legend</h5>
                                        <div class="d-flex align-items-center mb-2">
                                            <div style="width: 20px; height: 2px; background-color: rgba(72, 149, 239, 0.2);"></div>
                                            <span class="ms-2">Low Porting Volume</span>
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <div style="width: 20px; height: 5px; background-color: rgba(72, 149, 239, 0.5);"></div>
                                            <span class="ms-2">Medium Porting Volume</span>
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <div style="width: 20px; height: 8px; background-color: rgba(72, 149, 239, 0.8);"></div>
                                            <span class="ms-2">High Porting Volume</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-8">
                                    <div class="glass-card">
                                        <h5 class="mb-3">Top Porting Relationships</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>From Operator</th>
                                                        <th>To Operator</th>
                                                        <th>Porting Volume</th>
                                                        <th>Net Balance</th>
                                                    </tr>
                                                </thead>
                                                <tbody id="portingTable">
                                                    <!-- Will be populated via JavaScript -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Audit Details Modal -->
<div class="modal fade" id="auditDetailsModal" tabindex="-1" aria-labelledby="auditDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
            <div class="modal-header">
                <h5 class="modal-title" id="auditDetailsModalLabel">Audit Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="auditDetailsContent">
                <!-- Will be populated dynamically -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/d3@7"></script>
<script>
    // Initialize charts on page load
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
        initializeNetworkGraph();
        setupEventListeners();
    });
    
    // Chart data preparation
    const auditData = {{ audits|tojson|safe }};
    const portingData = {{ porting_data|safe }};
    
    function setupEventListeners() {
        // Set up event listeners for the "View Details" buttons
        document.querySelectorAll('.view-details').forEach(button => {
            button.addEventListener('click', function() {
                const auditId = this.getAttribute('data-id');
                fetchAuditDetails(auditId);
            });
        });
    }
    
    function fetchAuditDetails(auditId) {
        // Find the audit object in our existing data
        const audit = auditData.find(a => a._id === auditId);
        
        if (audit) {
            // Generate the modal content
            const content = `
                <div class="container-fluid">
                    <div class="row mb-3">
                        <div class="col-12">
                            <h4>${audit.operator} - ${audit.quarter} ${audit.year}</h4>
                            <p><strong>NDC:</strong> ${audit.ndc} <strong>Type:</strong> ${audit.sn_type}</p>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <h5>Number Range</h5>
                            <p><strong>Start Block:</strong> ${audit.start_block}</p>
                            <p><strong>End Block:</strong> ${audit.end_block}</p>
                            <p><strong>Total Allocated:</strong> ${audit.tan.toLocaleString()}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Performance Metrics</h5>
                            <p><strong>Utilization Rate:</strong> ${(audit.processed.utilization_rate * 100).toFixed(2)}%</p>
                            <p><strong>Inactive Rate:</strong> ${audit.processed.percent_inactive.toFixed(2)}%</p>
                            <p><strong>Reserved Rate:</strong> ${audit.processed.percent_reserved.toFixed(2)}%</p>
                            <p><strong>Porting Balance:</strong> ${audit.processed.porting_balance}</p>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h5>Allocation Breakdown</h5>
                            <p><strong>Assigned Numbers:</strong> ${audit.tasn.toLocaleString()}</p>
                            <p><strong>Inactive Numbers:</strong> ${audit.tin.toLocaleString()}</p>
                            <p><strong>Reserved Numbers:</strong> ${audit.trn.toLocaleString()}</p>
                        </div>
                        <div class="col-md-6">
                            <h5>Subscriber Activity</h5>
                            <p><strong>Gross Additions:</strong> ${audit.gross_add.toLocaleString()}</p>
                            <p><strong>Net Additions:</strong> ${audit.net_add.toLocaleString()}</p>
                            <p><strong>Cumulative Port-In:</strong> ${audit.cpb.toLocaleString()}</p>
                            <p><strong>Cumulative Port-Out:</strong> ${audit.cpo.toLocaleString()}</p>
                        </div>
                    </div>
                    
                    ${audit.snr_details ? `
                    <div class="row mt-3">
                        <div class="col-12">
                            <h5>SNR Details</h5>
                            <p><strong>Total SNRs:</strong> ${audit.snr_details.total_snrs}</p>
                            <p><strong>Service Categories:</strong> ${audit.snr_details.service_categories.join(', ')}</p>
                            <p><strong>3-Digit Shortcodes:</strong> ${audit.snr_details.digit_categories['3_digit']}</p>
                            <p><strong>4-Digit Shortcodes:</strong> ${audit.snr_details.digit_categories['4_digit']}</p>
                            <p><strong>5-Digit Shortcodes:</strong> ${audit.snr_details.digit_categories['5_digit']}</p>
                            <p><strong>6-Digit Shortcodes:</strong> ${audit.snr_details.digit_categories['6_digit']}</p>
                        </div>
                    </div>
                    ` : ''}
                </div>
            `;
            
            // Update the modal content
            document.getElementById('auditDetailsContent').innerHTML = content;
            
            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('auditDetailsModal'));
            modal.show();
        }
    }
    
    function initializeCharts() {
        if (auditData.length === 0) return;
        
        // Extract and prepare data for charts
        const operators = [...new Set(auditData.map(a => a.operator))];
        const utilizationByOperator = operators.map(op => {
            const operatorAudits = auditData.filter(a => a.operator === op);
            return {
                operator: op,
                utilization: operatorAudits.reduce((sum, audit) => sum + audit.processed.utilization_rate, 0) / operatorAudits.length * 100
            };
        });
        
        // Sort by utilization rate
        utilizationByOperator.sort((a, b) => b.utilization - a.utilization);
        
        // Group by NDC
        const ndcGroups = {};
        auditData.forEach(audit => {
            if (!ndcGroups[audit.ndc]) {
                ndcGroups[audit.ndc] = 0;
            }
            ndcGroups[audit.ndc] += audit.tan;
        });
        
        // Sort NDCs by total allocation
        const sortedNdcs = Object.keys(ndcGroups).sort((a, b) => ndcGroups[b] - ndcGroups[a]);
        
        // Chart 1: Utilization by Operator
        new Chart(document.getElementById('utilizationChart'), {
            type: 'bar',
            data: {
                labels: utilizationByOperator.map(d => d.operator),
                datasets: [{
                    label: 'Utilization Rate (%)',
                    data: utilizationByOperator.map(d => d.utilization.toFixed(2)),
                    backgroundColor: 'rgba(72, 149, 239, 0.6)',
                    borderColor: 'rgba(72, 149, 239, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.7)',
                        callbacks: {
                            label: function(context) {
                                return `Utilization: ${context.raw}%`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Utilization Rate (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Operator'
                        }
                    }
                }
            }
        });
        
        // Chart 2: Number Distribution by NDC
        new Chart(document.getElementById('ndcDistributionChart'), {
            type: 'pie',
            data: {
                labels: sortedNdcs,
                datasets: [{
                    data: sortedNdcs.map(ndc => ndcGroups[ndc]),
                    backgroundColor: [
                        'rgba(72, 149, 239, 0.6)',
                        'rgba(101, 116, 205, 0.6)',
                        'rgba(149, 76, 233, 0.6)',
                        'rgba(246, 109, 155, 0.6)',
                        'rgba(255, 159, 64, 0.6)',
                        'rgba(255, 205, 86, 0.6)',
                        'rgba(75, 192, 192, 0.6)',
                        'rgba(54, 162, 235, 0.6)',
                        'rgba(153, 102, 255, 0.6)',
                        'rgba(201, 203, 207, 0.6)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = (value / Object.values(ndcGroups).reduce((a, b) => a + b, 0) * 100).toFixed(2);
                                return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        // Chart 3: Utilization Trend (Mock data for demonstration)
        const quarters = ['Q1 2023', 'Q2 2023', 'Q3 2023', 'Q4 2023', 'Q1 2024', 'Q2 2024'];
        const trendDatasets = operators.map((op, index) => {
            // Generate mock trend data
            const trendData = quarters.map(() => Math.random() * 30 + 50); // Random values between 50-80%
            
            return {
                label: op,
                data: trendData,
                borderColor: getColorFromIndex(index),
                backgroundColor: 'transparent',
                tension: 0.4
            };
        });
        
        new Chart(document.getElementById('utilizationTrendChart'), {
            type: 'line',
            data: {
                labels: quarters,
                datasets: trendDatasets
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Utilization Rate (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Quarter'
                        }
                    }
                }
            }
        });
        
        // Chart 4: Utilization vs Inactive scatter plot
        const scatterData = auditData.map(audit => ({
            x: audit.processed.utilization_rate * 100,
            y: audit.processed.percent_inactive,
            r: Math.sqrt(audit.tan) / 50, // Size based on number allocation
            operator: audit.operator,
            ndc: audit.ndc
        }));
        
        new Chart(document.getElementById('utilizationVsInactiveChart'), {
            type: 'bubble',
            data: {
                datasets: [{
                    label: 'Operators',
                    data: scatterData,
                    backgroundColor: 'rgba(72, 149, 239, 0.5)',
                    borderColor: 'rgba(72, 149, 239, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const point = context.raw;
                                return [
                                    `Operator: ${point.operator}`,
                                    `NDC: ${point.ndc}`,
                                    `Utilization: ${point.x.toFixed(2)}%`,
                                    `Inactive: ${point.y.toFixed(2)}%`
                                ];
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: 'Utilization Rate (%)'
                        },
                        min: 0,
                        max: 100
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Inactive Rate (%)'
                        },
                        min: 0,
                        max: 100
                    }
                }
            }
        });
        
        // Chart 5: Number Allocation Pie Chart
        // Calculate totals
        const totalTan = auditData.reduce((sum, audit) => sum + audit.tan, 0);
        const totalTasn = auditData.reduce((sum, audit) => sum + audit.tasn, 0);
        const totalTin = auditData.reduce((sum, audit) => sum + audit.tin, 0);
        const totalTrn = auditData.reduce((sum, audit) => sum + audit.trn, 0);
        const totalOther = totalTan - totalTasn - totalTin - totalTrn;
        
        new Chart(document.getElementById('allocationPieChart'), {
            type: 'pie',
            data: {
                labels: ['Assigned', 'Inactive', 'Reserved', 'Other'],
                datasets: [{
                    data: [totalTasn, totalTin, totalTrn, totalOther > 0 ? totalOther : 0],
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(255, 205, 86, 0.7)',
                        'rgba(201, 203, 207, 0.7)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const value = context.raw;
                                const percentage = (value / totalTan * 100).toFixed(2);
                                return `${context.label}: ${value.toLocaleString()} (${percentage}%)`;
                            }
                        }
                    }
                }
            }
        });
        
        // SNR Charts (using mock data if not available)
        const snrData = auditData.filter(a => a.snr_details).map(a => a.snr_details);
        
        // If we have SNR data
        if (snrData.length > 0) {
            // Chart 6: SNR Digit Distribution
            const digitCounts = {
                '3-Digit': snrData.reduce((sum, snr) => sum + (snr.digit_categories['3_digit'] || 0), 0),
                '4-Digit': snrData.reduce((sum, snr) => sum + (snr.digit_categories['4_digit'] || 0), 0),
                '5-Digit': snrData.reduce((sum, snr) => sum + (snr.digit_categories['5_digit'] || 0), 0),
                '6-Digit': snrData.reduce((sum, snr) => sum + (snr.digit_categories['6_digit'] || 0), 0)
            };
            
            new Chart(document.getElementById('snrDigitChart'), {
                type: 'bar',
                data: {
                    labels: Object.keys(digitCounts),
                    datasets: [{
                        label: 'Count',
                        data: Object.values(digitCounts),
                        backgroundColor: 'rgba(153, 102, 255, 0.6)',
                        borderColor: 'rgba(153, 102, 255, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    indexAxis: 'y',
                    plugins: {
                        title: {
                            display: true,
                            text: 'Shortcode Distribution by Length'
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Number of Shortcodes'
                            }
                        }
                    }
                }
            });
            
            // Chart 7: SNR Service Category Distribution (Need to flatten service categories)
            const allCategories = snrData.flatMap(snr => snr.service_categories || []);
            const categoryCount = {};
            allCategories.forEach(cat => {
                categoryCount[cat] = (categoryCount[cat] || 0) + 1;
            });
            
            new Chart(document.getElementById('snrServiceChart'), {
                type: 'doughnut',
                data: {
                    labels: Object.keys(categoryCount),
                    datasets: [{
                        data: Object.values(categoryCount),
                        backgroundColor: [
                            'rgba(75, 192, 192, 0.7)',
                            'rgba(255, 99, 132, 0.7)',
                            'rgba(255, 205, 86, 0.7)',
                            'rgba(54, 162, 235, 0.7)',
                            'rgba(153, 102, 255, 0.7)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: 'SNR Service Categories'
                        }
                    }
                }
            });
        } else {
            // If no SNR data, display a message in the charts
            document.getElementById('snrDigitChart').parentNode.innerHTML = '<div class="alert alert-info h-100 d-flex align-items-center justify-content-center">No SNR data available for the selected filters</div>';
            document.getElementById('snrServiceChart').parentNode.innerHTML = '<div class="alert alert-info h-100 d-flex align-items-center justify-content-center">No SNR data available for the selected filters</div>';
        }
    }
    
    function initializeNetworkGraph() {
        // Check if we have porting data
        if (!portingData || !portingData.nodes || portingData.nodes.length === 0) {
            document.getElementById('networkGraph').innerHTML = '<div class="alert alert-info text-center p-5">No porting network data available for the selected filters</div>';
            return;
        }
        
        // Set up dimensions
        const width = document.getElementById('networkGraph').clientWidth;
        const height = 400;
        
        // Create a force-directed graph
        const svg = d3.select('#networkGraph')
            .append('svg')
            .attr('width', width)
            .attr('height', height);
        
        // Create a tooltip
        const tooltip = d3.select('body')
            .append('div')
            .attr('class', 'tooltip')
            .style('opacity', 0);
            
        // Set up the simulation
        const simulation = d3.forceSimulation(portingData.nodes)
            .force('link', d3.forceLink(portingData.links).id(d => d.id).distance(150))
            .force('charge', d3.forceManyBody().strength(-400))
            .force('center', d3.forceCenter(width / 2, height / 2))
            .force('collision', d3.forceCollide().radius(d => d.size * 2));
        
        // Create links with arrows
        const link = svg.append('g')
            .selectAll('path')
            .data(portingData.links)
            .enter()
            .append('path')
            .attr('stroke', 'rgba(72, 149, 239, 0.6)')
            .attr('stroke-width', d => d.width)
            .attr('fill', 'none')
            .attr('marker-end', 'url(#arrow)');
        
        // Define arrow marker
        svg.append('defs').append('marker')
            .attr('id', 'arrow')
            .attr('viewBox', '0 -5 10 10')
            .attr('refX', 30)
            .attr('refY', 0)
            .attr('markerWidth', 6)
            .attr('markerHeight', 6)
            .attr('orient', 'auto')
            .append('path')
            .attr('d', 'M0,-5L10,0L0,5')
            .attr('fill', 'rgba(72, 149, 239, 0.8)');
        
        // Create nodes
        const node = svg.append('g')
            .selectAll('circle')
            .data(portingData.nodes)
            .enter()
            .append('circle')
            .attr('r', d => d.size)
            .attr('fill', 'rgba(72, 149, 239, 0.6)')
            .attr('stroke', 'white')
            .attr('stroke-width', 1.5)
            .call(d3.drag()
                .on('start', dragstarted)
                .on('drag', dragged)
                .on('end', dragended));
        
        // Add node labels
        const nodeLabel = svg.append('g')
            .selectAll('text')
            .data(portingData.nodes)
            .enter()
            .append('text')
            .attr('dx', d => d.size + 5)
            .attr('dy', '.35em')
            .text(d => d.id)
            .style('font-size', '12px')
            .style('fill', '#333');
        
        // Add interactivity
        node.on('mouseover', function(event, d) {
                tooltip.transition()
                    .duration(200)
                    .style('opacity', .9);
                tooltip.html(`<strong>${d.id}</strong><br>Numbers: ${(d.size * 10000).toLocaleString()}`)
                    .style('left', (event.pageX + 10) + 'px')
                    .style('top', (event.pageY - 28) + 'px');
                
                // Highlight connected links and nodes
                link.style('stroke-opacity', o => (o.source === d || o.target === d) ? 1 : 0.1);
                node.style('opacity', o => (o === d || isConnected(d, o)) ? 1 : 0.3);
                nodeLabel.style('opacity', o => (o === d || isConnected(d, o)) ? 1 : 0.3);
            })
            .on('mouseout', function() {
                tooltip.transition()
                    .duration(500)
                    .style('opacity', 0);
                
                // Reset styles
                link.style('stroke-opacity', 1);
                node.style('opacity', 1);
                nodeLabel.style('opacity', 1);
            });
        
        // Check if two nodes are connected
        function isConnected(a, b) {
            return portingData.links.some(link => 
                (link.source === a && link.target === b) || 
                (link.source === b && link.target === a)
            );
        }
        
        // Update positions on tick
        simulation.on('tick', () => {
            // Update link paths
            link.attr('d', d => {
                // Create a curved path between nodes
                const dx = d.target.x - d.source.x,
                      dy = d.target.y - d.source.y,
                      dr = Math.sqrt(dx * dx + dy * dy) * 1.5;
                return `M${d.source.x},${d.source.y}A${dr},${dr} 0 0,1 ${d.target.x},${d.target.y}`;
            });
            
            // Update node positions
            node.attr('cx', d => d.x = Math.max(d.size, Math.min(width - d.size, d.x)))
                .attr('cy', d => d.y = Math.max(d.size, Math.min(height - d.size, d.y)));
            
            // Update label positions
            nodeLabel.attr('x', d => d.x)
                .attr('y', d => d.y);
        });
        
        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }
        
        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }
        
        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }
        
        // Populate the porting relationships table
        populatePortingTable();
    }
    
    function populatePortingTable() {
        // Check if we have links in the porting data
        if (!portingData || !portingData.links || portingData.links.length === 0) {
            document.getElementById('portingTable').innerHTML = '<tr><td colspan="4" class="text-center">No porting data available</td></tr>';
            return;
        }
        
        // Sort links by value (porting volume)
        const sortedLinks = [...portingData.links].sort((a, b) => b.value - a.value);
        
        // Take top 5 links
        const topLinks = sortedLinks.slice(0, 5);
        
        // Calculate net balance between operators
        const netBalanceMap = {};
        
        portingData.links.forEach(link => {
            const fromTo = `${link.source}-${link.target}`;
            const toFrom = `${link.target}-${link.source}`;
            
            if (!netBalanceMap[fromTo]) {
                netBalanceMap[fromTo] = 0;
            }
            
            netBalanceMap[fromTo] += link.value;
            
            if (netBalanceMap[toFrom]) {
                netBalanceMap[fromTo] -= netBalanceMap[toFrom];
                delete netBalanceMap[toFrom];
            }
        });
        
        // Generate table rows
        let tableHtml = '';
        topLinks.forEach(link => {
            const source = typeof link.source === 'object' ? link.source.id : link.source;
            const target = typeof link.target === 'object' ? link.target.id : link.target;
            const fromTo = `${source}-${target}`;
            const netBalance = netBalanceMap[fromTo] || 0;
            
            tableHtml += `
                <tr>
                    <td>${source}</td>
                    <td>${target}</td>
                    <td>${link.value.toLocaleString()}</td>
                    <td>${netBalance > 0 ? '+' : ''}${netBalance.toLocaleString()}</td>
                </tr>
            `;
        });
        
        document.getElementById('portingTable').innerHTML = tableHtml || '<tr><td colspan="4" class="text-center">No porting data available</td></tr>';
    }
    
    // Helper function to get a color based on index
    function getColorFromIndex(index) {
        const colors = [
            'rgba(72, 149, 239, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)'
        ];
        return colors[index % colors.length];
    }
</script>
{% endblock %}