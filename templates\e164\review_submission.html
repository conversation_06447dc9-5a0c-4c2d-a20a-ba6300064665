{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="header text-center mb-4 animated">
        <h1><i class="fas fa-clipboard-check me-2"></i> Review E.164 Submission</h1>
        <p class="text-muted">National Communications Authority - {{ submission.operator }} ({{ submission.reporting_period }})</p>
    </div>

    <!-- Submission Info Card -->
    <div class="glass-card form-section">
        <div class="d-flex justify-content-between align-items-start">
            <h3 class="mb-4">Submission Overview</h3>
            <div class="badge bg-warning text-dark p-2">Status: {{ submission.status|capitalize }}</div>
        </div>
        
        <div class="row">
            <div class="col-md-6 mb-3">
                <p><strong>Operator:</strong> {{ submission.operator }}</p>
                <p><strong>Reporting Period:</strong> {{ submission.reporting_period }}</p>
                <p><strong>Submitted:</strong> {{ submission.created_at }}</p>
                <p><strong>Contact Person:</strong> {{ submission.contact_person }}</p>
                <p><strong>Contact Email:</strong> {{ submission.contact_email }}</p>
            </div>
            <div class="col-md-6 mb-3">
                <div class="glass-card metric-card h-100">
                    <h5>Utilization Summary</h5>
                    <div class="row text-center mt-3">
                        <div class="col-6 mb-3">
                            <div class="metric-value">{{ metrics.total_allocated|default(0)|int|format_number }}</div>
                            <div class="metric-label">Total Allocated</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="metric-value">{{ metrics.total_active|default(0)|int|format_number }}</div>
                            <div class="metric-label">Total Active</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="metric-value">{{ metrics.utilization_rate|default(0)|round(2) }}%</div>
                            <div class="metric-label">Utilization Rate</div>
                        </div>
                        <div class="col-6 mb-3">
                            <div class="metric-value">{{ ranges|length }}</div>
                            <div class="metric-label">Number Ranges</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if submission.notes %}
        <div class="mt-3">
            <strong>Additional Notes:</strong>
            <p>{{ submission.notes }}</p>
        </div>
        {% endif %}
    </div>

    <!-- Number Ranges Table -->
    <div class="glass-card form-section mt-4" style="animation-delay: 0.2s;">
        <h3 class="mb-4">Number Range Data</h3>
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>NDC</th>
                        <th>Type</th>
                        <th>Starting Block</th>
                        <th>Ending Block</th>
                        <th>Total Allocated</th>
                        <th>Active Subscriber</th>
                        <th>Active Non-Subscriber</th>
                        <th>Inactive</th>
                        <th>Utilization</th>
                    </tr>
                </thead>
                <tbody>
                    {% for range in ranges %}
                    <tr>
                        <td>{{ range.ndc }}</td>
                        <td>{{ range.type }}</td>
                        <td>{{ range.start_block }}</td>
                        <td>{{ range.end_block }}</td>
                        <td>{{ range.total_allocated|int|format_number }}</td>
                        <td>{{ range.active_subscriber|int|format_number }}</td>
                        <td>{{ range.active_non_subscriber|default(0)|int|format_number }}</td>
                        <td>{{ range.inactive|int|format_number }}</td>
                        <td>{{ range.utilization_rate|default(0)|round(2) }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- Visualized Metrics (conditionally shown) -->
    <div class="glass-card form-section mt-4" style="animation-delay: 0.4s;">
        <h3 class="mb-4">Submission Metrics Analysis</h3>
        
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="chart-container" id="utilization-chart-container">
                    <canvas id="utilization-chart"></canvas>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="chart-container" id="status-distribution-chart-container">
                    <canvas id="status-distribution-chart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Review Actions -->
    <div class="glass-card form-section mt-4" style="animation-delay: 0.6s;">
        <h3 class="mb-4">Review Actions</h3>
        
        <div class="mb-3">
            <label for="review-comments" class="form-label">Admin Comments</label>
            <textarea class="form-control" id="review-comments" rows="3" placeholder="Optional comments or feedback"></textarea>
        </div>
        
        <div class="d-flex justify-content-end gap-3 mt-4">
            <button class="btn btn-outline-secondary" id="back-btn">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </button>
            <button class="btn btn-danger" id="reject-btn">
                <i class="fas fa-times-circle me-2"></i>Reject Submission
            </button>
            <button class="btn btn-success" id="approve-btn">
                <i class="fas fa-check-circle me-2"></i>Approve Submission
            </button>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="confirmModalTitle">Confirm Action</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="confirmModalBody">
                Are you sure you want to proceed with this action?
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="confirmModalBtn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.0/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize visualization
        renderUtilizationChart();
        renderStatusDistributionChart();
        
        // Back button handler
        document.getElementById('back-btn').addEventListener('click', function() {
            window.location.href = "{{ url_for('e164.review_pending_submissions') }}";
        });

        // Approve button handler
        document.getElementById('approve-btn').addEventListener('click', function() {
            showConfirmModal('Approve Submission', 'Are you sure you want to approve this submission? This will update all analytics data.', 'approve');
        });

        // Reject button handler
        document.getElementById('reject-btn').addEventListener('click', function() {
            showConfirmModal('Reject Submission', 'Are you sure you want to reject this submission? Please provide feedback in the comments.', 'reject');
        });

        // Confirm modal action
        document.getElementById('confirmModalBtn').addEventListener('click', function() {
            const action = this.dataset.action;
            const comments = document.getElementById('review-comments').value;
            
            if (action === 'reject' && !comments) {
                alert('Please provide comments explaining the rejection.');
                return;
            }
            
            performAction(action, comments);
        });
    });

    function showConfirmModal(title, message, action) {
        const modal = document.getElementById('confirmModal');
        document.getElementById('confirmModalTitle').textContent = title;
        document.getElementById('confirmModalBody').textContent = message;
        document.getElementById('confirmModalBtn').dataset.action = action;
        
        const modalInstance = new bootstrap.Modal(modal);
        modalInstance.show();
    }

    function performAction(action, comments) {
        const url = action === 'approve' 
            ? "{{ url_for('e164.approve_submission', submission_id=submission._id) }}"
            : "{{ url_for('e164.reject_submission', submission_id=submission._id) }}";
        
        fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ comments: comments })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(action === 'approve' ? 'Submission approved successfully!' : 'Submission rejected successfully!');
                window.location.href = data.redirect_url;
            } else {
                alert('Error: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred. Please try again.');
        });
    }

    function renderUtilizationChart() {
        const ctx = document.getElementById('utilization-chart').getContext('2d');
        
        const ndcs = [];
        const utilizationRates = [];
        
        {% for range in ranges %}
        ndcs.push("{{ range.ndc }}");
        utilizationRates.push({{ range.utilization_rate|default(0)|round(2) }});
        {% endfor %}
        
        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: ndcs,
                datasets: [{
                    label: 'Utilization Rate (%)',
                    data: utilizationRates,
                    backgroundColor: 'rgba(52, 152, 219, 0.7)',
                    borderColor: 'rgba(52, 152, 219, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        title: {
                            display: true,
                            text: 'Utilization Rate (%)'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Network Destination Code (NDC)'
                        }
                    }
                }
            }
        });
    }

    function renderStatusDistributionChart() {
        const ctx = document.getElementById('status-distribution-chart').getContext('2d');
        
        // Calculate totals
        let totalActive = {{ metrics.total_active|default(0) }};
        let totalInactive = {{ metrics.total_inactive|default(0) }};
        let totalReserved = {{ metrics.total_reserved|default(0) }};
        let totalAllocated = {{ metrics.total_allocated|default(0) }};
        
        // Calculate percentages
        const activePercentage = totalAllocated > 0 ? (totalActive / totalAllocated * 100).toFixed(1) : 0;
        const inactivePercentage = totalAllocated > 0 ? (totalInactive / totalAllocated * 100).toFixed(1) : 0;
        const reservedPercentage = totalAllocated > 0 ? (totalReserved / totalAllocated * 100).toFixed(1) : 0;
        
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Inactive', 'Reserved'],
                datasets: [{
                    data: [activePercentage, inactivePercentage, reservedPercentage],
                    backgroundColor: [
                        'rgba(26, 188, 156, 0.7)',
                        'rgba(231, 76, 60, 0.7)',
                        'rgba(241, 196, 15, 0.7)'
                    ],
                    borderColor: [
                        'rgba(26, 188, 156, 1)',
                        'rgba(231, 76, 60, 1)',
                        'rgba(241, 196, 15, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.raw}%`;
                            }
                        }
                    }
                }
            }
        });
    }
</script>

<style>
    :root {
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --accent-color: #1abc9c;
        --background-color: #ecf0f1;
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: 1px solid rgba(255, 255, 255, 0.18);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: var(--background-color);
        background-image: 
            radial-gradient(at 47% 33%, rgba(52, 152, 219, 0.25) 0, transparent 59%), 
            radial-gradient(at 82% 65%, rgba(26, 188, 156, 0.15) 0, transparent 55%);
        min-height: 100vh;
    }

    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        padding: 25px;
        margin-bottom: 25px;
    }

    .header {
        padding: 20px 0;
        color: var(--primary-color);
    }

    .tab-content {
        padding: 20px 0;
    }

    .nav-tabs {
        border-bottom: none;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 10px;
        margin-right: 5px;
        color: var(--primary-color);
        font-weight: 500;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .nav-tabs .nav-link.active {
        background: var(--glass-bg);
        border: var(--glass-border);
        color: var(--secondary-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.5);
        border-radius: 8px;
        padding: 12px 15px;
        backdrop-filter: blur(5px);
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.7);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        border-color: var(--secondary-color);
    }

    .table {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        overflow: hidden;
    }

    .btn-primary {
        background-color: var(--secondary-color);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
    }

    .btn-outline-secondary {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
    }

    .btn-outline-secondary:hover {
        background-color: var(--secondary-color);
        color: white;
    }

    .metric-card {
        padding: 20px;
        text-align: center;
        height: 100%;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--secondary-color);
    }

    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }

    .help-text {
        color: #7f8c8d;
        font-size: 0.85rem;
    }

    .locked-field {
        background-color: rgba(236, 240, 241, 0.7) !important;
        cursor: not-allowed;
    }

    .table th {
        font-weight: 600;
        color: var(--primary-color);
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(44, 62, 80, 0.2);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: rgba(44, 62, 80, 0.4);
    }

    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .animated {
        animation: fadeIn 0.5s ease forwards;
    }

    .form-section {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
        animation-delay: 0.2s;
    }
</style>
{% endblock %}
