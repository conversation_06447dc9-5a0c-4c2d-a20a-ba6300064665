/**
 * E.164 Number Range Audit Dashboard
 * Main CSS styles
 */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #1abc9c;
    --background-color: #ecf0f1;
    --glass-bg: rgba(255, 255, 255, 0.25);
    --glass-border: 1px solid rgba(255, 255, 255, 0.18);
    --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: var(--background-color);
    background-image: 
        radial-gradient(at 47% 33%, rgba(52, 152, 219, 0.25) 0, transparent 59%), 
        radial-gradient(at 82% 65%, rgba(26, 188, 156, 0.15) 0, transparent 55%);
    min-height: 100vh;
}

.glass-card {
    background: var(--glass-bg);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    border: var(--glass-border);
    box-shadow: var(--glass-shadow);
    padding: 25px;
    margin-bottom: 25px;
}

.header {
    padding: 20px 0;
    color: var(--primary-color);
}

.tab-content {
    padding: 20px 0;
}

.nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 10px;
    margin-right: 5px;
    color: var(--primary-color);
    font-weight: 500;
    padding: 12px 20px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(255, 255, 255, 0.3);
}

.nav-tabs .nav-link.active {
    background: var(--glass-bg);
    border: var(--glass-border);
    color: var(--secondary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.form-control, .form-select {
    background: rgba(255, 255, 255, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 8px;
    padding: 12px 15px;
    backdrop-filter: blur(5px);
    transition: all 0.3s;
}

.form-control:focus, .form-select:focus {
    background: rgba(255, 255, 255, 0.7);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    border-color: var(--secondary-color);
}

.table {
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    overflow: hidden;
}

.btn-primary {
    background-color: var(--secondary-color);
    border: none;
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
    transition: all 0.3s;
}

.btn-primary:hover {
    background-color: #2980b9;
    transform: translateY(-2px);
}

.btn-outline-secondary {
    border-color: var(--secondary-color);
    color: var(--secondary-color);
    border-radius: 8px;
    padding: 10px 20px;
    font-weight: 600;
}

.btn-outline-secondary:hover {
    background-color: var(--secondary-color);
    color: white;
}

.metric-card {
    padding: 20px;
    text-align: center;
    height: 100%;
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    color: var(--secondary-color);
}

.chart-container {
    height: 300px;
    margin-bottom: 20px;
}

.help-text {
    color: #7f8c8d;
    font-size: 0.85rem;
}

.locked-field {
    background-color: rgba(236, 240, 241, 0.7) !important;
    cursor: not-allowed;
}

.table th {
    font-weight: 600;
    color: var(--primary-color);
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb {
    background: rgba(44, 62, 80, 0.2);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(44, 62, 80, 0.4);
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.animated {
    animation: fadeIn 0.5s ease forwards;
}

.form-section {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
    animation-delay: 0.2s;
}


