{% extends "base.html" %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="header text-center mb-4 animated">
        <h1><i class="fas fa-clipboard-check me-2"></i> Pending E.164 Number Range Submissions</h1>
        <p class="text-muted">National Communications Authority - Admin Review Panel</p>
    </div>

    <!-- Overview Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="glass-card metric-card">
                <h5>Pending Submissions</h5>
                <div class="metric-value">{{ pending_submissions|length }}</div>
                <div class="metric-trend">
                    <i class="fas fa-clock text-warning"></i> Awaiting Review
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="glass-card metric-card">
                <h5>Total Ranges</h5>
                <div class="metric-value">{{ pending_submissions|sum(attribute='ranges_count') }}</div>
                <div class="metric-trend">
                    <i class="fas fa-hashtag text-info"></i> Number Ranges
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="glass-card metric-card">
                <h5>Oldest Submission</h5>
                <div class="metric-value">
                    {% if pending_submissions %}
                        {{ pending_submissions[-1].created_at|truncate(10, True, '') }}
                    {% else %}
                        N/A
                    {% endif %}
                </div>
                <div class="metric-trend">
                    <i class="fas fa-calendar-alt text-primary"></i> Days in Queue
                </div>
            </div>
        </div>
    </div>

    <!-- Submissions Table -->
    <div class="glass-card animated">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h3>Pending Submissions</h3>
            <div>
                <button class="btn btn-outline-secondary me-2" id="refresh-btn">
                    <i class="fas fa-sync-alt me-2"></i>Refresh
                </button>
            </div>
        </div>

        {% if pending_submissions %}
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Operator</th>
                            <th>Reporting Period</th>
                            <th>Submitted</th>
                            <th>Ranges</th>
                            <th>Utilization</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for submission in pending_submissions %}
                            <tr>
                                <td>{{ submission.operator }}</td>
                                <td>{{ submission.reporting_period }}</td>
                                <td>{{ submission.created_at }}</td>
                                <td>{{ submission.ranges_count }}</td>
                                <td>{{ submission.utilization_rate|default(0)|round(2) }}%</td>
                                <td>
                                    <a href="{{ url_for('e164.review_submission', submission_id=submission._id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye me-1"></i>Review
                                    </a>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No pending submissions found.
            </div>
        {% endif %}
    </div>

    <!-- Admin Navigation -->
    <div class="glass-card mt-4 animated">
        <h3 class="mb-4">Admin Actions</h3>
        <div class="d-flex flex-wrap gap-3">
            <a href="{{ url_for('e164.dashboard') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
            <a href="{{ url_for('e164.analytics') }}" class="btn btn-outline-primary">
                <i class="fas fa-chart-line me-2"></i>View Analytics
            </a>
        </div>
    </div>
</div>

<script>
    // Refresh button handler
    document.getElementById('refresh-btn').addEventListener('click', function() {
        location.reload();
    });
</script>

<style>
    :root {
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --accent-color: #1abc9c;
        --background-color: #ecf0f1;
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: 1px solid rgba(255, 255, 255, 0.18);
        --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background-color: var(--background-color);
        background-image: 
            radial-gradient(at 47% 33%, rgba(52, 152, 219, 0.25) 0, transparent 59%), 
            radial-gradient(at 82% 65%, rgba(26, 188, 156, 0.15) 0, transparent 55%);
        min-height: 100vh;
    }

    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: var(--glass-border);
        box-shadow: var(--glass-shadow);
        padding: 25px;
        margin-bottom: 25px;
    }

    .header {
        padding: 20px 0;
        color: var(--primary-color);
    }

    .tab-content {
        padding: 20px 0;
    }

    .nav-tabs {
        border-bottom: none;
    }

    .nav-tabs .nav-link {
        border: none;
        border-radius: 10px;
        margin-right: 5px;
        color: var(--primary-color);
        font-weight: 500;
        padding: 12px 20px;
        transition: all 0.3s ease;
    }

    .nav-tabs .nav-link:hover {
        background: rgba(255, 255, 255, 0.3);
    }

    .nav-tabs .nav-link.active {
        background: var(--glass-bg);
        border: var(--glass-border);
        color: var(--secondary-color);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .form-control, .form-select {
        background: rgba(255, 255, 255, 0.5);
        border: 1px solid rgba(255, 255, 255, 0.5);
        border-radius: 8px;
        padding: 12px 15px;
        backdrop-filter: blur(5px);
        transition: all 0.3s;
    }

    .form-control:focus, .form-select:focus {
        background: rgba(255, 255, 255, 0.7);
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        border-color: var(--secondary-color);
    }

    .table {
        background: rgba(255, 255, 255, 0.7);
        border-radius: 10px;
        overflow: hidden;
    }

    .btn-primary {
        background-color: var(--secondary-color);
        border: none;
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s;
    }

    .btn-primary:hover {
        background-color: #2980b9;
        transform: translateY(-2px);
    }

    .btn-outline-secondary {
        border-color: var(--secondary-color);
        color: var(--secondary-color);
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 600;
    }

    .btn-outline-secondary:hover {
        background-color: var(--secondary-color);
        color: white;
    }

    .metric-card {
        padding: 20px;
        text-align: center;
        height: 100%;
    }

    .metric-value {
        font-size: 2rem;
        font-weight: bold;
        color: var(--secondary-color);
    }

    .chart-container {
        height: 300px;
        margin-bottom: 20px;
    }

    .help-text {
        color: #7f8c8d;
        font-size: 0.85rem;
    }

    .locked-field {
        background-color: rgba(236, 240, 241, 0.7) !important;
        cursor: not-allowed;
    }

    .table th {
        font-weight: 600;
        color: var(--primary-color);
    }

    /* Custom scrollbar */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: rgba(255, 255, 255, 0.1);
    }

    ::-webkit-scrollbar-thumb {
        background: rgba(44, 62, 80, 0.2);
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: rgba(44, 62, 80, 0.4);
    }

    /* Animation */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .animated {
        animation: fadeIn 0.5s ease forwards;
    }

    .form-section {
        opacity: 0;
        animation: fadeIn 0.5s ease forwards;
        animation-delay: 0.2s;
    }
</style>
{% endblock %}
