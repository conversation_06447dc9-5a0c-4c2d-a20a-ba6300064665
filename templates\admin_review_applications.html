{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <h2>Review Applications</h2>
    <div id="floating-alert-container" style="position: fixed; top: 20px; right: 20px; width: 300px; z-index: 1050;"></div>

    <!-- Filters for application review -->
    <form method="POST" action="{{ url_for('review_applications') }}">
<!-- Replace the entire form with this filter section -->
    <div class="form-row">
        <div class="form-group col-md-3">
            <label for="statusFilter">Status</label>
            <select class="form-control" id="statusFilter">
                <option value="All">All</option>
                <option value="Pending Review">Pending Review</option>
                <option value="Approved">Approved</option>
                <option value="Rejected">Rejected</option>
            </select>
        </div>
        <div class="form-group col-md-3">
            <label for="dateFromFilter">Date From</label>
            <input type="date" class="form-control" id="dateFromFilter">
        </div>
        <div class="form-group col-md-3">
            <label for="dateToFilter">Date To</label>
            <input type="date" class="form-control" id="dateToFilter">
        </div>
    </div>
    </form>

    <!-- Applications Table -->
    <div class="mt-4">
        <h4>Applications ({{ total_count }})</h4>
        <table id="applicationsTable" class="table table-striped">
            <thead>
                <tr>
                    <th>Application ID</th>
                    <th>Applicant Name</th>
                    <th>Status</th>
                    <th>Application Date</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                {% for application in applications %}
                <tr id="application-row-{{ application.application_id }}">
                    <td>{{ application.application_id }}</td>
                    <td>{{ application.applicant_name }}</td>
                    <td>
                        <select id="status-{{ application.application_id }}" name="new_status" class="form-control form-control-sm d-inline-block w-auto">
                            <option value="Pending Review" {% if application.status == 'Pending Review' %}selected{% endif %}>Pending Review</option>
                            <option value="Approved" {% if application.status == 'Approved' %}selected{% endif %}>Approved</option>
                            <option value="Rejected" {% if application.status == 'Rejected' %}selected{% endif %}>Rejected</option>
                        </select>
                        <button type="button" onclick="changeStatus('{{ application.application_id }}')" class="btn btn-sm btn-success mt-1">Update</button>
                    </td>
                    <td>{{ application.application_date }}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-info details-button" 
                        data-application-id="{{ application.application_id }}" 
                        data-toggle="modal" data-target="#detailsModal">
                            View Details
                        </button>
                
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>


<!-- Generic Modal for Application Details -->
<div class="modal fade" id="detailsModal" tabindex="-1" role="dialog" aria-labelledby="detailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailsModalLabel">Application Details</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="modalContent">
                <!-- Dynamic content will be inserted here -->
            </div>
            <div class="modal-footer" id="modalFooter">
                <!-- Dynamic footer content will be inserted here -->
            </div>
        </div>
    </div>
</div>

</div>

<!-- Include jQuery first -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap4.min.js"></script>
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">


<script>
    $(document).ready(function() {
        const table = $('#applicationsTable').DataTable({
        "processing": true,
        "serverSide": true,
        "ajax": {
            "url": "{{ url_for('get_applications') }}",
            "data": function(d) {
                return {
                    "draw": d.draw,
                    "start": d.start,
                    "length": d.length,
                    "search[value]": d.search.value,
                    "status": $('#statusFilter').val(),
                    "date_from": $('#dateFromFilter').val(),
                    "date_to": $('#dateToFilter').val()
                }
            }
        },
            "columns": [
                { "data": 0 },
                { "data": 1 },
                { 
                    "data": 2,
                    "render": function(data, type, row) {
                        return `
                            <select class="status-select" data-id="${row[0]}">
                                <option ${data === 'Pending Review' ? 'selected' : ''}>Pending Review</option>
                                <option ${data === 'Approved' ? 'selected' : ''}>Approved</option>
                                <option ${data === 'Rejected' ? 'selected' : ''}>Rejected</option>
                            </select>
                            <button class="btn btn-sm btn-success update-status" data-id="${row[0]}">Update</button>
                        `;
                    }
                },
                { "data": 3 },
                { "data": 4 }
            ],
            "pageLength": 50,
            "lengthMenu": [10, 25, 50, 100],
            "searchDelay": 500
        });
    
        // Filter controls
        $('#statusFilter, #dateFromFilter, #dateToFilter').on('change', function() {
            table.ajax.reload();
        });
    
        // Update status handler
        $(document).on('click', '.update-status', function() {
            const appId = $(this).data('id');
            const newStatus = $(this).prev('.status-select').val();
            
            fetch(`/review_applications_status/${appId}`, {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({status: newStatus})
            }).then(response => {
                if(response.ok) table.ajax.reload(null, false);
            });
        });
    
        // Details modal handler
        $(document).on('click', '.details-button', function() {
            const appId = $(this).data('id');
            $.get(`/get_application_details/${appId}`, function(data) {
                $('#modalContent').html(data.html);
                $('#modalFooter').html(data.footer_html);
                $('#detailsModal').modal('show');
            });
        });
    });
    </script>

{% endblock %}
