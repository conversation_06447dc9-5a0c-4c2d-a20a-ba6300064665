<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E.164 Number Range Audit Submission</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        /* Styles remain the same as provided previously */
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #1abc9c;
            --background-color: #ecf0f1;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: 1px solid rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            background-image:
                radial-gradient(at 47% 33%, rgba(52, 152, 219, 0.25) 0, transparent 59%),
                radial-gradient(at 82% 65%, rgba(26, 188, 156, 0.15) 0, transparent 55%);
            min-height: 100vh;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            padding: 25px;
            margin-bottom: 25px;
        }

        .header {
            padding: 20px 0;
            color: var(--primary-color);
        }

        .tab-content {
            padding: 20px 0;
        }

        .nav-tabs {
            border-bottom: none;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px;
            margin-right: 5px;
            color: var(--primary-color);
            font-weight: 500;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-tabs .nav-link.active {
            background: var(--glass-bg);
            border: var(--glass-border);
            color: var(--secondary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 12px 15px;
            backdrop-filter: blur(5px);
            transition: all 0.3s;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.7);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
            border-color: var(--secondary-color);
        }

        .table {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            overflow: hidden;
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .btn-outline-secondary {
            border-color: var(--secondary-color);
            color: var(--secondary-color);
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .metric-card {
            padding: 20px;
            text-align: center;
            height: 100%;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--secondary-color);
        }

        .chart-container {
            min-height: 300px; /* Ensure container has height */
            margin-bottom: 20px;
            display: flex; /* Optional: for centering loading spinners etc. */
            justify-content: center;
            align-items: center;
        }

        .help-text {
            color: #7f8c8d;
            font-size: 0.85rem;
        }

        .locked-field {
            background-color: rgba(236, 240, 241, 0.7) !important;
            cursor: not-allowed;
        }

        .table th {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(44, 62, 80, 0.2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(44, 62, 80, 0.4);
        }

        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animated {
            animation: fadeIn 0.5s ease forwards;
        }

        .form-section {
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s; /* Adjust delay as needed */
        }
        /* Add specific delays for form sections if desired */
        #submission .form-section:nth-of-type(1) { animation-delay: 0.2s; }
        #submission .form-section:nth-of-type(2) { animation-delay: 0.4s; }
        #submission .form-section:nth-of-type(3) { animation-delay: 0.5s; }
        #submission .form-section:nth-of-type(4) { animation-delay: 0.6s; }

        #analytics .animated { animation-delay: 0.1s; }
        #analytics .glass-card.animated.mt-4:nth-of-type(2) { animation-delay: 0.2s; } /* Operator Comparison */
        #analytics .glass-card.animated.mt-4:nth-of-type(3) { animation-delay: 0.3s; } /* Porting Activity */
        #analytics .glass-card.animated.mt-4:nth-of-type(4) { animation-delay: 0.4s; } /* Porting Matrix */

        #history .animated { animation-delay: 0.1s; }





        /* static/css/e164-styles.css (or your main stylesheet) */

#dashboard-animation-container {
    width: 100%;
    height: 500px; /* Adjust height as needed */
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 2rem; /* Ensure spacing */
    perspective: 800px; /* Needed for 3D-like rotations */
    opacity: 0; /* Start hidden, fade in */
    animation: fadeIn 0.8s 0.3s ease forwards;
    overflow: hidden; /* Ensure elements don't overflow container */
    /* Remove background if needed, or set one */
     background: transparent;
}

.mobius-spinner {
    width: 150px; /* Adjust size of the overall spinner */
    height: 150px;
    position: relative;
    transform-style: preserve-3d; /* Allow 3D positioning */
}

.mobius-ring {
    position: absolute;
    bottom: 1px;
    left: 0;
    width: 210%;
    height: 210%;
    border: 2px solid rgba(219, 52, 155, 0.6); /* Line color and thickness */
    border-radius: 60%; /* Make rings circular initially */
    /* Change border-radius to 10% or similar for squared effect like image */
    /* border-radius: 10%; */
    animation: spin 5s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite; /* Animation type */
}




/* Staggering and Scaling */
/* Adjust scale (size) and animation delay for each ring */
.mobius-ring:nth-child(1) { transform: scale(1.0); animation-delay: -0.1s; border-color: rgba(52, 152, 219, 1.0); }
.mobius-ring:nth-child(2) { transform: scale(0.9); animation-delay: -0.2s; border-color: rgba(52, 152, 219, 0.9); }
.mobius-ring:nth-child(3) { transform: scale(0.8); animation-delay: -0.3s; border-color: rgba(52, 152, 219, 0.8); }
.mobius-ring:nth-child(4) { transform: scale(0.7); animation-delay: -0.4s; border-color: rgba(52, 152, 219, 0.7); }
.mobius-ring:nth-child(5) { transform: scale(0.6); animation-delay: -0.5s; border-color: rgba(52, 152, 219, 0.6); }
.mobius-ring:nth-child(6) { transform: scale(0.5); animation-delay: -0.6s; border-color: rgba(52, 152, 219, 0.5); }
.mobius-ring:nth-child(7) { transform: scale(0.4); animation-delay: -0.7s; border-color: rgba(52, 152, 219, 0.4); }
.mobius-ring:nth-child(8) { transform: scale(0.3); animation-delay: -0.8s; border-color: rgba(52, 152, 219, 0.3); }


/* Rotation Animation */
@keyframes spin {
    0% {
        /* Start rotation */
        transform: rotateY(0deg) rotateX(0deg) scale(var(--scale, 1)); /* Use scale from nth-child */
    }
    50% {
         /* Mid-point rotation - add X rotation for wobble */
         transform: rotateY(180deg) rotateX(60deg) scale(var(--scale, 1));
     }

    100% {
        /* Complete rotation */
        transform: rotateY(360deg) rotateX(0deg) scale(var(--scale, 1));
    }
}

/* Existing fade-out logic */
body.content-loaded #dashboard-animation-container {
  transition: opacity 0.5s ease, height 0.5s ease, margin 0.5s ease;
  opacity: 0;
  height: 0;
  margin-bottom: 0 !important;
  overflow: hidden;
}

    </style>



    <script>
        // const E164Data = { ... old way ... }; // Remove the old block
        window.E164Data = {{ E164Data | tojson | safe }}; // Embed the whole payload
        console.log("E164 Data initialized:", window.E164Data);
     </script>
</head>
<body>

    <div class="container py-5">
        <div class="header text-center mb-4 animated">
            <h1><i class="fas fa-phone-alt me-2"></i> E.164 Number Range Audit Submission</h1>
            <p class="text-muted">National Communications Authority - Biannual Numbering Audit</p>
        </div>

        <ul class="nav nav-tabs mb-4 animated" id="e164Tab" role="tablist">

            {# --- Data Submission Tab (Only for Operators) --- #}
            {% if not is_admin %}
            <li class="nav-item" role="presentation">
                <button class="nav-link {% if active_tab == 'submission' or (active_tab == 'default' and not is_admin) %}active{% endif %}"
                        id="submission-tab" data-bs-toggle="tab" data-bs-target="#submission"
                        type="button" role="tab" aria-controls="submission"
                        aria-selected="{{ 'true' if active_tab == 'submission' or (active_tab == 'default' and not is_admin) else 'false' }}">
                    <i class="fas fa-edit me-2"></i>Data Submission
                </button>
            </li>
            {% endif %}

            {# --- Analytics Tab (For Admins and Operators) --- #}
            <li class="nav-item" role="presentation">
                <button class="nav-link {% if active_tab == 'analytics' or (active_tab == 'default' and is_admin) %}active{% endif %}"
                        id="analytics-tab" data-bs-toggle="tab" data-bs-target="#analytics"
                        type="button" role="tab" aria-controls="analytics"
                        aria-selected="{{ 'true' if active_tab == 'analytics' or (active_tab == 'default' and is_admin) else 'false' }}">
                    <i class="fas fa-chart-bar me-2"></i>Analytics
                </button>
            </li>

            {# --- Submission History Tab (For Admins and Operators) --- #}
            <li class="nav-item" role="presentation">
                <button class="nav-link {% if active_tab == 'history' %}active{% endif %}"
                        id="history-tab" data-bs-toggle="tab" data-bs-target="#history"
                        type="button" role="tab" aria-controls="history"
                        aria-selected="{{ 'true' if active_tab == 'history' else 'false' }}">
                    <i class="fas fa-history me-2"></i>Submission History
                </button>
            </li>
        </ul>






        <div class="tab-content" id="e164TabContent">


            <div id="dashboard-animation-container" class="mb-4">
                <div class="mobius-spinner">
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    <div class="mobius-ring"></div>
                    </div>
            </div>
            

            {# --- Data Submission Pane (Only for Operators) --- #}
            {% if not is_admin %}
            <div class="tab-pane fade {% if active_tab == 'submission' or (active_tab == 'default' and not is_admin) %}show active{% endif %}"
                 id="submission" role="tabpanel" aria-labelledby="submission-tab">

                <div class="glass-card form-section">
                    <h3 class="mb-4">Operator Information</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="operator" class="form-label">Operator</label>
                            {# Populate dropdown using JS after page load to handle potential dynamic updates #}
                            <select class="form-select" id="operator" required>
                                <option value="" selected disabled>Select Operator</option>
                                {# Options populated by e164-audit.js #}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reporting-period" class="form-label">Reporting Period</label>
                            <select class="form-select" id="reporting-period" required>
                                <option value="" disabled>Select Period</option>
                                {% for period in reporting_periods %}
                                    <option value="{{ period.value }}" {% if period.selected %}selected{% endif %}>
                                        {{ period.text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="glass-card form-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Number Range Data</h3>
                        <div>
                            <button class="btn btn-outline-secondary me-2" id="preview-btn">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                            <button class="btn btn-primary" id="add-range-btn">
                                <i class="fas fa-plus me-2"></i>Add Range
                            </button>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="number-ranges-table">
                            <thead>
                                <tr>
                                    <th>NDC</th>
                                    <th>Type</th>
                                    <th>Starting Block</th>
                                    <th>Ending Block</th>
                                    <th>Total Allocated</th>
                                    <th>Active Subscriber</th>
                                    <th>Active Non-Subscriber</th>
                                    <th>Inactive</th>
                                    <th>Reserved</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                </tbody>
                        </table>
                    </div>
                </div>

                <div class="glass-card form-section">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Porting Data</h3>
                        <div>
                            <button class="btn btn-outline-secondary me-2" id="import-porting-btn">
                                <i class="fas fa-file-import me-2"></i>Import CSV
                            </button>
                            <button class="btn btn-primary" id="add-porting-btn">
                                <i class="fas fa-plus me-2"></i>Add Porting Record
                            </button>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Provide details of numbers ported between operators for each relevant NDC during the reporting period.
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover" id="porting-data-table">
                            <thead>
                                <tr>
                                    <th>NDC</th>
                                    <th>Range Type</th>
                                    <th>Source Operator</th>
                                    <th>Target Operator</th>
                                    <th>Number of Ports</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                </tbody>
                        </table>
                    </div>
                </div>

                <div class="glass-card form-section">
                    <h3 class="mb-4">Additional Information</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="submission-contact" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="submission-contact" placeholder="Name of responsible person">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="submission-email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="submission-email" placeholder="Contact email address">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="submission-notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="submission-notes" rows="3" placeholder="Any additional information or clarifications"></textarea>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <button class="btn btn-outline-secondary me-2" id="save-draft-btn">
                            <i class="fas fa-save me-2"></i>Save Draft
                        </button>
                        <button class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-paper-plane me-2"></i>Submit
                        </button>
                    </div>
                </div>
            </div>
            {% endif %} {# End of operator-only submission pane #}


            {# --- Analytics Pane (For Admins and Operators) --- #}
            <div class="tab-pane fade {% if active_tab == 'analytics' or (active_tab == 'default' and is_admin) %}show active{% endif %}"
                 id="analytics" role="tabpanel" aria-labelledby="analytics-tab">


                {% if is_admin %}
                <div class="alert alert-primary mb-4 animated fadeIn">
                    <i class="fas fa-user-shield me-2"></i> <strong>Admin View:</strong> Displaying aggregated data across all operators for the latest approved period. Use the selectors below to filter the view.
                </div>
            
                <div class="glass-card animated mb-4 p-3">
                    <h5 class="mb-3">Filter Analytics Data</h5>
                    <div class="row g-3 align-items-end">
                        <div class="col-md-5">
                            <label for="admin-analytics-period-select" class="form-label">Reporting Period</label>
                            <select class="form-select form-select-sm" id="admin-analytics-period-select">
                                <option value="latest" selected>Latest Approved Period</option>
                                <option value="all">All Approved Periods (Trends)</option> {# Option for trend view if implemented #}
                                {% for period in reporting_periods %} {# Assuming reporting_periods has approved periods #}
                                    <option value="{{ period.value }}">{{ period.text }}</option>
                                {% endfor %}
                                {# Or populate dynamically via JS if list is very long #}
                            </select>
                        </div>
                        <div class="col-md-5">
                            <label for="admin-analytics-operator-select" class="form-label">Operator</label>
                            <select class="form-select form-select-sm" id="admin-analytics-operator-select">
                                <option value="AT" selected>AT</option>
                                <option value="MTN" selected>MTN</option>
                                <option value="Telecel" selected>Telecel</option>
                                <option value="all" selected>All Operators</option>
                                </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary btn-sm w-100" id="admin-analytics-refresh-btn">
                                <i class="fas fa-sync-alt me-1"></i> Refresh
                            </button>
                        </div>
                    </div>
                </div>



                {% else %}
                <div class="alert alert-info mb-4 animated fadeIn">
                    <i class="fas fa-user me-2"></i> <strong>Operator View:</strong> Displaying analytics based on your latest approved submission.
                </div>
                {% endif %}

                <!-- REPLACED -->
                <div class="glass-card animated">
                    <h3 class="mb-4">Overall Utilization Metrics</h3>
                    <div class="row mb-4 text-center" id="metrics-summary">
                        <div class="col-md-3 col-6 mb-3" id="metric-utilization">  <div class="metric-card">
                                <h6>Utilization Rate</h6>
                                <div class="metric-value" data-metric="utilization">N/A</div>
                                <div class="metric-trend text-muted small" data-trend="utilization"><i class="fas fa-minus"></i> --</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3" id="metric-dormancy"> <div class="metric-card">
                                <h6>Dormancy Rate</h6>
                                <div class="metric-value" data-metric="dormancy">N/A</div>
                                <div class="metric-trend text-muted small" data-trend="dormancy"><i class="fas fa-minus"></i> --</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3" id="metric-reservation"> <div class="metric-card">
                                <h6>Reservation Rate</h6>
                                <div class="metric-value" data-metric="reservation">N/A</div>
                                <div class="metric-trend text-muted small" data-trend="reservation"><i class="fas fa-minus"></i> --</div>
                            </div>
                        </div>
                         <div class="col-md-3 col-6 mb-3" id="metric-growth"> <div class="metric-card">
                                <h6>Growth Rate (vs Prev)</h6>
                                <div class="metric-value" data-metric="growth">N/A</div>
                                <div class="metric-trend text-muted small" data-trend="growth"><i class="fas fa-minus"></i> --</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                            <h5 class="text-center mb-3">Market Share (Based on Active Numbers)</h5>
                            <div class="chart-container" id="market-share-chart">
                                </div>
                        </div>
                        <div class="col-lg-6">
                             <h5 class="text-center mb-3">Number Status Distribution</h5>
                             <div class="chart-container" id="status-distribution-chart">
                                </div>
                        </div>
                    </div>
                    </div> ```

                {% if is_admin %}
                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Operator Comparison</h3>
                    <div class="alert alert-secondary mb-3">
                        <i class="fas fa-info-circle me-2"></i> Administrator view comparing key metrics across operators.
                    </div>
                    <div class="row">
                        <div class="col-lg-6">
                             <h5 class="text-center mb-3">Overall Allocation Comparison</h5>
                            <div class="chart-container" id="operator-comparison-chart">
                                </div>
                        </div>
                        <div class="col-lg-6">
                             <h5 class="text-center mb-3">Utilization Rate Comparison</h5>
                            <div class="chart-container" id="utilization-comparison-chart">
                                </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if is_admin %}
                <div class="glass-card animated mt-4">
                    <h3 class="mb-3">Trend Analysis</h3>
                    <div class="chart-container" id="trend-chart" style="min-height: 400px;">
                         <div class="text-muted text-center p-5"></div>
                     </div>
                </div>
                {% endif %}

                <div class="glass-card animated mt-4">
                    <h3 class="mb-3">Range Usage Details & Exhaustion</h3>
                    <div class="chart-container" id="range-usage-detail-chart" style="min-height: 400px;">
                         <div class="text-muted text-center p-5">Select filters and refresh to view range details.</div>
                     </div>
                </div>

                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Porting Activity</h3>
                     <div class="alert alert-secondary mb-3">
                        <i class="fas fa-exchange-alt me-2"></i> Visualizing the flow of ported numbers between operators based on the latest approved data.
                    </div>
                    <div class="row">
                        {# --- Sankey Chart Container --- #}
                        <div class="col-12">
                             <h5 class="text-center mb-3">Inter-Operator Porting Flow (Sankey Diagram)</h5>
                             <div id="sankey-chart" class="chart-container bg-light rounded p-2 shadow-sm">
                                 <div class="text-muted text-center p-5">Loading Sankey Diagram...</div>
                             </div>
                        </div>
                        {# --- Other Porting Charts (Placeholder) --- #}
                        {#
                        <div class="col-md-6">
                             <h5 class="text-center mb-3">Net Porting Gains/Losses</h5>
                            <div class="chart-container" id="net-porting-chart">

                            </div>
                        </div>
                        #}
                    </div>
                </div>

            </div> {# End of Analytics Pane #}


            {# --- Submission History Pane (For Admins and Operators) --- #}
            <div class="tab-pane fade {% if active_tab == 'history' %}show active{% endif %}"
                 id="history" role="tabpanel" aria-labelledby="history-tab">
                 <div class="glass-card animated">
                     <h3 class="mb-4">Submission History</h3>
                     <div class="table-responsive">
                         <table class="table table-hover" id="history-table">
                             <thead>
                                 <tr>
                                     {% if is_admin %}<th>Operator</th>{% endif %}
                                     <th>Reporting Period</th>
                                     <th>Submitted At</th>
                                     <th>Status</th>
                                     <th>Total Allocated</th>
                                     <th>Utilization %</th>
                                     <th>Actions</th>
                                 </tr>
                             </thead>
                             <tbody>
                                 <tr><td colspan="{% if is_admin %}7{% else %}6{% endif %}" class="text-center p-5"><span class="spinner-border spinner-border-sm"></span> Loading history...</td></tr>
                             </tbody>
                         </table>
                     </div>
                 </div>
            </div> {# End of History Pane #}

        </div> {# End of Tab Content #}
    </div> {# End of Container #}

    <div class="modal fade" id="addRangeModal" tabindex="-1" aria-labelledby="addRangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRangeModalLabel">Add/Edit Number Range</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="rangeForm">
                        <input type="hidden" id="range-index"> {# For editing #}
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="ndc" class="form-label">NDC</label>
                                <select class="form-select" id="ndc" required>
                                    <option value="" selected disabled>Select NDC</option>
                                    {# Options populated by JS based on operator #}
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="range-type" class="form-label">Type</label>
                                <select class="form-select" id="range-type" required>
                                    <option value="" selected disabled>Select Type</option>
                                    <option value="WIRELESS DEDICATED">WIRELESS DEDICATED</option>
                                    <option value="WIRELINE DEDICATED">WIRELINE DEDICATED</option>
                                    <option value="SHARED">SHARED</option>
                                    <option value="OTHER">OTHER</option>
                                </select>
                            </div>
                        </div>
                         <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="start-block" class="form-label">Starting Block</label>
                                <input type="number" class="form-control" id="start-block" placeholder="e.g., 1000" required min="0">
                            </div>
                            <div class="col-md-6">
                                <label for="end-block" class="form-label">Ending Block</label>
                                <input type="number" class="form-control" id="end-block" placeholder="e.g., 1999" required min="0">
                                <div class="invalid-feedback">Ending block must be greater than or equal to starting block.</div>
                            </div>
                        </div>
                         <div class="row mb-3">
                             <div class="col-md-6">
                                <label for="total-allocated" class="form-label">Total Allocated</label>
                                <input type="number" class="form-control locked-field" id="total-allocated" readonly>
                                <small class="help-text">Calculated automatically (Ending - Starting + 1) * 1000</small>
                             </div>
                              <div class="col-md-6">
                                <label for="active-subscriber" class="form-label">Active Subscriber Numbers</label>
                                <input type="number" class="form-control" id="active-subscriber" required min="0" value="0">
                             </div>
                         </div>
                         <div class="row mb-3">
                             <div class="col-md-4">
                                <label for="active-non-subscriber" class="form-label">Active Non-Subscriber</label>
                                <input type="number" class="form-control" id="active-non-subscriber" required min="0" value="0">
                             </div>
                              <div class="col-md-4">
                                <label for="inactive" class="form-label">Inactive Numbers</label>
                                <input type="number" class="form-control" id="inactive" required min="0" value="0">
                             </div>
                              <div class="col-md-4">
                                <label for="reserved" class="form-label">Reserved Numbers</label>
                                <input type="number" class="form-control" id="reserved" required min="0" value="0">
                             </div>
                         </div>
                         <div class="alert alert-warning d-none" id="range-validation-error">
                             <i class="fas fa-exclamation-triangle me-2"></i> The sum of Active (Subscriber & Non-Subscriber), Inactive, and Reserved numbers must equal the Total Allocated numbers.
                         </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-range-btn">Save Range</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="addPortingModal" tabindex="-1" aria-labelledby="addPortingModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title" id="addPortingModalLabel">Add Porting Record</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="portingForm">
                        <div class="mb-3">
                            <label for="porting-ndc" class="form-label">NDC</label>
                            <input type="text" class="form-control" id="porting-ndc" required placeholder="e.g., 023, 020">
                        </div>
                        <div class="mb-3">
                            <label for="porting-range-type" class="form-label">Range Type</label>
                            <select class="form-select" id="porting-range-type" required>
                                <option value="" selected disabled>Select Range Type</option>
                                <option value="WIRELESS DEDICATED">WIRELESS DEDICATED</option>
                                <option value="WIRELINE DEDICATED">WIRELINE DEDICATED</option>
                                <option value="SHARED">SHARED</option>
                                <option value="OTHER">OTHER</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="porting-source-operator" class="form-label">Source Operator</label>
                             <select class="form-select" id="porting-source-operator" required>
                                <option value="" selected disabled>Select Source</option>
                                {# Options populated by JS #}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="porting-target-operator" class="form-label">Target Operator</label>
                             <select class="form-select" id="porting-target-operator" required>
                                <option value="" selected disabled>Select Target</option>
                                 {# Options populated by JS #}
                            </select>
                        </div>
                         <div class="mb-3">
                            <label for="porting-count" class="form-label">Number of Ports</label>
                            <input type="number" class="form-control" id="porting-count" required min="1">
                         </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-porting-btn">Add Record</button>
                </div>
            </div>
        </div>
    </div>


    <div class="modal fade" id="importPortingModal" tabindex="-1" aria-labelledby="importPortingModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title" id="importPortingModalLabel">Import Porting Data from CSV</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="importPortingForm">
                        <div class="mb-3">
                            <label for="porting-csv" class="form-label">Select CSV File</label>
                            <input class="form-control" type="file" id="porting-csv" accept=".csv" required>
                            <small class="form-text text-muted">
                                CSV format: NDC, RangeType, SourceOperatorID, TargetOperatorID, Count (No header row)
                            </small>
                        </div>
                        <div id="csv-preview-area" class="mb-3" style="max-height: 200px; overflow-y: auto;">
                            <small class="text-muted">File preview will appear here...</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="import-porting-csv-btn">Import Data</button>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="previewModal" tabindex="-1" aria-labelledby="previewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title" id="previewModalLabel">Submission Preview</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="preview-content">
                    <p class="text-center text-muted">Generating preview...</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <div class="toast-container position-fixed bottom-0 end-0 p-3">
        </div>


    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/d3@7"></script> {# Include D3 library #}
    <script src="https://cdn.jsdelivr.net/npm/d3-sankey@0.12.3/dist/d3-sankey.min.js"></script> {# Include D3 Sankey plugin #}

    

    {# Link to the refactored e164-audit.js file #}
    <script src="{{ url_for('static', filename='js/e164-audit.js') }}"></script>

</body>
</html>
