"""
Convert Telecel Audit Excel file to MongoDB JSON format.

This script reads the Telecel audit Excel file and converts it to JSON format
that can be imported into the MongoDB 'audit' collection.
"""

import json
import pandas as pd
import numpy as np
from datetime import datetime
import re

def clean_number(value):
    """Convert number strings to integers, handling NaN and invalid values."""
    if pd.isna(value) or value is None:
        return 0
    
    if isinstance(value, (int, float)):
        return int(value)
    
    try:
        # Remove non-numeric characters and convert to int
        value = re.sub(r'[^\d.-]', '', str(value))
        return int(float(value)) if value else 0
    except (ValueError, TypeError):
        return 0

def clean_block_number(value):
    """Clean block numbers, removing leading zeros."""
    if pd.isna(value) or value is None:
        return 0
    
    value_str = str(value).strip()
    # Remove leading zeros
    value_str = re.sub(r'^0+', '', value_str)
    
    try:
        return int(value_str) if value_str else 0
    except (ValueError, TypeError):
        return 0

def process_telecel_audit():
    """Process Telecel audit Excel file and convert to MongoDB format."""
    excel_file = "TELECEL FIRST HALF 2024 AUDIT SUMMARY.xlsx"
    operator_name = "Telecel Ghana"
    
    print(f"Processing {operator_name} data from {excel_file}...")
    
    try:
        # Read the Excel file
        xls = pd.ExcelFile(excel_file)
        
        # Check for required sheets
        if "E.164 TYPE NUMBERS" not in xls.sheet_names:
            print(f"Error: Required sheet 'E.164 TYPE NUMBERS' not found in {excel_file}.")
            return []
        
        # Read E.164 TYPE NUMBERS sheet
        e164_df = pd.read_excel(xls, "E.164 TYPE NUMBERS")
        
        # Check if SNR sheet exists
        snr_details = {
            "total_snrs": 0,
            "service_categories": [],
            "digit_categories": {
                "3_digit": 0,
                "4_digit": 0,
                "5_digit": 0,
                "6_digit": 0
            }
        }
        
        if "SNR" in xls.sheet_names:
            snr_df = pd.read_excel(xls, "SNR")
            
            # Count SNRs by digit length
            if "SNR" in snr_df.columns:
                snr_details["total_snrs"] = len(snr_df)
                
                # Count digit categories
                for snr in snr_df["SNR"]:
                    if pd.isna(snr):
                        continue
                    
                    snr_str = str(snr).strip()
                    if len(snr_str) == 3:
                        snr_details["digit_categories"]["3_digit"] += 1
                    elif len(snr_str) == 4:
                        snr_details["digit_categories"]["4_digit"] += 1
                    elif len(snr_str) == 5:
                        snr_details["digit_categories"]["5_digit"] += 1
                    elif len(snr_str) == 6:
                        snr_details["digit_categories"]["6_digit"] += 1
            
            # Get unique service categories
            if "SERVICE" in snr_df.columns:
                services = snr_df["SERVICE"].dropna().unique()
                snr_details["service_categories"] = [s for s in services if isinstance(s, str) and s.strip()]
        
        # Initialize porting data mapping
        porting_map = {}
        if "PORTING DATA" in xls.sheet_names:
            porting_df = pd.read_excel(xls, "PORTING DATA")
            
            # Check for required columns
            if "Subscriber NDC e.g. 02A" in porting_df.columns:
                ported_in_col = "Cumulative Ported In Numbers within the Audit Period" if "Cumulative Ported In Numbers within the Audit Period" in porting_df.columns else None
                ported_out_col = "Cumulative Ported Out Numbers within the Audit Period" if "Cumulative Ported Out Numbers within the Audit Period" in porting_df.columns else None
                
                for _, row in porting_df.iterrows():
                    ndc = str(row["Subscriber NDC e.g. 02A"]).strip()
                    if not ndc:
                        continue
                    
                    ported_in = clean_number(row[ported_in_col]) if ported_in_col else 0
                    ported_out = clean_number(row[ported_out_col]) if ported_out_col else 0
                    
                    porting_map[ndc] = {
                        "ported_in": ported_in,
                        "ported_out": ported_out
                    }
        
        # Process E.164 data to create audit documents
        mongodb_documents = []
        
        # Iterate through each row
        for _, row in e164_df.iterrows():
            # Skip rows without MNO
            if "MNO" not in row or pd.isna(row["MNO"]):
                continue
            
            # Extract NDC
            ndc = str(row["NDC"]).strip() if "NDC" in row and not pd.isna(row["NDC"]) else ""
            if not ndc:
                continue
            
            # Extract data
            sn_type = row["TYPE of NDC"] if "TYPE of NDC" in row and not pd.isna(row["TYPE of NDC"]) else "WIRELESS DEDICATED"
            start_block = clean_block_number(row["STARTING BLOCK"]) if "STARTING BLOCK" in row else 0
            end_block = clean_block_number(row["ENDING BLOCK"]) if "ENDING BLOCK" in row else 0
            
            # Subscriber Numbers column (could have a space at the end)
            tasn_col = None
            for col in row.index:
                if isinstance(col, str) and col.startswith("TAcN - SUBSCRIBER NUMBERS"):
                    tasn_col = col
                    break
            
            tasn = clean_number(row[tasn_col]) if tasn_col and not pd.isna(row[tasn_col]) else 0
            
            # Non-Subscriber Numbers
            active_non_sub_col = None
            for col in row.index:
                if isinstance(col, str) and "NON-SUBSCRIBER NUMBERS" in col and "TAcN" in col:
                    active_non_sub_col = col
                    break
            
            active_non_sub = clean_number(row[active_non_sub_col]) if active_non_sub_col and not pd.isna(row[active_non_sub_col]) else 0
            
            # Inactive Subscriber Numbers
            inactive_sub_col = None
            for col in row.index:
                if isinstance(col, str) and "TOTAL INACTIVE NUMBERS (TIN) - SUBSCRIBER NUMBERS" in col:
                    inactive_sub_col = col
                    break
            
            inactive_sub = clean_number(row[inactive_sub_col]) if inactive_sub_col and not pd.isna(row[inactive_sub_col]) else 0
            
            # Inactive Non-Subscriber Numbers
            inactive_non_sub_col = None
            for col in row.index:
                if isinstance(col, str) and "TOTAL INACTIVE NUMBERS (TIN) - NON-SUBSCRIBER NUMBERS" in col:
                    inactive_non_sub_col = col
                    break
            
            inactive_non_sub = clean_number(row[inactive_non_sub_col]) if inactive_non_sub_col and not pd.isna(row[inactive_non_sub_col]) else 0
            
            # Reserved Subscriber Numbers
            reserved_sub_col = None
            for col in row.index:
                if isinstance(col, str) and "[RESERVED SUBSCRIBER NUMBERS]" in col:
                    reserved_sub_col = col
                    break
            
            reserved_sub = clean_number(row[reserved_sub_col]) if reserved_sub_col and not pd.isna(row[reserved_sub_col]) else 0
            
            # Reserved Non-Subscriber Numbers
            reserved_non_sub_col = None
            for col in row.index:
                if isinstance(col, str) and "[RESERVED NON-SUBSCRIBER NUMBERS]" in col:
                    reserved_non_sub_col = col
                    break
            
            reserved_non_sub = clean_number(row[reserved_non_sub_col]) if reserved_non_sub_col and not pd.isna(row[reserved_non_sub_col]) else 0
            
            # Gross Addition
            gross_add_col = None
            for col in row.index:
                if isinstance(col, str) and "*GROSS ADDITION" in col:
                    gross_add_col = col
                    break
            
            gross_add = clean_number(row[gross_add_col]) if gross_add_col and not pd.isna(row[gross_add_col]) else 0
            
            # Net Addition
            net_add_col = None
            for col in row.index:
                if isinstance(col, str) and "* NET ADDITION" in col:
                    net_add_col = col
                    break
            
            net_add = clean_number(row[net_add_col]) if net_add_col and not pd.isna(row[net_add_col]) else 0
            
            # Cumulative Ported Back
            cpb_col = None
            for col in row.index:
                if isinstance(col, str) and "*CUMULATIVE PORTED BACK (CPB)" in col:
                    cpb_col = col
                    break
            
            cpb = clean_number(row[cpb_col]) if cpb_col and not pd.isna(row[cpb_col]) else 0
            
            # Cumulative Ported Out
            cpo_col = None
            for col in row.index:
                if isinstance(col, str) and "*CUMULATIVE PORTED OUT (CPO)" in col:
                    cpo_col = col
                    break
            
            cpo = clean_number(row[cpo_col]) if cpo_col and not pd.isna(row[cpo_col]) else 0
            
            # Calculate derivatives
            tin = inactive_sub + inactive_non_sub
            trn = reserved_sub + reserved_non_sub
            
            # Default TAN to tasn if not explicitly provided
            tan = tasn if tasn > 0 else 10000000  # Default to 10M if no data
            
            # Calculate range size
            try:
                number_range_size = end_block - start_block + 1
                if number_range_size <= 0:
                    number_range_size = tan or 1000000  # Fallback if calculation is invalid
            except:
                number_range_size = tan or 1000000  # Fallback on error
            
            # Calculate metrics
            utilization_rate = (tasn + active_non_sub) / max(number_range_size, 1)
            percent_inactive = (tin / max(number_range_size, 1)) * 100
            percent_reserved = (trn / max(number_range_size, 1)) * 100
            
            # Get porting data for this NDC
            porting_data = porting_map.get(ndc, {"ported_in": 0, "ported_out": 0})
            porting_balance = porting_data["ported_in"] - porting_data["ported_out"]
            
            # Calculate performance scores
            utilization_score = min(3, utilization_rate * 3.33)
            porting_factor = min(1.0, max(0, 1 - abs(porting_balance) / 1000))
            porting_score = porting_factor * 3.33
            inactive_factor = max(0, 1 - (percent_inactive / 100))
            inactive_score = inactive_factor * 3.34
            overall_score = utilization_score + porting_score + inactive_score
            
            # Create audit document
            audit_doc = {
                "operator": operator_name,
                "quarter": "H1",  # First half of 2024
                "year": 2024,
                "sn_type": sn_type,
                "ndc": ndc,
                "start_block": start_block,
                "end_block": end_block,
                "tan": tan,
                "tasn": tasn,
                "active_non_sub": active_non_sub,
                "tin": tin,
                "inactive_sub": inactive_sub,
                "inactive_non_sub": inactive_non_sub,
                "trn": trn,
                "reserved_sub": reserved_sub,
                "reserved_non_sub": reserved_non_sub,
                "gross_add": gross_add,
                "net_add": net_add,
                "cpb": cpb,
                "cpo": cpo,
                "created_by": "admin",
                "timestamp": datetime.now().isoformat(),
                "processed": {
                    "utilization_rate": round(utilization_rate, 2),
                    "percent_inactive": round(percent_inactive, 2),
                    "percent_reserved": round(percent_reserved, 2),
                    "porting_balance": porting_balance
                },
                "snr_details": snr_details,
                "performance_scores": {
                    "utilization": round(utilization_score, 2),
                    "porting_balance": round(porting_score, 2),
                    "inactive": round(inactive_score, 2),
                    "overall": round(overall_score, 2)
                },
                # New fields for SNR tracking
                "active_snrs_count": 0,
                "deactivated_snrs_count": 0,
                "active_snrs": [],
                "deactivated_snrs": []
            }
            
            mongodb_documents.append(audit_doc)
        
        print(f"Processed {len(mongodb_documents)} records for {operator_name}.")
        
        # Save to JSON file
        output_file = "Telecel_Ghana_audit_data.json"
        with open(output_file, 'w') as f:
            json.dump(mongodb_documents, f, indent=2)
        
        print(f"Converted data saved to {output_file}")
        print(f"Total documents: {len(mongodb_documents)}")
        
        return mongodb_documents
    
    except Exception as e:
        print(f"Error processing {excel_file}: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

if __name__ == "__main__":
    process_telecel_audit()
