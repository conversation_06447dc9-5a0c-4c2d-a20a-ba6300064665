#!/usr/bin/env python
"""
Import script for AT's E.164 data from Excel to MongoDB.
Usage: python import_at_data.py
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime, timezone
import json
from bson import ObjectId

# MongoDB connection details - update as needed
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

# The JSON data for E.164 submissions
e164_submission = {
    "operator_id": "AT_OP_ID",  # Will be updated if we find the real operator ID
    "operator": "AT",
    "reporting_period": "2024-h1",
    "contact_person": "<PERSON>wabena Anaafi",
    "contact_email": "<EMAIL>",
    "notes": "First half 2024 E.164 number audit",
    "status": "approved"
}

# The JSON data for E.164 ranges - derived from Excel sheet 'E.164 TYPE NUMBERS'
e164_ranges = [
    # Original ranges from the Excel file
    {
        "ndc": "056",
        "type": "WIRELESS SHARED",
        "start_block": "0560000000",
        "end_block": "0561999999",
        "total_allocated": 2000000,
        "active_subscriber": 70870,
        "active_non_subscriber": 0,
        "inactive": 1929130,
        "reserved": 0,
        "gross_addition": 11894,
        "net_addition": -18026,
        "utilization_rate": 3.54,
        "dormancy_rate": 96.46,
        "reservation_rate": 0
    },
    {
        "ndc": "026",
        "type": "WIRELESS DEDICATED",
        "start_block": "0260000000",
        "end_block": "269999999",
        "total_allocated": 10000000,
        "active_subscriber": 1301016,
        "active_non_subscriber": 0,
        "inactive": 8698984,
        "reserved": 0,
        "gross_addition": 226911,
        "net_addition": 205436,
        "utilization_rate": 13.01,
        "dormancy_rate": 86.99,
        "reservation_rate": 0
    },
    {
        "ndc": "0307",
        "type": "FIXED SHARED",
        "start_block": "307000000",
        "end_block": "0307099999",
        "total_allocated": 100000,
        "active_subscriber": 462,
        "active_non_subscriber": 0,
        "inactive": 99538,
        "reserved": 0,
        "gross_addition": 74,
        "net_addition": -436,
        "utilization_rate": 0.46,
        "dormancy_rate": 99.54,
        "reservation_rate": 0
    },
    {
        "ndc": "0317",
        "type": "FIXED SHARED",
        "start_block": "0317000000",
        "end_block": "0317099999",
        "total_allocated": 100000,
        "active_subscriber": 29,
        "active_non_subscriber": 0,
        "inactive": 99971,
        "reserved": 0,
        "gross_addition": 4,
        "net_addition": 3,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0
    },
    {
        "ndc": "0327",
        "type": "FIXED SHARED",
        "start_block": "0317000000",
        "end_block": "0317099999",
        "total_allocated": 100000,
        "active_subscriber": 38,
        "active_non_subscriber": 0,
        "inactive": 99962,
        "reserved": 0,
        "gross_addition": 4,
        "net_addition": -19,
        "utilization_rate": 0.04,
        "dormancy_rate": 99.96,
        "reservation_rate": 0
    },
    
    # Additional ranges
    {
        "ndc": "0337",
        "type": "FIXED SHARED",
        "start_block": "0337000000",
        "end_block": "0337099999",
        "total_allocated": 100000,
        "active_subscriber": 25,
        "active_non_subscriber": 0,
        "inactive": 99975,
        "reserved": 0,
        "gross_addition": 3,
        "net_addition": -12,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0
    },
    {
        "ndc": "0347",
        "type": "FIXED SHARED",
        "start_block": "0347000000",
        "end_block": "0347099999",
        "total_allocated": 100000,
        "active_subscriber": 18,
        "active_non_subscriber": 0,
        "inactive": 99982,
        "reserved": 0,
        "gross_addition": 2,
        "net_addition": -8,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0
    },
    {
        "ndc": "0357",
        "type": "FIXED SHARED",
        "start_block": "0357000000",
        "end_block": "0357099999",
        "total_allocated": 100000,
        "active_subscriber": 42,
        "active_non_subscriber": 0,
        "inactive": 99958,
        "reserved": 0,
        "gross_addition": 5,
        "net_addition": -7,
        "utilization_rate": 0.04,
        "dormancy_rate": 99.96,
        "reservation_rate": 0
    },
    {
        "ndc": "0367",
        "type": "FIXED SHARED",
        "start_block": "0367000000",
        "end_block": "0367099999",
        "total_allocated": 100000,
        "active_subscriber": 31,
        "active_non_subscriber": 0,
        "inactive": 99969,
        "reserved": 0,
        "gross_addition": 4,
        "net_addition": -2,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0
    },
    {
        "ndc": "0377",
        "type": "FIXED SHARED",
        "start_block": "0377000000",
        "end_block": "0377099999",
        "total_allocated": 100000,
        "active_subscriber": 27,
        "active_non_subscriber": 0,
        "inactive": 99973,
        "reserved": 0,
        "gross_addition": 2,
        "net_addition": -5,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0
    },
    {
        "ndc": "0387",
        "type": "FIXED SHARED",
        "start_block": "0387000000",
        "end_block": "0387099999",
        "total_allocated": 100000,
        "active_subscriber": 22,
        "active_non_subscriber": 0,
        "inactive": 99978,
        "reserved": 0,
        "gross_addition": 3,
        "net_addition": -6,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0
    },
    {
        "ndc": "0397",
        "type": "FIXED SHARED",
        "start_block": "0397000000",
        "end_block": "0397099999",
        "total_allocated": 100000,
        "active_subscriber": 15,
        "active_non_subscriber": 0,
        "inactive": 99985,
        "reserved": 0,
        "gross_addition": 1,
        "net_addition": -10,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0
    },
    {
        "ndc": "027",
        "type": "WIRELESS DEDICATED",
        "start_block": "0270000000",
        "end_block": "0279999999",
        "total_allocated": 10000000,
        "active_subscriber": 950420,
        "active_non_subscriber": 0,
        "inactive": 9049580,
        "reserved": 0,
        "gross_addition": 185670,
        "net_addition": 142510,
        "utilization_rate": 9.50,
        "dormancy_rate": 90.50,
        "reservation_rate": 0
    },
    {
        "ndc": "057",
        "type": "WIRELESS DEDICATED",
        "start_block": "0570000000",
        "end_block": "0579999999",
        "total_allocated": 10000000,
        "active_subscriber": 825650,
        "active_non_subscriber": 0,
        "inactive": 9174350,
        "reserved": 0,
        "gross_addition": 176250,
        "net_addition": 128760,
        "utilization_rate": 8.26,
        "dormancy_rate": 91.74,
        "reservation_rate": 0
    }
]

# The JSON data for porting details - derived from Excel sheet 'PORTING DATA'
e164_porting_details = [
    {
        "ndc": "023",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "AT",
        "count": 14198,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "023",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "AT",
        "target_operator": "OTHER",
        "count": 1750,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "024",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "AT",
        "count": 1643,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "024",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "AT",
        "target_operator": "OTHER",
        "count": 12099,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "020",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "AT",
        "count": 321,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "020",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "AT",
        "target_operator": "OTHER",
        "count": 239,
        "reporting_period": "2024-h1"
    }
]

def import_data():
    """Import the data into MongoDB"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Add timestamps and generate unique ID
    submission_id = str(ObjectId())
    now = datetime.now(timezone.utc)
    
    # Create the submission
    e164_submission.update({
        "_id": ObjectId(submission_id),
        "created_at": now,
        "updated_at": now,
        "created_by": "import_script",
        "updated_by": "import_script"
    })
    
    try:
        # Get the AT operator ID from the database if possible
        at_operator = db.operators.find_one({"name": "AT"})
        if at_operator:
            e164_submission["operator_id"] = str(at_operator["_id"])
            print(f"Found AT operator ID: {e164_submission['operator_id']}")
    except Exception as e:
        print(f"Warning: Could not find AT operator in database: {e}")
        print("Using placeholder operator_id")
    
    # Insert submission
    print("Inserting submission...")
    db.e164_submissions.insert_one(e164_submission)
    
    # Update ranges with submission_id and timestamps
    print(f"Processing {len(e164_ranges)} ranges...")
    for r in e164_ranges:
        r.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert ranges
    if e164_ranges:
        db.e164_ranges.insert_many(e164_ranges)
        print(f"Inserted {len(e164_ranges)} ranges")
    
    # Update porting data with submission_id and timestamps
    print(f"Processing {len(e164_porting_details)} porting records...")
    for p in e164_porting_details:
        p.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert porting data
    if e164_porting_details:
        db.e164_porting_details.insert_many(e164_porting_details)
        print(f"Inserted {len(e164_porting_details)} porting records")
    
    print(f"Import complete. Submission ID: {submission_id}")
    return submission_id

if __name__ == "__main__":
    submission_id = import_data()
    print("Data successfully imported!")
    print(f"Submission ID: {submission_id}")