# extensions.py
from flask_pymongo import <PERSON>yMongo
from flask_login import <PERSON>gin<PERSON>ana<PERSON>, login_required, current_user
from flask_cors import CORS
from flask_caching import Cache
from celery import Celery
from functools import wraps
from flask import flash, redirect, url_for

# Initialize extension objects (without the app yet)
login_manager = LoginManager()
mongo = PyMongo()
cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})
cors = CORS()
celery = Celery()

# Define admin_required decorator
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Define allowed_file function
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}

def allowed_file(filename):
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
            and not any(char in filename for char in {'/', '\\', ':'}))