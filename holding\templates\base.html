
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title if title else "SNR Application System" }}</title>

<script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/css/bootstrap.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/styles.css') }}">
    
</head>
<body>





<!-- Navbar -->
<nav class="navbar navbar-expand-lg navbar-light bg-light">
    <a class="navbar-brand" href="/">NCA Numbering Administrative System</a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
        <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav ml-auto">
            <!-- Other nav items -->


            <!-- Notification dropdown -->
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="notificationsDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa fa-bell"></i>
                    {% if unread_notifications_count > 0 %}
                        <span class="badge badge-danger">{{ unread_notifications_count }}</span>
                    {% endif %}
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="notificationsDropdown" style="max-width: 300px; overflow-y: auto; max-height: 400px;">
                    {% if notifications %}
                        {% for notification in notifications %}
                            <a class="dropdown-item" href="{{ url_for('view_notification', notification_id=notification._id) }}">
                                {{ notification.message }}
                                <br>
                                <small class="text-muted">{{ notification.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                            </a>
                        {% endfor %}
                    {% else %}
                        <a class="dropdown-item text-center text-muted">No new notifications</a>
                    {% endif %}
                </div>
            </li>

            <!-- User profile dropdown -->
            <li class="nav-item dropdown">
                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa fa-user"></i>
                    {{ current_user.username }}
                </a>
                <div class="dropdown-menu dropdown-menu-right" aria-labelledby="userDropdown">
                    {% if current_user.is_authenticated and current_user.is_admin %}
                    <a class="dropdown-item" href="{{ url_for('review_applications') }}">Application Review Dashboard</a>
                    <a class="dropdown-item" href="{{ url_for('e164.list_users') }}">
                        <i class="fas fa-users me-2"></i>Manage Users</a>
                    <a class="dropdown-item" href="{{ url_for('generate_report') }}">Reports</a>

                    <a class="dropdown-item" href="{{ url_for('e164.dashboard') }}">E.164 Audit</a>
                    {% else %}
                    <a class="dropdown-item" href="{{ url_for('dashboard') }}">Dashboard</a>
                    {% endif %}
                    <a class="dropdown-item" href="{{ url_for('logout') }}">Logout</a>
                </div>
            </li>
        </ul>
    </div>
</nav>






<div class="container mt-3">
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    {% block content %}{% endblock %}
</div>


<script src="https://cdn.jsdelivr.net/npm/popper.js@1.16.1/dist/umd/popper.min.js"></script>

<script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.0/js/bootstrap.min.js"> </script>
<!-- <script src="https://kit.fontawesome.com/9de6b596b9.js" crossorigin="anonymous"></script> -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
</body>
</html>
