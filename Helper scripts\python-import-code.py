# from pymongo import Mongo<PERSON><PERSON>
from datetime import datetime
from app import mongo
import json

def import_mtn_audit_data():
    # Connect to MongoDB
    # client = MongoClient("mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0")
    # db = client['audit']
    # collection = db['audits']
    
    # Load the JSON file
    with open('mtn_audit_h1_2024.json', 'r') as file:
        audit_data = json.load(file)
    
    # Convert timestamp strings to datetime objects
    for record in audit_data:
        record['timestamp'] = datetime.fromisoformat(record['timestamp'].replace('Z', '+00:00'))
    
    # Insert the records into MongoDB collection
    result = mongo.db.audits.insert_many(audit_data)
    
    # Print the results
    print(f"Successfully imported {len(result.inserted_ids)} audit records")
    
    # Close the MongoDB connection
    # client.close()

if __name__ == "__main__":
    import_mtn_audit_data()
