<!-- templates/application_details.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Application Details</h2>

                    {% if application %}
                        <div class="form-group">
                            <label>Applicant Name</label>
                            <p>{{ application.applicant_name }}</p>
                        </div>

                        <div class="form-group">
                            <label>Application Date</label>
                            <p>{{ application.application_date }}</p>
                        </div>

                        <div class="form-group">
                            <label>Status</label>
                            <p>{{ application.status }}</p>
                        </div>

                        <div class="form-group">
                            <label>Remarks</label>
                            <p>{{ application.remarks }}</p>
                        </div>

                        <div class="form-group">
                            <label>Company</label>
                            <p>{{ application.company }}</p>
                        </div>

                        <div class="form-group">
                            <label>Contact</label>
                            <p>{{ application.contact }}</p>
                        </div>

                        <div class="form-group">
                            <label>Email</label>
                            <p>{{ application.email }}</p>
                        </div>

                        <div class="form-group">
                            <label>Phone</label>
                            <p>{{ application.phone }}</p>
                        </div>

                        <div class="form-group">
                            <label>SNR Details</label>
                            <ul>
                                {% for snr in application.snr_details %}
                                <li>
                                    <strong>SNR:</strong> {{ snr.snr }}<br>
                                    <strong>Type:</strong> {{ snr.type }}<br>
                                    <strong>Purpose:</strong> {{ snr.purpose }}<br>
                                    <strong>Status:</strong> {{ snr.status }}<br>
                                    <strong>Certificate ID:</strong> {{ snr.certificate_id }}
                                </li>
                                {% endfor %}
                            </ul>
                        </div>

                        <a href="{{ url_for('review_applications') }}" class="btn btn-secondary btn-block mt-2">Back to Applications</a>
                    {% else %}
                        <p>No application details found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .glassmorphic-card {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 16px;
        box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.3);
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .glassmorphic-card:hover {
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        transform: translateY(-5px);
    }

    .card-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }

    .form-group {
        margin-bottom: 1.5rem;
    }

    .form-group label {
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .form-group p {
        margin: 0;
        padding: 0.5rem;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
    }

    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
    }
</style>

{% endblock %}
