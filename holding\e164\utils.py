# utils.py
import os
import re
import base64
from io import BytesIO
from datetime import datetime, timedelta, timezone
from functools import wraps
from bson import ObjectId
from flask import flash, redirect, url_for
from flask_login import current_user
import qrcode

from extensions import mongo



# File handling utilities
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}

def allowed_file(filename):
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
            and not any(char in filename for char in {'/', '\\', ':'}))

# Authorization decorators
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def log_activity(action, details=""):
    """Log user activity to the activity_log collection"""
 
    
    activity = {
        "user_id": getattr(current_user, 'id', 'Guest'),
        "email": getattr(current_user, 'email', 'Guest'),
        "action": action,
        "details": details,
        "timestamp": datetime.now(timezone.utc)
    }
    mongo.db.activity_log.insert_one(activity)

def add_notification(message, recipient):
    """Add a notification for a user"""

    
    notification = {
        'message': message,
        'recipient': recipient,
        'read': False,
        'timestamp': datetime.now(timezone.utc)
    }
    mongo.db.notifications.insert_one(notification)

def snr_length_finder(snr):
    """Determine SNR type based on length and format"""
    # Remove spaces and hyphens
    if not snr:
        return "Undefined"
        
    snr = str(snr).replace(" ", "").replace("-", "")
    
    # If the snr starts with "0800", it's Tollfree
    if len(snr) >= 9 and (snr.startswith("0800") or snr.startswith("800")):
        ans = "Tollfree"
    elif len(snr) >= 9 and (snr.startswith("0900") or snr.startswith("900")):
        ans = "Premium"
    elif len(snr) == 3 and 100 <= int(snr) <= 999:
        ans = "3 Digit"
    elif len(snr) == 4 and 1000 <= int(snr) <= 9999:
        ans = "4 Digit"
    elif len(snr) == 5 and 10000 <= int(snr) <= 99999:
        ans = "5 Digit"
    elif len(snr) == 6 and 100000 <= int(snr) <= 999999:
        ans = "6 Digit"
    else:
        ans = "Undefined"
    
    return ans

def get_status_table(snr):
    """Determine which status collection to update based on SNR"""
    if str(snr).startswith('0800'):
        return 'tf_status'
    elif str(snr).startswith('0900'):
        return 'pr_status'
    else:
        return 'snr_status'


def serialize_to_json(doc):
    """Converts MongoDB document (with ObjectId and datetime) to JSON-serializable dict."""
    if doc is None:
        return None
    serialized = {}
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            serialized[key] = str(value)
        elif isinstance(value, datetime):
            serialized[key] = value.isoformat() # Convert datetime to ISO string
        elif isinstance(value, dict):
            serialized[key] = serialize_to_json(value) # Recursively serialize nested dicts
        elif isinstance(value, list):
             # Handle lists (e.g., list of dicts)
             serialized[key] = [serialize_to_json(item) if isinstance(item, dict) else item for item in value]
        else:
            serialized[key] = value
    return serialized

def calculate_number_metrics(number_range):
    """Calculate derived metrics for a number range"""
    # Get basic numbers
    total_allocated = number_range.get('total_allocated', 0)
    active_subscriber = number_range.get('active_subscriber', 0)
    active_non_subscriber = number_range.get('active_non_subscriber', 0)
    inactive = number_range.get('inactive', 0)
    reserved = number_range.get('reserved', 0)
    
    # Calculate utilization metrics
    total_active = active_subscriber + active_non_subscriber
    utilization_rate = (total_active / total_allocated * 100) if total_allocated > 0 else 0
    dormancy_rate = (inactive / total_allocated * 100) if total_allocated > 0 else 0
    reservation_rate = (reserved / total_allocated * 100) if total_allocated > 0 else 0
    
    # Return calculated metrics
    return {
        'total_active': total_active,
        'utilization_rate': round(utilization_rate, 2),
        'dormancy_rate': round(dormancy_rate, 2),
        'reservation_rate': round(reservation_rate, 2),
        'unaccounted': total_allocated - total_active - inactive - reserved,
    }