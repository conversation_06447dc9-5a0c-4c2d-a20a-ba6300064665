# e164_routes.py
from functools import wraps
from flask import Blueprint, jsonify, render_template, request, redirect, url_for, flash
from flask_login import login_required, current_user
from bson.objectid import ObjectId
from datetime import datetime, timezone
import pandas as pd
import json

# Import from extensions instead of app.py
from extensions import mongo

# Create a Blueprint for E.164 routes
e164_bp = Blueprint('e164', __name__, url_prefix='/e164')

# Import the admin_required decorator or redefine it
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function



# Helper functions for E.164 number range operations
def calculate_number_metrics(number_range):
    """Calculate derived metrics for a number range"""
    # Get basic numbers
    total_allocated = number_range.get('total_allocated', 0)
    active_subscriber = number_range.get('active_subscriber', 0)
    active_non_subscriber = number_range.get('active_non_subscriber', 0)
    inactive = number_range.get('inactive', 0)
    reserved = number_range.get('reserved', 0)
    
    # Calculate utilization metrics
    total_active = active_subscriber + active_non_subscriber
    utilization_rate = (total_active / total_allocated * 100) if total_allocated > 0 else 0
    dormancy_rate = (inactive / total_allocated * 100) if total_allocated > 0 else 0
    reservation_rate = (reserved / total_allocated * 100) if total_allocated > 0 else 0
    
    # Return calculated metrics
    return {
        'total_active': total_active,
        'utilization_rate': round(utilization_rate, 2),
        'dormancy_rate': round(dormancy_rate, 2),
        'reservation_rate': round(reservation_rate, 2),
        'unaccounted': total_allocated - total_active - inactive - reserved,
    }

def fetch_operator_ndcs(operator_id):
    """Fetch NDCs allocated to a specific operator"""
    # Query the number_allocations collection
    allocations = list(mongo.db.number_allocations.find({'operator_id': operator_id}))
    
    result = []
    for allocation in allocations:
        result.append({
            'ndc': allocation['ndc'],
            'type': allocation['type'],
            'start_block': allocation['start_block'],
            'end_block': allocation['end_block'],
            'total_allocated': allocation['total_allocated']
        })
    
    return result

def aggregate_submission_data(submission_id):
    """Aggregate data from a submission for analytics"""
    # Get the submission data
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return None
    
    # Get all ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))
    
    # Aggregate metrics
    total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
    total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
    total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
    total_inactive = sum(r.get('inactive', 0) for r in ranges)
    total_reserved = sum(r.get('reserved', 0) for r in ranges)
    total_ported_in = sum(r.get('ported_in', 0) for r in ranges)
    total_ported_out = sum(r.get('ported_out', 0) for r in ranges)
    
    # Calculate overall metrics
    total_active = total_active_subscriber + total_active_non_subscriber
    overall_utilization = (total_active / total_allocated * 100) if total_allocated > 0 else 0
    
    return {
        'submission_id': submission_id,
        'operator': submission.get('operator'),
        'period': submission.get('reporting_period'),
        'status': submission.get('status'),
        'submission_date': submission.get('created_at'),
        'metrics': {
            'total_allocated': total_allocated,
            'total_active': total_active,
            'total_active_subscriber': total_active_subscriber,
            'total_active_non_subscriber': total_active_non_subscriber,
            'total_inactive': total_inactive,
            'total_reserved': total_reserved,
            'total_ported_in': total_ported_in,
            'total_ported_out': total_ported_out,
            'utilization_rate': round(overall_utilization, 2),
            'net_porting': total_ported_in - total_ported_out
        },
        'ranges_count': len(ranges)
    }

def get_historical_data(operator_id, limit=5):
    """Get historical submission data for trend analysis"""
    # Get the latest submissions for this operator
    submissions = list(mongo.db.e164_submissions.find(
        {'operator_id': operator_id, 'status': 'approved'}
    ).sort('reporting_period', -1).limit(limit))
    
    results = []
    for submission in submissions:
        # Aggregate data for each submission
        aggregated = aggregate_submission_data(str(submission['_id']))
        if aggregated:
            results.append(aggregated)
    
    return results

def calculate_projected_exhaustion(ndc, growth_rate):
    """Calculate projected exhaustion date for an NDC based on growth rate"""
    # Get the latest data for this NDC
    latest_range = mongo.db.e164_ranges.find_one(
        {'ndc': ndc, 'status': 'approved'},
        sort=[('created_at', -1)]
    )
    
    if not latest_range:
        return None
    
    # Calculate remaining numbers
    total_allocated = latest_range.get('total_allocated', 0)
    total_active = latest_range.get('active_subscriber', 0) + latest_range.get('active_non_subscriber', 0)
    remaining = total_allocated - total_active
    
    # Calculate years until exhaustion (simple linear projection)
    if growth_rate <= 0:
        return float('inf')  # Never exhausts at negative growth
    
    years_until_exhaustion = remaining / (total_active * (growth_rate / 100))
    return round(years_until_exhaustion, 1)

def compare_operators_utilization():
    """Compare utilization rates across operators"""
    # Get the latest submission for each operator
    pipeline = [
        {"$sort": {"created_at": -1}},
        {"$group": {
            "_id": "$operator_id",
            "submission_id": {"$first": "$_id"},
            "operator": {"$first": "$operator"},
            "reporting_period": {"$first": "$reporting_period"},
            "created_at": {"$first": "$created_at"}
        }}
    ]
    
    latest_submissions = list(mongo.db.e164_submissions.aggregate(pipeline))
    
    # Get aggregated data for each submission
    results = []
    for submission in latest_submissions:
        aggregated = aggregate_submission_data(str(submission['submission_id']))
        if aggregated:
            results.append({
                'operator': submission['operator'],
                'period': submission['reporting_period'],
                'utilization_rate': aggregated['metrics']['utilization_rate'],
                'total_allocated': aggregated['metrics']['total_allocated'],
                'total_active': aggregated['metrics']['total_active']
            })
    
    return results

# E.164 Route handlers

@e164_bp.route('/dashboard')
@login_required
def dashboard():
    """E.164 Number Range Audit Dashboard"""
    # Get operator information if the user is associated with an operator
    operator = None
    if not current_user.is_admin:
        operator = mongo.db.operators.find_one({'_id': ObjectId(current_user.vas_id)})
    
    # Get submission history
    if operator:
        # For operators, show only their submissions
        submissions = list(mongo.db.e164_submissions.find(
            {'operator_id': str(operator['_id'])}
        ).sort('created_at', -1).limit(10))
    else:
        # For admins, show all submissions
        submissions = list(mongo.db.e164_submissions.find().sort('created_at', -1).limit(20))
    
    # Process submissions for display
    for submission in submissions:
        submission['_id'] = str(submission['_id'])
        submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    
    # For admin, get operator comparison
    operator_comparison = None
    if current_user.is_admin:
        operator_comparison = compare_operators_utilization()
    
    return render_template(
        'e164/dashboard.html',
        operator=operator,
        submissions=submissions,
        operator_comparison=operator_comparison
    )

@e164_bp.route('/submission/new', methods=['GET'])
@login_required
def new_submission():
    """Render the new submission form"""
    # Check if the user is associated with an operator
    operator = None
    if not current_user.is_admin:
        operator = mongo.db.operators.find_one({'_id': ObjectId(current_user.vas_id)})
        if not operator:
            flash('You are not associated with any operator.', 'danger')
            return redirect(url_for('dashboard'))
    
    # Get NDCs for the operator
    ndcs = []
    if operator:
        ndcs = fetch_operator_ndcs(str(operator['_id']))
    
    # Get reporting periods
    current_year = datetime.now().year
    reporting_periods = [
        f"{current_year}-h1",  # First half of current year
        f"{current_year}-h2",  # Second half of current year
        f"{current_year-1}-h1",  # First half of previous year
        f"{current_year-1}-h2"   # Second half of previous year
    ]
    
    return render_template(
        'e164/submission_form.html',
        operator=operator,
        ndcs=ndcs,
        reporting_periods=reporting_periods
    )

@e164_bp.route('/submission/create', methods=['POST'])
@login_required
def create_submission():
    """Create a new E.164 submission"""
    # Parse request data
    data = request.json
    
    # Validate required fields
    if not data.get('operator_id') or not data.get('reporting_period'):
        return jsonify({'success': False, 'error': 'Missing required fields'}), 400
    
    # Create submission document
    submission = {
        'operator_id': data.get('operator_id'),
        'operator': data.get('operator'),
        'reporting_period': data.get('reporting_period'),
        'contact_person': data.get('contact_person'),
        'contact_email': data.get('contact_email'),
        'notes': data.get('notes'),
        'status': 'pending',  # Initial status is pending
        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc),
        'created_by': current_user.id,
        'updated_by': current_user.id
    }
    
    # Insert submission
    submission_result = mongo.db.e164_submissions.insert_one(submission)
    submission_id = str(submission_result.inserted_id)
    
    # Process ranges
    ranges = data.get('ranges', [])
    for range_data in ranges:
        # Calculate derived metrics
        metrics = calculate_number_metrics(range_data)
        
        # Create range document
        range_doc = {
            'submission_id': submission_id,
            'ndc': range_data.get('ndc'),
            'type': range_data.get('type'),
            'start_block': range_data.get('start_block'),
            'end_block': range_data.get('end_block'),
            'total_allocated': range_data.get('total_allocated'),
            'active_subscriber': range_data.get('active_subscriber'),
            'active_non_subscriber': range_data.get('active_non_subscriber', 0),
            'inactive': range_data.get('inactive'),
            'reserved': range_data.get('reserved', 0),
            'ported_in': range_data.get('ported_in', 0),
            'ported_out': range_data.get('ported_out', 0),
            'gross_addition': range_data.get('gross_addition', 0),
            'net_addition': range_data.get('net_addition', 0),
            'utilization_rate': metrics['utilization_rate'],
            'dormancy_rate': metrics['dormancy_rate'],
            'reservation_rate': metrics['reservation_rate'],
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }
        
        # Insert range
        mongo.db.e164_ranges.insert_one(range_doc)
    
    # Log the activity
    log_activity("E.164 Submission Created", f"User {current_user.contact} created E.164 submission for {data.get('operator')} ({data.get('reporting_period')})")
    
    return jsonify({
        'success': True,
        'submission_id': submission_id
    })

@e164_bp.route('/submission/<submission_id>', methods=['GET'])
@login_required
def view_submission(submission_id):
    """View a specific E.164 submission"""
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard'))
    
    # Check if the user is authorized to view this submission
    if not current_user.is_admin and str(submission.get('operator_id')) != current_user.vas_id:
        flash('You are not authorized to view this submission.', 'danger')
        return redirect(url_for('e164.dashboard'))
    
    # Get the ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))
    
    # Prepare submission for template
    submission['_id'] = str(submission['_id'])
    submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    submission['updated_at'] = submission['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
    
    # Aggregate metrics
    metrics = {
        'total_allocated': sum(r.get('total_allocated', 0) for r in ranges),
        'total_active_subscriber': sum(r.get('active_subscriber', 0) for r in ranges),
        'total_active_non_subscriber': sum(r.get('active_non_subscriber', 0) for r in ranges),
        'total_inactive': sum(r.get('inactive', 0) for r in ranges),
        'total_reserved': sum(r.get('reserved', 0) for r in ranges),
        'total_ported_in': sum(r.get('ported_in', 0) for r in ranges),
        'total_ported_out': sum(r.get('ported_out', 0) for r in ranges)
    }
    
    # Calculate overall metrics
    total_active = metrics['total_active_subscriber'] + metrics['total_active_non_subscriber']
    metrics['total_active'] = total_active
    metrics['overall_utilization'] = round((total_active / metrics['total_allocated'] * 100), 2) if metrics['total_allocated'] > 0 else 0
    
    return render_template(
        'e164/view_submission.html',
        submission=submission,
        ranges=ranges,
        metrics=metrics
    )

@e164_bp.route('/submission/<submission_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_submission(submission_id):
    """Approve an E.164 submission"""
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return jsonify({'success': False, 'error': 'Submission not found'}), 404
    
    # Update submission status
    mongo.db.e164_submissions.update_one(
        {'_id': ObjectId(submission_id)},
        {
            '$set': {
                'status': 'approved',
                'approved_at': datetime.now(timezone.utc),
                'approved_by': current_user.id,
                'updated_at': datetime.now(timezone.utc),
                'updated_by': current_user.id
            }
        }
    )
    
    # Also update status on all ranges in this submission
    mongo.db.e164_ranges.update_many(
        {'submission_id': submission_id},
        {
            '$set': {
                'status': 'approved',
                'updated_at': datetime.now(timezone.utc)
            }
        }
    )
    
    # Log the activity
    log_activity("E.164 Submission Approved", f"User {current_user.contact} approved E.164 submission {submission_id} for {submission.get('operator')}")
    
    # Send notification to operator
    add_notification(
        f"Your E.164 submission for {submission.get('reporting_period')} has been approved.",
        submission.get('operator_id')
    )
    
    return jsonify({'success': True})

@e164_bp.route('/submission/<submission_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_submission(submission_id):
    """Reject an E.164 submission with comments"""
    # Get request data
    data = request.json
    comments = data.get('comments', 'No comments provided')
    
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return jsonify({'success': False, 'error': 'Submission not found'}), 404
    
    # Update submission status
    mongo.db.e164_submissions.update_one(
        {'_id': ObjectId(submission_id)},
        {
            '$set': {
                'status': 'rejected',
                'rejection_comments': comments,
                'rejected_at': datetime.now(timezone.utc),
                'rejected_by': current_user.id,
                'updated_at': datetime.now(timezone.utc),
                'updated_by': current_user.id
            }
        }
    )
    
    # Also update status on all ranges in this submission
    mongo.db.e164_ranges.update_many(
        {'submission_id': submission_id},
        {
            '$set': {
                'status': 'rejected',
                'updated_at': datetime.now(timezone.utc)
            }
        }
    )
    
    # Log the activity
    log_activity("E.164 Submission Rejected", f"User {current_user.contact} rejected E.164 submission {submission_id} for {submission.get('operator')}")
    
    # Send notification to operator
    add_notification(
        f"Your E.164 submission for {submission.get('reporting_period')} has been rejected. Reason: {comments}",
        submission.get('operator_id')
    )
    
    return jsonify({'success': True})

@e164_bp.route('/analytics')
@login_required
def analytics():
    """E.164 Number Range Analytics Dashboard"""
    # For operators, show only their data
    operator_id = None
    if not current_user.is_admin:
        operator_id = current_user.vas_id
    
    # Get overall statistics
    if operator_id:
        # Get latest submission for this operator
        latest_submission = mongo.db.e164_submissions.find_one(
            {'operator_id': operator_id, 'status': 'approved'},
            sort=[('reporting_period', -1)]
        )
        
        if latest_submission:
            latest_data = aggregate_submission_data(str(latest_submission['_id']))
            historical_data = get_historical_data(operator_id)
        else:
            latest_data = None
            historical_data = []
    else:
        # For admins, get data across all operators
        operator_data = compare_operators_utilization()
        
        # Get overall metrics
        total_allocated = sum(op.get('total_allocated', 0) for op in operator_data)
        total_active = sum(op.get('total_active', 0) for op in operator_data)
        
        latest_data = {
            'metrics': {
                'total_allocated': total_allocated,
                'total_active': total_active,
                'utilization_rate': round((total_active / total_allocated * 100), 2) if total_allocated > 0 else 0
            }
        }
        
        # Get historical trends - this would need to be calculated differently for admin view
        historical_data = []
    
    # Process data for charts
    chart_data = {
        'labels': [],
        'utilization': [],
        'dormancy': [],
        'reservation': []
    }
    
    for item in historical_data:
        chart_data['labels'].append(item['period'])
        chart_data['utilization'].append(item['metrics']['utilization_rate'])
        chart_data['dormancy'].append(100 - item['metrics']['utilization_rate'])
        # You would add more metrics here based on your data model
    
    return render_template(
        'e164/analytics.html',
        latest_data=latest_data,
        historical_data=historical_data,
        chart_data=chart_data,
        is_admin=current_user.is_admin
    )

@e164_bp.route('/api/operator/<operator_id>/ndcs')
@login_required
def get_operator_ndcs_api(operator_id):
    """API to get NDCs for a specific operator"""
    ndcs = fetch_operator_ndcs(operator_id)
    return jsonify(ndcs)

@e164_bp.route('/api/submissions/<reporting_period>')
@login_required
@admin_required
def get_submissions_by_period(reporting_period):
    """API to get all submissions for a specific reporting period"""
    submissions = list(mongo.db.e164_submissions.find({'reporting_period': reporting_period}))
    
    # Process submissions
    for submission in submissions:
        submission['_id'] = str(submission['_id'])
        submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        
        # Fetch aggregated metrics
        metrics = aggregate_submission_data(str(submission['_id']))
        if metrics:
            submission['metrics'] = metrics['metrics']
    
    return jsonify(submissions)

@e164_bp.route('/api/analytics/operator-comparison')
@login_required
@admin_required
def get_operator_comparison():
    """API to get comparison data for all operators"""
    comparison = compare_operators_utilization()
    return jsonify(comparison)

@e164_bp.route('/api/analytics/ndc-utilization')
@login_required
def get_ndc_utilization():
    """API to get utilization data by NDC"""
    # Get operator restriction if applicable
    operator_id = None
    if not current_user.is_admin:
        operator_id = current_user.vas_id
    
    # Construct the database query
    match_stage = {}
    if operator_id:
        # Get latest submission ID for this operator
        latest_submission = mongo.db.e164_submissions.find_one(
            {'operator_id': operator_id, 'status': 'approved'},
            sort=[('reporting_period', -1)]
        )
        
        if not latest_submission:
            return jsonify([])
            
        match_stage['submission_id'] = str(latest_submission['_id'])
    else:
        # For admin, get the latest submission for each operator
        # This requires a more complex aggregation
        latest_submissions = mongo.db.e164_submissions.aggregate([
            {"$match": {"status": "approved"}},
            {"$sort": {"reporting_period": -1}},
            {"$group": {
                "_id": "$operator_id",
                "submission_id": {"$first": "$_id"}
            }}
        ])
        
        submission_ids = [str(sub['submission_id']) for sub in latest_submissions]
        if not submission_ids:
            return jsonify([])
            
        match_stage['submission_id'] = {"$in": submission_ids}
    
    # Get utilization data for each NDC
    utilization_data = list(mongo.db.e164_ranges.find(
        match_stage,
        {
            'ndc': 1,
            'type': 1, 
            'total_allocated': 1,
            'active_subscriber': 1,
            'active_non_subscriber': 1,
            'utilization_rate': 1
        }
    ))
    
    # Process data
    result = []
    for item in utilization_data:
        item['_id'] = str(item['_id'])
        result.append(item)
    
    return jsonify(result)

# Register the blueprint with the app
def register_e164_blueprint(app):
    app.register_blueprint(e164_bp)