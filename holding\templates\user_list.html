{% extends "base.html" %}
{% block content %}
<div class="glass-card animated">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h3>User Management</h3>
        <a href="{{ url_for('e164.create_user') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>Create New User
        </a>
    </div>
    
    <div class="table-responsive">
        <table class="table table-hover">
            <thead>
                <tr>
                    <th>Username</th>
                    <th>Email</th>
                    <th>Type</th>
                    <th>Organization</th>
                    <th>Created</th>
                </tr>
            </thead>
            <tbody>
                {% for user in users %}
                <tr>
                    <td>{{ user.contact }}</td>
                    <td>{{ user.email }}</td>
                    <td>
                        {% if user.is_admin %}
                            <span class="badge bg-danger">Admin</span>
                        {% elif user.operator_id %}
                            <span class="badge bg-primary">Operator</span>
                        {% elif user.vas_id %}
                            <span class="badge bg-success">VAS</span>
                        {% else %}
                            <span class="badge bg-secondary">Regular</span>
                        {% endif %}
                    </td>
                    <td>
                        {% if user.operator_name %}
                            {{ user.operator_name }}
                        {% elif user.vas_name %}
                            {{ user.vas_name }}
                        {% else %}
                            -
                        {% endif %}
                    </td>
                    <td>{{ user.created_at|datetime }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}