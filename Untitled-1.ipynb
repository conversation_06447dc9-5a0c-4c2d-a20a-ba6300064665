{"cells": [{"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pinged your deployment. You successfully connected to MongoDB!\n"]}], "source": ["\n", "from pymongo.mongo_client import MongoClient\n", "from dotenv import load_dotenv\n", "import json\n", "import openai\n", "import os\n", "\n", "\n", "\n", "openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))\n", "\n", "\n", "uri = \"mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0\"\n", "\n", "# Create a new client and connect to the server\n", "client = MongoClient(uri)\n"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{\n", "    \"collections\": [\"pr_status\", \"app\"]\n", "}\n", "['pr_status', 'app']\n"]}], "source": ["\n", "app_schema = {\n", "  \"_id\": {\n", "    \"$oid\": \"ObjectId\"\n", "  },\n", "  \"sid\": \"Integer\",\n", "  \"applicant\": \"String\",\n", "  \"email\": \"String\",\n", "  \"phone\": \"String\",\n", "  \"contact\": \"String\",\n", "  \"address\": \"String\",\n", "  \"vasp\": \"String\",\n", "  \"tipe\": \"String\",                # Indicates the type of number (e.g., \"shortcode\", \"tollfree\", \"premium-rate\")\n", "  \"snr\": \"String\",                 # Special Numbering Resource (SNR) assigned to the applicant\n", "  \"aircheck\": \"String\",            # (\"on\"/\"off\") whether the number is accessible via AirtelTigo\n", "  \"glocheck\": \"String\",            # (\"on\"/\"off\") whether the number is accessible via Glo\n", "  \"mtncheck\": \"String\",            # (\"on\"/\"off\") whether the number is accessible via MTN\n", "  \"vodacheck\": \"String\",           # (\"on\"/\"off\") whether the number is accessible via Vodafone\n", "  \"voicecheck\": \"String\",          # (\"on\"/\"off\") whether voice services are available\n", "  \"ussdcheck\": \"String\",           # (\"on\"/\"off\") whether USSD services are available\n", "  \"smscheck\": \"String\",            # (\"on\"/\"off\") whether SMS services are available\n", "  \"odacheck\": \"String\",            # (\"on\"/\"off\") whether other data applications are available\n", "  \"purp\": \"String\",                # Purpose of the SNR\n", "  \"date\": \"String\",                # Date of application in MM/DD/YYYY format\n", "  \"status\": \"String\",              # Status of the application (e.g., \"Certificate_printable\")\n", "  \"last_updated\": \"String\",        # Timestamp of last update in human-readable format\n", "  \"years\": \"String\",               # Duration for which the SNR is valid\n", "  \"uploader\": \"String\",            # Name of the person who uploaded the application\n", "  \"comments\": [\n", "    \"String\"                       # List of comments regarding the application\n", "  ],\n", "  \"reviewer\": \"String\",            # Name of the reviewer of the application\n", "  \"cert_date\": \"String\",           # Certification date in a human-readable format\n", "  \"cert_expiry\": \"String\",         # Certification expiry date in a human-readable format\n", "  \"clicks\": \"Integer\",             # Number of clicks or interactions with the application\n", "  \"updated_by\": \"String\"           # Name of the person who last updated the record\n", "}\n", "\n", "snr_status_schema = {\n", "  \"_id\": {\n", "    \"$oid\": \"ObjectId\"\n", "  },\n", "  \"SNR\": \"String\",                  # Shortcode Special Numbering Resource (SNR) from 100 to 699999\n", "  \"STATUS\": \"String\",              # Status of the SNR ( one of the following, \"AVAILABLE, EXPIRED, ASSIGNED OR RESERVED\" )\n", "  \"EXPIRY\": \"String\",              # Expiry date of the SNR in the format (mm/dd/yyyy) or \"\" if not applicable\n", "  \"ASSIGNEE\": \"String\"             # Name of the assignee of the SNR or \"\" if not assigned\n", "}\n", "\n", "tf_status_schema = {\n", "  \"_id\": {\n", "    \"$oid\": \"ObjectId\"\n", "  },\n", "  \"SNR\": \"String\",                  # Tollfree Special Numbering Resource (SNR) from 800000000 to 800999999. The preceding'0' before each number is omitted here but appended during processing.\"\n", "  \"STATUS\": \"String\",              # Status of the SNR ( one of the following, \"AVAILABLE, EXPIRED, ASSIGNED OR RESERVED\" )\n", "  \"EXPIRY\": \"String\",              # Expiry date of the SNR in the format (mm/dd/yyyy) or \"\" if not applicable\n", "  \"ASSIGNEE\": \"String\"             # Name of the assignee of the SNR or \"\" if not assigned\n", "}\n", "\n", "pr_status_schema = {\n", "  \"_id\": {\n", "    \"$oid\": \"ObjectId\"\n", "  },\n", "  \"SNR\": \"String\",                 # Premium-rate Special Numbering Resource (SNR) from 900000000 to 900999999. The preceding'0' before each number is omitted here but appended during processing.\"\n", "  \"STATUS\": \"String\",              # Status of the SNR ( one of the following, \"AVAILABLE, EXPIRED, ASSIGNED OR RESERVED\" )\n", "  \"EXPIRY\": \"String\",              # Expiry date of the SNR in the format (mm/dd/yyyy) or \"\" if not applicable\n", "  \"ASSIGNEE\": \"String\"             # Name of the assignee of the SNR or \"\" if not assigned\n", "}\n", "\n", "\n", "query = \"1090\"\n", "\"\"\"\n", "Sends the user query to GPT-4o-mini to determine the collections to search.\n", "Returns a list of collection names.\n", "\"\"\"\n", "prompt = f\"\"\"\n", "You are an assistant helping to interpret user search queries for SNR applications.\n", "\n", "\n", "Determine the best collection(s) to query based on the information from the user's query and schema descriptions provided below. The MongoDB collections to search based on the extracted information are as follows:\n", "-'snr_status' for shortcodes (3 digit to 6 digit numbers) only, \n", "-'tf_status'(toll-free numbers with the prefix 0800 and 10 digits in total) for toll-free numbers only\n", "-'pr_status' for premium-rate numbers ( numbers with the prefix 0900 and 10 digits in total) only\n", "- 'app' for all application history and information, which also iclude details of the shortcode, toll-free, and premium-rate numbers.\n", "'pr_status' for premium-rate numbers only, or 'app' for all application history and information, which also include details of the shortcode, toll-free, and premium-rate numbers.\n", "\n", "\n", "User Query: \"{query}\"\n", "\n", "Collections schema: \"{snr_status_schema}\", \"{tf_status_schema}\", \"{pr_status_schema}\", \"{app_schema}\"\n", "\n", "Provide the output in JSON format as follows:\n", "\n", "{{\n", "    \"collections\": [...]\n", "}}\n", "\"\"\"\n", "\n", "\n", "response = openai_client.chat.completions.create(\n", "    model=\"gpt-4o-mini\",  # Use GPT-4o-mini API for the task\n", "    messages=[\n", "        {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "        {\"role\": \"user\", \"content\": prompt}\n", "    ],\n", "    temperature=0,  # For deterministic output\n", "    response_format={\"type\": \"json_object\"}\n", ")\n", "\n", "reply = response.choices[0].message.content.strip()\n", "print(reply)\n", "# Parse the JSON output\n", "search_params = json.loads(reply)\n", "\n", "print(search_params.get('collections', [])) \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'openai_client'", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[1], line 5\u001b[0m\n\u001b[0;32m      3\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m datetime\n\u001b[0;32m      4\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;<PERSON><PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mobjectid\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ObjectId\n\u001b[1;32m----> 5\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01mo<PERSON>ai_client\u001b[39;00m\n\u001b[0;32m      7\u001b[0m \u001b[38;5;66;03m# Helper Function: Determine Collections to Search Programmatically\u001b[39;00m\n\u001b[0;32m      8\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21mdetermine_collections\u001b[39m(query):\n", "\u001b[1;31mModuleNotFoundError\u001b[0m: No module named 'openai_client'"]}], "source": ["import re\n", "import json\n", "from datetime import datetime\n", "from bson.objectid import ObjectId\n", "import openai_client\n", "\n", "# Helper Function: Determine Collections to Search Programmatically\n", "def determine_collections(query):\n", "    \"\"\"\n", "    Determines the MongoDB collections to search based on the user's query.\n", "    Returns a list of collection names.\n", "    \"\"\"\n", "    # Convert query to lowercase for easier keyword searching\n", "    query = query.lower()\n", "    collections = ['applications']\n", "\n", "    # Keywords for determining collections\n", "    if re.search(r'\\bshortcode\\b|\\b3 digit\\b|\\b4 digit\\b|\\b5 digit\\b|\\b6 digit\\b|^[0-9]{3,6}$', query):\n", "        collections.append('snr_status')\n", "\n", "    if re.search(r'\\btoll-?free\\b|\\b0800\\b|^0800[0-9]{6}$', query):\n", "        collections.append('tf_status')\n", "\n", "    if re.search(r'\\bpremium-?rate\\b|\\b0900\\b|^0900[0-9]{6}$', query):\n", "        collections.append('pr_status')\n", "\n", "    # If the query contains general terms like \"application\", \"history\", \"details\", or an applicant's name\n", "    if re.search(r'\\bapplication\\b|\\bhistory\\b|\\bapplicant\\b|\\bdetails\\b|^[0-9]{3,6}$', query):\n", "        collections.append('applications')\n", "\n", "    # If none of the specific collections are found, default to 'applications' for a general query\n", "    if not collections:\n", "        collections.append('applications')\n", "\n", "    # Include 'snrs' collection to ensure completeness of SNR data\n", "    collections.append('snrs')\n", "\n", "    return list(set(collections))  # Remove duplicates and return the list of collections to search\n", "\n", "# Helper Function: Create MongoDB Queries with GPT-4o-mini\n", "def create_mongodb_queries_with_gpt(query, collections):\n", "    \"\"\"\n", "    Sends the user query and collections to GPT-4o-mini to generate MongoDB queries.\n", "    Returns a list of MongoDB query dictionaries.\n", "    \"\"\"\n", "    prompt = f\"\"\"\n", "    You are an assistant helping to create MongoDB queries based on the user's search intent. First, explain your thought process clearly based on the user’s query. Then generate the MongoDB query based on that explanation.\n", "    - The 'SNR' field is stored as a string in the database.\n", "    - If the user's query involves numeric ranges, you should use a regex query to ensure proper results for string-based values.\n", "    - Generate MongoDB queries that accurately reflect the user's search while handling type mismatches appropriately.\n", "\n", "    Collections to be queried:\n", "    - 'snr_status' (shortcodes, including fields: SNR, STATUS, EXPIRY, ASSIGNEE)\n", "    - 'tf_status' (toll-free numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE)\n", "    - 'pr_status' (premium-rate numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE)\n", "    - 'applications' (application history, including fields: applicant_id, user_id, applicant_name, application_date, status, remarks, created_at, updated_at, certificate_expiry_date, certificate_generated, certificate_start_date, processed_by)\n", "    - 'snrs' (SNR-specific data, including fields: application_id, snr, type, purpose, status, certificate_id, created_at, updated_at)\n", "\n", "    User Query: \"{query}\"\n", "    Collections: {collections}\n", "\n", "    Based on the user's query, provide the MongoDB queries for each relevant collection.\n", "\n", "    Your response should only be in JSON format:\n", "    {{\n", "        \"queries\": {{\n", "            \"snr_status\": <MongoDB query for snr_status>,\n", "            \"tf_status\": <MongoDB query for tf_status>,\n", "            \"pr_status\": <MongoDB query for pr_status>,\n", "            \"applications\": <MongoDB query for applications>,\n", "            \"snrs\": <MongoDB query for snrs>\n", "        }},\n", "        \"limit\": <Maximum number of results to return based on the query or 10 if not specified in the query>\n", "    }}\n", "\n", "    - If a query involves numeric ranges for SNR but the SNR field is a string, use a regex pattern to reflect the range.\n", "    - If a collection is not relevant to the query, set its value to `null`.\n", "    Explain your reasoning and formulate the appropriate query.\n", "    \"\"\"\n", "\n", "    try:\n", "        response = openai_client.chat.completions.create(\n", "            model=\"gpt-4o-mini\",  # Use GPT-4o-mini API for the task\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "                {\"role\": \"user\", \"content\": prompt}\n", "            ],\n", "            temperature=0,  # For deterministic output\n", "            response_format={\"type\": \"json_object\"}\n", "        )\n", "\n", "        # Extract the assistant's reply\n", "        reply = response.choices[0].message.content.strip()\n", "        print('reply', reply)  # Print the reply for debugging purposes\n", "        # Parse the JSON output\n", "        query_data = json.loads(reply)\n", "\n", "        return query_data\n", "\n", "    except Exception as e:\n", "        print(f\"Error creating MongoDB queries with GPT: {e}\")\n", "        return {}\n", "\n", "# Iterative Feedback Loop for Query Refinement\n", "def refine_query_with_feedback(initial_query, collections):\n", "    \"\"\"\n", "    Uses an iterative feedback loop to refine the generated query if the initial one returns no results.\n", "    \"\"\"\n", "    response = create_mongodb_queries_with_gpt(initial_query, collections)\n", "    query_data = response.get('queries', {})\n", "\n", "    # Execute the MongoDB query\n", "    results = execute_mongo_query(query_data)\n", "    if not results:\n", "        # If no results, refine the query with additional context\n", "        refined_prompt = f\"\"\"\n", "        \n", "        The initial MongoDB query returned no results or incorrect results.\n", "        Please refine the query based on the fact that the 'SNR' field is stored as a string, and the numeric range must be handled accordingly. \n", "        First, explain your thought process clearly based on the user’s query. Then generate the MongoDB query based on that explanation.\n", "        User Query: \"{initial_query}\"\n", "        Collections: {collections}\n", "        Explain your reasoning and formulate the appropriate query.\n", "        \"\"\"\n", "        response = openai_client.chat.completions.create(\n", "            model=\"gpt-4o-mini\",  # Use GPT-4o-mini API for the task\n", "            messages=[\n", "                {\"role\": \"system\", \"content\": \"You are a helpful assistant.\"},\n", "                {\"role\": \"user\", \"content\": refined_prompt}\n", "            ],\n", "            temperature=0,  # For deterministic output\n", "            response_format={\"type\": \"json_object\"}\n", "        )\n", "\n", "        # Extract the assistant's reply\n", "        reply = response.choices[0].message.content.strip()\n", "        print('refined reply', reply)  # Print the refined reply for debugging purposes\n", "        query_data = json.loads(reply)\n", "\n", "    return query_data\n", "\n", "# Helper Function: Execute MongoDB Query\n", "def execute_mongo_query(query_data):\n", "    \"\"\"\n", "    Executes the provided MongoDB query data.\n", "    \"\"\"\n", "    results = []\n", "    # Iterate through the collections and execute queries\n", "    for collection, query in query_data.items():\n", "        if query:\n", "            try:\n", "                collection_ref = mongo.db[collection]\n", "                result = list(collection_ref.find(query).limit(query_data.get('limit', 10)))\n", "                results.extend(result)\n", "            except Exception as e:\n", "                print(f\"Error executing query on collection {collection}: {e}\")\n", "                # Improved error handling\n", "                log_activity(\"Query Execution Error\", f\"Failed to execute query on collection {collection}: {str(e)}\")\n", "    return results\n", "\n", "# Helper Function: <PERSON><PERSON> and <PERSON><PERSON><PERSON> Duplicates\n", "def merge_and_remove_duplicates(applications_data, snrs_data):\n", "    \"\"\"\n", "    Merges data from the 'applications' and 'snrs' collections, removing duplicates based on 'application_id'.\n", "    \"\"\"\n", "    merged_data = {}\n", "\n", "    # Merge applications data\n", "    for app in applications_data:\n", "        app_id = app.get('application_id')\n", "        if app_id not in merged_data:\n", "            merged_data[app_id] = app\n", "\n", "    # Merge SNRs data\n", "    for snr in snrs_data:\n", "        app_id = snr.get('application_id')\n", "        if app_id not in merged_data:\n", "            merged_data[app_id] = snr\n", "        else:\n", "            # Merge the SNR-specific data into the application data\n", "            if 'snr_data' in merged_data[app_id]:\n", "                merged_data[app_id]['snr_data'].append(snr)\n", "            else:\n", "                merged_data[app_id]['snr_data'] = [snr]\n", "\n", "    # Convert merged data to a list\n", "    return list(merged_data.values())\n", "\n", "# Usage Example\n", "initial_query = \"i want an available 4 digit number between 1000 and 1999\"\n", "collections = determine_collections(initial_query)\n", "final_query_data = refine_query_with_feedback(initial_query, collections)\n", "search_results = execute_mongo_query(final_query_data)\n", "merged_results = merge_and_remove_duplicates([], search_results)\n", "\n", "# Log or display results\n", "print(merged_results)\n"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["from werkzeug.security import generate_password_hash, check_password_hash\n", "\n", "hashed = \"scrypt:32768:8:1$JIp3Nu1tjP5VmB6a$79989a317fcf3f7e55372d9f7c535ff985e999f496317511fd105c3e5403d58702a4d0fbfd5259855022a1efd876434e7a72c54e2923f3e43ffd654573f966d0\"\n", "check_password_hash(hashed, 'NiggaTron38')"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import os\n", "\n", "oti = pd.read_excel(r\"C:\\Users\\<USER>\\Downloads\\OTI WORST PERFORMANING DISTRICTS - MTN.xlsx\", sheet_name='Sheet1')\n", "\n", "oti.columns = oti.columns.str.strip()\n", "oti.head()\n", "\n", "# filter out rows where 'Cells' is  'OKAGYAKROM_3 '\n", "okdf = oti[oti['Cells']==' OKAGYAKROM_3 ']\n", "# convert the Day column to datetime format with day of the week\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\3767216711.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['datetime'] = pd.to_datetime(okdf['Day'])\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\3767216711.py:2: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['datetime'].dt.day_name()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\3767216711.py:3: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf[\"CALL SETUP SUCCESS RATE\"] = okdf[\"CALL SETUP SUCCESS RATE\"].astype(str).str.strip()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\3767216711.py:4: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf[\"CALL SETUP SUCCESS RATE\"] = pd.to_numeric(okdf[\"CALL SETUP SUCCESS RATE\"])\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\3767216711.py:6: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf[\"CALL DROP RATE\"] = pd.to_numeric(okdf[\"CALL DROP RATE\"].astype(str).str.strip())\n"]}, {"data": {"text/plain": ["Day                                object\n", "Regions                            object\n", "Districts                          object\n", "Cells                              object\n", "CALL SETUP SUCCESS RATE           float64\n", "CALL DROP RATE                    float64\n", "CELL DOWNTIME (H)                  object\n", "datetime                   datetime64[ns]\n", "Day_of_week                        object\n", "dtype: object"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["okdf['datetime'] = pd.to_datetime(okdf['Day'])\n", "okdf['Day_of_week'] = okdf['datetime'].dt.day_name()\n", "okdf[\"CALL SETUP SUCCESS RATE\"] = okdf[\"CALL SETUP SUCCESS RATE\"].astype(str).str.strip()\n", "okdf[\"CALL SETUP SUCCESS RATE\"] = pd.to_numeric(okdf[\"CALL SETUP SUCCESS RATE\"]) \n", "\n", "okdf[\"CALL DROP RATE\"] = pd.to_numeric(okdf[\"CALL DROP RATE\"].astype(str).str.strip()) \n", "# .str.replace(\"%\", \"\").astype(float)\n", "okdf.dtypes"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>CALL SETUP SUCCESS RATE</th>\n", "      <th>CALL DROP RATE</th>\n", "      <th>datetime</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>58.000000</td>\n", "      <td>58.000000</td>\n", "      <td>58</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>64.528024</td>\n", "      <td>2.109358</td>\n", "      <td>2025-01-01 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>38.191000</td>\n", "      <td>1.088440</td>\n", "      <td>2024-12-01 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>53.997250</td>\n", "      <td>1.482167</td>\n", "      <td>2024-12-16 06:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>66.109650</td>\n", "      <td>2.133045</td>\n", "      <td>2024-12-30 12:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>73.661100</td>\n", "      <td>2.622010</td>\n", "      <td>2025-01-14 18:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>88.316500</td>\n", "      <td>4.047220</td>\n", "      <td>2025-02-12 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>12.774250</td>\n", "      <td>0.693880</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       CALL SETUP SUCCESS RATE  CALL DROP RATE             datetime\n", "count                58.000000       58.000000                   58\n", "mean                 64.528024        2.109358  2025-01-01 00:00:00\n", "min                  38.191000        1.088440  2024-12-01 00:00:00\n", "25%                  53.997250        1.482167  2024-12-16 06:00:00\n", "50%                  66.109650        2.133045  2024-12-30 12:00:00\n", "75%                  73.661100        2.622010  2025-01-14 18:00:00\n", "max                  88.316500        4.047220  2025-02-12 00:00:00\n", "std                  12.774250        0.693880                  NaN"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["okdf.describe()"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\483987017.py:1: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['graph_date'] = okdf['datetime'].astype(str) + ' ' + okdf['Day_of_week'].astype(str)\n"]}], "source": ["okdf['graph_date'] = okdf['datetime'].astype(str) + ' ' + okdf['Day_of_week'].astype(str)\n", "# okdf['graph_date'] = pd.to_datetime(okdf['graph_date'])\n", "# okdf['graph_date'] = okdf['graph_date'].dt.strftime('%Y-%m-%d %H:%M:%S')\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"image/png": "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************************************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***************************************************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", "text/plain": ["<Figure size 715x500 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import matplotlib.pyplot as plt\n", "\n", "#let's make a pairplot to visualize the call success rate and call drop rate for the 'OKAGYAKROM_3' cell on each day of the week\n", "\n", "import seaborn as sns\n", "\n", "ltd_df = okdf[['graph_date', 'CALL SETUP SUCCESS RATE', 'CALL DROP RATE']]\n", "\n", "sns.pairplot(ltd_df, hue='graph_date', palette='viridis')\n", "plt.show()\n", "\n", "\n", "\n", "# for day in okdf['Day_of_week'].unique():\n", "#     day_df = okdf[okdf['Day_of_week'] == day]\n", "#     plt.figure(figsize=(10, 6)) \n", "#     plt.plot(day_df['graph_date'], day_df['CALL SETUP SUCCESS RATR'], marker='o', linestyle='-', label=day) \n", "#     plt.title(f'CALL SETUP SUCCESS RATE for OKAGYAKROM_3 on {day}') \n", "#     plt.xlabel('Date') \n", "#     plt.ylabel('CALL SETUP SUCCESS RATE') \n", "#     plt.xticks(rotation=45) \n", "#     plt.grid(True) \n", "#     plt.legend()\n", "#     plt.show()\n", "\n", "\n", "# plt.figure(figsize=(10, 6))\n", "# plt.plot(okdf['graph_date'], okdf['CALL SETUP SUCCESS RATE'], marker='o', linestyle='-', color='b')\n", "\n", "# plt.title('CALL SETUP SUCCESS RATE for OKAGYAKROM_3 over time')\n", "# plt.xlabel('Date')\n", "# plt.ylabel('CALL SETUP SUCCESS RATE')\n", "\n", "# plt.xticks(rotation=45)\n", "\n", "# plt.grid(True)"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:5: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = pd.Categorical(okdf['Day_of_week'], categories=['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'], ordered=True)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:8: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['Day_of_week'].cat.codes + 1\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:10: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['Day_of_week'].astype(int)\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:12: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['Day_of_week'].replace({1:'Monday', 2:'Tuesday', 3:'Wednesday', 4:'Thursday', 5:'Friday', 6:'Saturday', 7:'Sunday'})\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:14: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['Day_of_week'].astype('category')\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_28520\\45349768.py:17: SettingWithCopyWarning: \n", "A value is trying to be set on a copy of a slice from a DataFrame.\n", "Try using .loc[row_indexer,col_indexer] = value instead\n", "\n", "See the caveats in the documentation: https://pandas.pydata.org/pandas-docs/stable/user_guide/indexing.html#returning-a-view-versus-a-copy\n", "  okdf['Day_of_week'] = okdf['Day_of_week'].cat.reorder_categories(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])\n"]}], "source": ["#let's bin the data into 3 bins to see which days of the week are usually poor in terms of call setup success rate and plot the histogram\n", "\n", "\n", "\n", "okdf['Day_of_week'] = pd.Categorical(okdf['Day_of_week'], categories=['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'], ordered=True)\n", "\n", "\n", "okdf['Day_of_week'] = okdf['Day_of_week'].cat.codes + 1   \n", "\n", "okdf['Day_of_week'] = okdf['Day_of_week'].astype(int)\n", "\n", "okdf['Day_of_week'] = okdf['Day_of_week'].replace({1:'Monday', 2:'Tuesday', 3:'Wednesday', 4:'Thursday', 5:'Friday', 6:'Saturday', 7:'Sunday'})\n", "\n", "okdf['Day_of_week'] = okdf['Day_of_week'].astype('category')  \n", "\n", "\n", "okdf['Day_of_week'] = okdf['Day_of_week'].cat.reorder_categories(['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'])"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# sort the call setup success rate column from low to high and bin the data into 3 bins to see which days of the week are usually poor in terms of call setup success rate and plot the histogram\n", "\n", "\n", "\n", "\n", "okdf['Call_Setup_Success_Rate_Bin'] = pd.cut(okdf['CALL SETUP SUCCESS RATE'].sort_values(), bins=3, labels=['Low', 'Medium', 'High'])\n", "# display the first few rows of the dataframe to verify the changes\n", "\n", "okdf.head()"]}, {"cell_type": "code", "execution_count": 50, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'queries': {'snr_status': {'SNR': {'$in': ['2121']}, 'ASSIGNEE': {'$regex': 'check|availability', '$options': 'i'}}, 'snrs': {'snr': {'$in': ['2121']}, 'purpose': {'$regex': 'check|availability', '$options': 'i'}}}, 'limit': 10}\n", "{'queries': {'snr_status': {'SNR': {'$in': ['2020']}, 'ASSIGNEE': {'$regex': 'check|availability', '$options': 'i'}}, 'snrs': {'snr': {'$in': ['2020']}, 'purpose': {'$regex': 'check|availability', '$options': 'i'}}}, 'limit': 10}\n", "{'queries': {'snr_status': {'SNR': {'$in': ['3023']}, 'ASSIGNEE': {'$regex': 'check|availability', '$options': 'i'}}, 'snrs': {'snr': {'$in': ['3023']}, 'purpose': {'$regex': 'check|availability', '$options': 'i'}}}, 'limit': 10}\n"]}], "source": ["import re\n", "def determine_collections(query):\n", "    \"\"\"\n", "    Determines the MongoDB collections to search based on the user's query.\n", "    Returns a list of collection names.\n", "    \"\"\"\n", "    # Convert query to lowercase for easier keyword searching\n", "    query = query.lower()\n", "    collections = ['applications']\n", "\n", "    # Keywords for determining collections\n", "    if re.search(r'\\bshortcode\\b|\\b3 digit\\b|\\b4 digit\\b|\\b5 digit\\b|\\b6 digit\\b|^[0-9]{3,6}$', query):\n", "        collections.append('snr_status')\n", "\n", "    if re.search(r'\\btoll-?free\\b|\\b0800\\b|^0800[0-9]{6}$', query):\n", "        collections.append('tf_status')\n", "\n", "    if re.search(r'\\bpremium-?rate\\b|\\b0900\\b|^0900[0-9]{6}$', query):\n", "        collections.append('pr_status')\n", "\n", "    # If the query contains general terms like \"application\", \"history\", \"details\", or an applicant's name\n", "    if re.search(r'\\bapplication\\b|\\bhistory\\b|\\bapplicant\\b|\\bdetails\\b|^[0-9]{3,6}$', query):\n", "        collections.append('applications')\n", "\n", "    # If none of the specific collections are found, default to 'applications' for a general query\n", "    if not collections:\n", "        collections.append('applications')\n", "\n", "    # Include 'snrs' collection to ensure completeness of SNR data\n", "    collections.append('snrs')\n", "\n", "    return list(set(collections))  # Remove duplicates and return the list of collections to search\n", "\n", "def create_mongodb_queries(query, collections):\n", "    \"\"\"Create MongoDB queries based on search patterns and collection schemas\"\"\"\n", "    # Base query structure\n", "    queries = {\n", "        \"snr_status\": None,\n", "        \"tf_status\": None,\n", "        \"applications\": None,\n", "        \"pr_status\": None,\n", "        \"snrs\": None\n", "    }\n", "    \n", "    limit = 10  # De<PERSON>ult limit\n", "    \n", "    # Common patterns\n", "    number_pattern = r'\\b(\\d{3,6}|[08]00\\d{6,7})\\b'\n", "    status_pattern = r'\\b(available|assigned|expired|reserved)\\b'\n", "    date_pattern = r'\\b(\\d{4}-\\d{2}-\\d{2})\\b'\n", "\n", "    # Extract key components from query\n", "    numbers = re.findall(number_pattern, query, re.IGNORECASE)\n", "    statuses = re.findall(status_pattern, query, re.IGNORECASE)\n", "    dates = re.findall(date_pattern, query)\n", "    \n", "    # Generic text search for names/companies\n", "    text_terms = [term for term in re.split(r'\\W+', query) \n", "                 if len(term) > 3 and not term.isdigit()]\n", "\n", "    # Build queries for each collection\n", "    for collection in collections:\n", "        # Status collections (snr_status, tf_status, pr_status)\n", "        if collection.endswith('_status'):\n", "            status_query = {}\n", "            if numbers:\n", "                status_query['SNR'] = {'$in': numbers}\n", "            if statuses:\n", "                status_query['STATUS'] = {'$in': [s.upper() for s in statuses]}\n", "            if text_terms:\n", "                status_query['ASSIGNEE'] = {'$regex': '|'.join(text_terms), '$options': 'i'}\n", "            \n", "            if status_query:\n", "                queries[collection] = status_query\n", "\n", "        # Applications collection\n", "        elif collection == 'applications':\n", "            app_query = {}\n", "            if dates:\n", "                app_query['application_date'] = {'$gte': dates[0]}\n", "                if len(dates) > 1:\n", "                    app_query['application_date']['$lte'] = dates[1]\n", "                if text_terms:\n", "                    app_query['$or'] = [\n", "                        {'applicant_name': {'$regex': term, '$options': 'i'}} or\n", "                        {'processed_by': {'$regex': term, '$options': 'i'}}\n", "                        for term in text_terms\n", "                    ]\n", "            if app_query:\n", "                queries['applications'] = app_query\n", "\n", "        # SNRs collection\n", "        elif collection == 'snrs':\n", "            snr_query = {}\n", "            if numbers:\n", "                snr_query['snr'] = {'$in': numbers}\n", "            if text_terms:\n", "                snr_query['purpose'] = {'$regex': '|'.join(text_terms), '$options': 'i'}\n", "            if snr_query:\n", "                queries['snrs'] = snr_query\n", "\n", "    # Determine limit from numeric ranges in query\n", "    range_match = re.search(r'first (\\d+) results', query)\n", "    if range_match:\n", "        limit = min(int(range_match.group(1)), 100)  # Max 100 results\n", "\n", "    return {\n", "        \"queries\": {k: v for k, v in queries.items() if v is not None},\n", "        \"limit\": limit\n", "    }\n", "\n", "\n", "for i in ['2121', '2020', '3023']:\n", "    s = determine_collections(i)\n", "    results = create_mongodb_queries(f\"check the availability of {i}\", s)\n", "    print(results)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Add these functions to your mtn-snr-invoice-generator.py script\n", "\n", "from fuzzywuzzy import fuzz\n", "import re\n", "\n", "\n", "# Then modify the match_snrs_with_protected function by replacing the discrepancy check section with:\n", "\n", "# Simple discrepancy check\n", "has_discrepancy = False\n", "discrepancy_note = \"\"\n", "\n", "if db_applicant != \"N/A\" or db_vasp != \"N/A\":\n", "    # Check if the MTN user matches the database records\n", "    name_match = check_name_match(user, db_applicant, db_vasp)\n", "    \n", "    if not name_match:\n", "        has_discrepancy = True\n", "        if is_name_similar(user, \"MTN\"):\n", "            discrepancy_note = \"MTN claims ownership but database shows different entity\"\n", "        else:\n", "            discrepancy_note = f\"Listed as '{user}' but database shows different entity\""]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["similarities = compare_text_similarity(user_text, db_texts)\n", "for similarity, index in similarities:\n", "    print(f\"Similarity: {similarity:.4f}, Index: {index}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.1"}}, "nbformat": 4, "nbformat_minor": 2}