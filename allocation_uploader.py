from pymongo import MongoClient
import json
from datetime import datetime

# Connect to MongoDB
client = MongoClient("mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0")
db = client['new_numbering']

# Read the JSON file with additional allocations
with open('AT.json', 'r') as file:
    allocations = json.load(file)

# Convert date strings to datetime objects
for allocation in allocations:
    if allocation.get('allocation_date'):
        allocation['allocation_date'] = datetime.fromisoformat(
            allocation['allocation_date']['$date'].replace('Z', '+00:00')
        )
    if allocation.get('expiry_date') and allocation['expiry_date']:
        allocation['expiry_date'] = datetime.fromisoformat(
            allocation['expiry_date']['$date'].replace('Z', '+00:00')
        )
    if allocation.get('created_at'):
        allocation['created_at'] = datetime.fromisoformat(
            allocation['created_at']['$date'].replace('Z', '+00:00')
        )
    if allocation.get('updated_at'):
        allocation['updated_at'] = datetime.fromisoformat(
            allocation['updated_at']['$date'].replace('Z', '+00:00')
        )

# Insert the allocations
result = db.number_allocations.insert_many(allocations)
print(f"Inserted {len(result.inserted_ids)} additional number allocations for AT")