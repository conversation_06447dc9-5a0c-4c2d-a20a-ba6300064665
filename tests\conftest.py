import os
import sys
import pytest
import mongomock
from flask import Flask, template_rendered
from flask_login import <PERSON><PERSON><PERSON>ana<PERSON>, login_user
from unittest.mock import patch, MagicMock
from contextlib import contextmanager

# Add the project root to the sys.path if needed
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Import the routes module
from e164 import e164_bp

# Directory for test templates
TEST_TEMPLATE_FOLDER = os.path.join(os.path.dirname(__file__), 'templates')
os.makedirs(TEST_TEMPLATE_FOLDER, exist_ok=True)

# Create a basic template for testing
with open(os.path.join(TEST_TEMPLATE_FOLDER, 'mock_template.html'), 'w') as f:
    f.write("<!DOCTYPE html><html><body>Mock Template</body></html>")

class MockMongo:
    """Mock MongoDB connection"""
    def __init__(self):
        self.client = mongomock.MongoClient()
        self.db = self.client.get_database('test_db')

@contextmanager
def captured_templates(app):
    """
    Capture templates being rendered for testing
    """
    recorded = []
    def record(sender, template, context, **extra):
        recorded.append((template, context))
    template_rendered.connect(record, app)
    try:
        yield recorded
    finally:
        template_rendered.disconnect(record, app)

def create_mock_user(user_id="test_user"):
    """Create a mock user for testing"""
    user = MagicMock()
    user.id = user_id
    user.is_authenticated = True
    user.is_active = True
    user.is_anonymous = False
    user.role = 'admin'
    user.is_admin = True
    
    def get_id():
        return user_id
    
    user.get_id = get_id
    return user

@pytest.fixture
def app():
    """Create a Flask application for testing with proper configuration"""
    app = Flask(__name__, template_folder=TEST_TEMPLATE_FOLDER)
    app.config.update({
        'TESTING': True,
        'SECRET_KEY': 'test_key',
        'WTF_CSRF_ENABLED': False,
        'PRESERVE_CONTEXT_ON_EXCEPTION': False,
        'LOGIN_DISABLED': False
    })

    # Setup Flask-Login
    login_manager = LoginManager()
    login_manager.init_app(app)
    
    @login_manager.user_loader
    def load_user(user_id):
        # Return a mock user for testing
        return create_mock_user(user_id)
    
    # Add mongo directly to the app
    app.mongo = MockMongo()
    
    # Register the blueprint
    app.register_blueprint(e164_bp)

    # Override render_template to avoid template not found errors
    def mock_render_template(*args, **kwargs):
        # For testing, always return a simple HTML string
        return "<!DOCTYPE html><html><body>Mock Template for {}</body></html>".format(args[0] if args else "Unknown")
    
    # Apply the patch for testing
    template_patcher = patch('flask.render_template', mock_render_template)
    template_patcher.start()
    
    yield app
    
    # Clean up after test
    template_patcher.stop()

@pytest.fixture
def client(app):
    """A test client for the app"""
    with app.test_client() as client:
        with app.app_context():
            yield client

@pytest.fixture
def mock_mongo(app):
    """Make the mock MongoDB available to tests"""
    # The mongo instance is already set up on the app
    # Just need to patch any direct imports of 'mongo' in the routes module
    with patch('e164.routes.mongo', app.mongo):
        yield app.mongo.db

@pytest.fixture
def logged_in_admin(client):
    """Fixture for logged in admin user"""
    with client.session_transaction() as session:
        # Setup session
        user = create_mock_user()
        login_user(user)
        session['_user_id'] = user.id
    
    return client

@pytest.fixture
def request_context(app):
    """Provides a request context for testing"""
    with app.test_request_context():
        yield