<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Network Porting Visualization</title>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #f4f4f4;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            padding: 20px;
        }
        .graph-container {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 20px;
            width: 100%;
            max-width: 800px;
        }
        #graph {
            width: 100%;
            height: 500px;
        }
        .link {
            stroke: #999;
            stroke-opacity: 0.6;
            stroke-width: 1.5px;
        }
        .node {
            stroke: #fff;
            stroke-width: 1.5px;
        }
        .node text {
            font-size: 10px;
            font-weight: bold;
        }
        .controls {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .tooltip {
            position: absolute;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.2s;
        }
    </style>
</head>
<body>
    <div class="graph-container">
        <div class="controls">
            <div>
                <label for="nodeType">View:</label>
                <select id="nodeType">
                    <option value="all">All Nodes</option>
                    <option value="ported-in">Ported In</option>
                    <option value="ported-out">Ported Out</option>
                </select>
            </div>
            <div>
                <label for="forceStrength">Force Strength:</label>
                <input type="range" id="forceStrength" min="0.1" max="1" step="0.1" value="0.5">
            </div>
        </div>
        <div id="graph"></div>
    </div>
    <div class="tooltip" id="tooltip"></div>

    <script>
        // Porting data
        const portingData = [
            { id: 1, ndc: '023', portedIn: 26, portedOut: 0 },
            { id: 2, ndc: '020', portedIn: 0, portedOut: 64 },
            { id: 3, ndc: '050', portedIn: 0, portedOut: 147 },
            { id: 4, ndc: '056', portedIn: 7, portedOut: 0 },
            { id: 5, ndc: '026', portedIn: 75, portedOut: 0 },
            { id: 6, ndc: '027', portedIn: 55, portedOut: 0 },
            { id: 7, ndc: '057', portedIn: 47, portedOut: 0 }
        ];

        // Create graph data
        const nodes = portingData.map(d => ({
            id: d.ndc,
            portedIn: d.portedIn,
            portedOut: d.portedOut,
            total: d.portedIn + d.portedOut
        }));

        const links = [];
        portingData.forEach((d, i) => {
            if (d.portedIn > 0) {
                links.push({
                    source: 'Ported In',
                    target: d.ndc,
                    value: d.portedIn
                });
            }
            if (d.portedOut > 0) {
                links.push({
                    source: d.ndc,
                    target: 'Ported Out',
                    value: d.portedOut
                });
            }
        });

        // Add central nodes
        nodes.push(
            { id: 'Ported In', total: 210, type: 'central' },
            { id: 'Ported Out', total: 211, type: 'central' }
        );

        // SVG Setup
        const svg = d3.select("#graph")
            .append("svg")
            .attr("width", "100%")
            .attr("height", "100%")
            .call(d3.zoom().on("zoom", zoomed));

        const g = svg.append("g");

        const tooltip = d3.select("#tooltip");

        // Color Scale
        const color = d3.scaleOrdinal()
            .domain(nodes.map(d => d.id))
            .range(d3.schemeSet3);

        // Simulation
        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-100))
            .force("center", d3.forceCenter(400, 250));

        // Links
        const link = g.append("g")
            .selectAll("line")
            .data(links)
            .enter().append("line")
            .attr("class", "link")
            .style("stroke-width", d => Math.sqrt(d.value) * 2);

        // Nodes
        const node = g.append("g")
            .selectAll("g")
            .data(nodes)
            .enter().append("g")
            .call(d3.drag()
                .on("start", dragstarted)
                .on("drag", dragged)
                .on("end", dragended));

        node.append("circle")
            .attr("r", d => {
                const size = Math.sqrt(d.total || 10) * 5;
                return Math.max(size, 10);
            })
            .attr("fill", d => color(d.id))
            .attr("class", "node")
            .on("mouseover", showTooltip)
            .on("mouseout", hideTooltip);

        node.append("text")
            .text(d => d.id)
            .attr("x", 0)
            .attr("y", 4)
            .attr("text-anchor", "middle")
            .attr("font-size", "10px")
            .attr("fill", "white");

        // Simulation tick
        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("transform", d => `translate(${d.x},${d.y})`);
        });

        // Tooltip functions
        function showTooltip(event, d) {
            tooltip.transition().duration(200).style("opacity", .9);
            tooltip.html(`
                <strong>${d.id}</strong><br>
                Ported In: ${d.portedIn || 0}<br>
                Ported Out: ${d.portedOut || 0}<br>
                Total: ${d.total || 0}
            `)
            .style("left", (event.pageX + 10) + "px")
            .style("top", (event.pageY - 28) + "px");
        }

        function hideTooltip() {
            tooltip.transition().duration(500).style("opacity", 0);
        }

        // Zoom function
        function zoomed(event) {
            g.attr("transform", event.transform);
        }

        // Drag functions
        function dragstarted(event, d) {
            if (!event.active) simulation.alphaTarget(0.3).restart();
            d.fx = d.x;
            d.fy = d.y;
        }

        function dragged(event, d) {
            d.fx = event.x;
            d.fy = event.y;
        }

        function dragended(event, d) {
            if (!event.active) simulation.alphaTarget(0);
            d.fx = null;
            d.fy = null;
        }

        // Node type filter
        d3.select("#nodeType").on("change", function() {
            const selectedType = this.value;
            node.style("display", d => {
                if (selectedType === "all") return "block";
                if (selectedType === "ported-in") return d.portedIn > 0 ? "block" : "none";
                if (selectedType === "ported-out") return d.portedOut > 0 ? "block" : "none";
            });
            link.style("display", d => {
                if (selectedType === "all") return "block";
                if (selectedType === "ported-in") return d.target.portedIn > 0 || d.source.id === "Ported In" ? "block" : "none";
                if (selectedType === "ported-out") return d.source.portedOut > 0 || d.target.id === "Ported Out" ? "block" : "none";
            });
        });

        // Force strength control
        d3.select("#forceStrength").on("input", function() {
            const strength = -100 * this.value;
            simulation.force("charge", d3.forceManyBody().strength(strength));
            simulation.alpha(1).restart();
        });
    </script>
</body>
</html>