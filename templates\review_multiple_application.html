<!-- templates/review_multiple_application.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Review Your Application</h2>
                    <p class="text-muted text-center">
                        Please review the details below before confirming your application.
                    </p>
                    
                    <!-- Application details -->
                    <div class="mb-4">
                        <h4>Applicant Information</h4>
                        <p><strong>Applicant Name:</strong> {{ applicant_name }}</p>
                        <p><strong>Email:</strong> {{ email }}</p>
                        <p><strong>Phone:</strong> {{ phone }}</p>
                        <p><strong>Company:</strong> {{ company }}</p>
                        <p><strong>Application Date:</strong> {{ application_date }}</p>
                    </div>

                    <!-- SNR details -->
                    <h4>Numbers and Purposes</h4>
                    {% for snr in snrs %}
                    <div class="card mt-3">
                        <div class="card-body">
                            <h5><strong>SNR:</strong> {{ snr.snr }}</h5>
                            <p><strong>Type:</strong> {{ snr.type }}</p>
                            <p><strong>Purpose:</strong> {{ snr.purpose }}</p>
                            <p><strong>Status:</strong> {{ snr.status }}</p>
                            <p><strong>Certificate ID:</strong> {{ snr.certificate_id }}</p>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Confirmation form -->
                    <form id="confirmation-form" method="POST" action="{{ url_for('confirm_apply_multiple') }}">
                        <input type="hidden" name="application_id" value="{{ application_id }}">
                        <div class="mt-4 d-flex justify-content-between">
                            <a href="{{ url_for('search') }}?from_review=1" class="btn btn-secondary">Back to Edit</a>
                            <button type="submit" class="btn btn-success">Confirm and Submit Application</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- <script>
    document.addEventListener("DOMContentLoaded", function() {
        // Serialize the snrs variable to JSON and assign it to the hidden input field

        const snrsData = {{ snrs | tojson }};
        const snrsInput = document.getElementById("snrs-input");
        snrsInput.value = JSON.stringify(snrsData);

        // Attach event listener to the form submission to ensure correct serialization
        document.getElementById("confirmation-form").addEventListener("submit", function(event) {
            snrsInput.value = JSON.stringify(snrsData);
        });
    });
</script> -->
{% endblock %}
