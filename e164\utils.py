# utils.py
import os
import re
import base64
from io import BytesIO
from datetime import datetime, timedelta, timezone
from functools import wraps
from bson import ObjectId
from flask import flash, redirect, url_for, current_app
from flask_login import current_user
import qrcode
import pandas as pd

from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.table import Table, TableStyleInfo


from extensions import mongo



# File handling utilities
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}


RATES = {
    'active_wireless': 0.32,
    'inactive_reserved_wireless': 0.032,
    'active_fixed': 0.01,
    'inactive_reserved_fixed': 0.001,
}

def allowed_file(filename):
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
            and not any(char in filename for char in {'/', '\\', ':'}))

# Authorization decorators
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

def log_activity(action, details=""):
    """Log user activity to the activity_log collection"""
 
    
    activity = {
        "user_id": getattr(current_user, 'id', 'Guest'),
        "email": getattr(current_user, 'email', 'Guest'),
        "action": action,
        "details": details,
        "timestamp": datetime.now(timezone.utc)
    }
    mongo.db.activity_log.insert_one(activity)

def add_notification(message, recipient):
    """Add a notification for a user"""

    
    notification = {
        'message': message,
        'recipient': recipient,
        'read': False,
        'timestamp': datetime.now(timezone.utc)
    }
    mongo.db.notifications.insert_one(notification)

def snr_length_finder(snr):
    """Determine SNR type based on length and format"""
    # Remove spaces and hyphens
    if not snr:
        return "Undefined"
        
    snr = str(snr).replace(" ", "").replace("-", "")
    
    # If the snr starts with "0800", it's Tollfree
    if len(snr) >= 9 and (snr.startswith("0800") or snr.startswith("800")):
        ans = "Tollfree"
    elif len(snr) >= 9 and (snr.startswith("0900") or snr.startswith("900")):
        ans = "Premium"
    elif len(snr) == 3 and 100 <= int(snr) <= 999:
        ans = "3 Digit"
    elif len(snr) == 4 and 1000 <= int(snr) <= 9999:
        ans = "4 Digit"
    elif len(snr) == 5 and 10000 <= int(snr) <= 99999:
        ans = "5 Digit"
    elif len(snr) == 6 and 100000 <= int(snr) <= 999999:
        ans = "6 Digit"
    else:
        ans = "Undefined"
    
    return ans

def get_status_table(snr):
    """Determine which status collection to update based on SNR"""
    if str(snr).startswith('0800'):
        return 'tf_status'
    elif str(snr).startswith('0900'):
        return 'pr_status'
    else:
        return 'snr_status'


def serialize_to_json(doc):
    """Converts MongoDB document (with ObjectId and datetime) to JSON-serializable dict."""
    if doc is None:
        return None
    serialized = {}
    for key, value in doc.items():
        if isinstance(value, ObjectId):
            serialized[key] = str(value)
        elif isinstance(value, datetime):
            serialized[key] = value.isoformat() # Convert datetime to ISO string
        elif isinstance(value, dict):
            serialized[key] = serialize_to_json(value) # Recursively serialize nested dicts
        elif isinstance(value, list):
             # Handle lists (e.g., list of dicts)
             serialized[key] = [serialize_to_json(item) if isinstance(item, dict) else item for item in value]
        else:
            serialized[key] = value
    return serialized

def calculate_number_metrics(number_range):
    """Calculate derived metrics for a number range"""
    # Get basic numbers
    total_allocated = number_range.get('total_allocated', 0)
    active_subscriber = number_range.get('active_subscriber', 0)
    active_non_subscriber = number_range.get('active_non_subscriber', 0)
    inactive = number_range.get('inactive', 0)
    reserved = number_range.get('reserved', 0)
    
    # Calculate utilization metrics
    total_active = active_subscriber + active_non_subscriber
    utilization_rate = (total_active / total_allocated * 100) if total_allocated > 0 else 0
    dormancy_rate = (inactive / total_allocated * 100) if total_allocated > 0 else 0
    reservation_rate = (reserved / total_allocated * 100) if total_allocated > 0 else 0
    
    # Return calculated metrics
    return {
        'total_active': total_active,
        'utilization_rate': round(utilization_rate, 2),
        'dormancy_rate': round(dormancy_rate, 2),
        'reservation_rate': round(reservation_rate, 2),
        'unaccounted': total_allocated - total_active - inactive - reserved,
    }


def calculate_e164_invoice(submission_id):
    """
    Calculates invoice details for a given E.164 submission.

    Args:
        submission_id (str): The ID of the E.164 submission.

    Returns:
        dict: Contains invoice_summary, invoice_breakdown, and submission_details,
              or None if the submission is not found or has no ranges.
    """
    try:
        obj_submission_id = ObjectId(submission_id)
    except Exception as e:
        current_app.logger.error(f"Invalid submission_id for invoice calculation: {submission_id}, Error: {e}")
        return None

    submission = mongo.db.e164_submissions.find_one({'_id': obj_submission_id})
    if not submission:
        current_app.logger.warning(f"Submission not found for invoice calculation: {submission_id}")
        return None

    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))
    if not ranges:
        current_app.logger.info(f"No number ranges found for submission {submission_id} to calculate invoice.")
        return {
             'submission_details': {
                'submission_id': submission_id,
                'operator_name': submission.get('operator', 'N/A'),
                'reporting_period': submission.get('reporting_period', 'N/A'),
            },
            'invoice_summary': {'total_amount_ghc': 0.00},
            'invoice_breakdown': [],
            'rates_applied': RATES,
            'currency': 'GHS'
        }


    invoice_breakdown = []
    total_invoice_amount = 0.0

    for r_idx, r_data in enumerate(ranges):
        range_detail = {
            'id': r_idx + 1,
            'ndc': r_data.get('ndc', 'N/A'),
            'type': r_data.get('type', 'N/A'),
            'start_block': r_data.get('start_block', 'N/A'),
            'end_block': r_data.get('end_block', 'N/A'),
            'total_allocated': r_data.get('total_allocated', 0),
            'active_subscriber': r_data.get('active_subscriber', 0),
            'active_non_subscriber': r_data.get('active_non_subscriber', 0),
            'inactive': r_data.get('inactive', 0),
            'reserved': r_data.get('reserved', 0),
            'cost_ghc': 0.0,
            'rate_applied_description': 'N/A'
        }

        total_active = range_detail['active_subscriber'] + range_detail['active_non_subscriber']
        total_inactive_reserved = range_detail['inactive'] + range_detail['reserved']
        
        line_cost = 0.0
        rate_desc_parts = []

        range_type_lower = range_detail['type'].lower()

        if 'wireless' in range_type_lower:
            cost_active = total_active * RATES['active_wireless']
            cost_inactive_reserved = total_inactive_reserved * RATES['inactive_reserved_wireless']
            line_cost = cost_active + cost_inactive_reserved
            if total_active > 0:
                rate_desc_parts.append(f"Active Wireless: {total_active} * {RATES['active_wireless']}")
            if total_inactive_reserved > 0:
                 rate_desc_parts.append(f"Inactive/Reserved Wireless: {total_inactive_reserved} * {RATES['inactive_reserved_wireless']}")
        elif 'wireline' in range_type_lower or 'fixed' in range_type_lower: # Assuming 'fixed' might appear in type
            cost_active = total_active * RATES['active_fixed']
            cost_inactive_reserved = total_inactive_reserved * RATES['inactive_reserved_fixed']
            line_cost = cost_active + cost_inactive_reserved
            if total_active > 0:
                rate_desc_parts.append(f"Active Fixed: {total_active} * {RATES['active_fixed']}")
            if total_inactive_reserved > 0:
                rate_desc_parts.append(f"Inactive/Reserved Fixed: {total_inactive_reserved} * {RATES['inactive_reserved_fixed']}")
        else:
            # Handle "SHARED", "OTHER", or undefined types - currently no charge
            current_app.logger.info(f"Range type '{range_detail['type']}' for NDC {range_detail['ndc']} is not billable under current rules.")
            rate_desc_parts.append("Not billable under current rules")


        range_detail['cost_ghc'] = round(line_cost, 2)
        range_detail['rate_applied_description'] = "; ".join(rate_desc_parts) if rate_desc_parts else "N/A"
        total_invoice_amount += line_cost
        invoice_breakdown.append(range_detail)

    invoice_summary = {
        'total_amount_ghc': round(total_invoice_amount, 2)
    }

    submission_details = {
        'submission_id': submission_id,
        'operator_name': submission.get('operator', 'N/A'),
        'reporting_period': submission.get('reporting_period', 'N/A'),
        'submission_date': submission.get('created_at').strftime('%Y-%m-%d %H:%M:%S') if submission.get('created_at') else 'N/A',
    }

    return {
        'submission_details': submission_details,
        'invoice_summary': invoice_summary,
        'invoice_breakdown': invoice_breakdown,
        'rates_applied': RATES,
        'currency': 'GHC' # Explicitly state currency
    }


# holding/e164/utils.py

# ... (RATES dictionary and calculate_e164_invoice function remain the same) ...

def generate_invoice_excel(invoice_data):
    """
    Generates a styled Excel workbook from the calculated invoice data.

    Args:
        invoice_data (dict): The dictionary returned by calculate_e164_invoice.

    Returns:
        io.BytesIO: A BytesIO object containing the Excel file, or None if error.
    """
    if not invoice_data:
        current_app.logger.error("generate_invoice_excel: No invoice data provided.")
        return None

    output = BytesIO()
    try:
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            workbook = writer.book

            # --- Define Styles ---
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4F81BD", end_color="4F81BD", fill_type="solid") # Blue
            title_font = Font(bold=True, size=16)
            subtitle_font = Font(bold=True, size=14)
            total_font = Font(bold=True, size=12)
            currency_format = '_("GHS"* #,##0.00_);_("GHS"* (#,##0.00);_("GHS"* "-"??_);_(@_)' # Excel currency format for GHS
            
            center_alignment = Alignment(horizontal='center', vertical='center')
            left_alignment = Alignment(horizontal='left', vertical='center')
            right_alignment = Alignment(horizontal='right', vertical='center')
            
            thin_border_side = Side(border_style="thin", color="000000")
            thin_border = Border(left=thin_border_side, right=thin_border_side, top=thin_border_side, bottom=thin_border_side)

            # --- Invoice Summary Sheet ---
            ws_summary = workbook.create_sheet(title="Invoice Summary", index=0) # Create sheet manually for more control
            if "Sheet" in workbook.sheetnames and len(workbook.sheetnames) > 1: # Remove default sheet if pandas created one
                del workbook["Sheet"]

            # Titles
            ws_summary['A1'] = "NATIONAL COMMUNICATIONS AUTHORITY"
            ws_summary['A1'].font = title_font
            ws_summary['A1'].alignment = center_alignment
            ws_summary.merge_cells('A1:D1')

            ws_summary['A2'] = "SUMMARY OF E.164 RESOURCES INVOICE"
            ws_summary['A2'].font = subtitle_font
            ws_summary['A2'].alignment = center_alignment
            ws_summary.merge_cells('A2:D2')
            
            current_row = 4
            summary_details = invoice_data.get('submission_details', {})
            ws_summary[f'A{current_row}'] = "Operator Name:"
            ws_summary[f'B{current_row}'] = summary_details.get('operator_name', 'N/A')
            ws_summary[f'A{current_row}'].font = Font(bold=True)
            current_row += 1
            ws_summary[f'A{current_row}'] = "Reporting Period:"
            ws_summary[f'B{current_row}'] = summary_details.get('reporting_period', 'N/A')
            ws_summary[f'A{current_row}'].font = Font(bold=True)
            current_row += 1
            ws_summary[f'A{current_row}'] = "Submission Date:"
            ws_summary[f'B{current_row}'] = summary_details.get('submission_date', 'N/A')
            ws_summary[f'A{current_row}'].font = Font(bold=True)
            current_row += 1
            ws_summary[f'A{current_row}'] = "Submission ID:"
            ws_summary[f'B{current_row}'] = summary_details.get('submission_id', 'N/A')
            ws_summary[f'A{current_row}'].font = Font(bold=True)
            
            current_row += 2 # Spacer
            ws_summary[f'A{current_row}'] = "Total Invoice Amount:"
            ws_summary[f'B{current_row}'] = invoice_data.get('invoice_summary', {}).get('total_amount_ghc', 0.00)
            ws_summary[f'A{current_row}'].font = total_font
            ws_summary[f'B{current_row}'].font = total_font
            ws_summary[f'B{current_row}'].number_format = currency_format
            ws_summary[f'B{current_row}'].alignment = left_alignment


            current_row += 2 # Spacer
            ws_summary[f'A{current_row}'] = "Rates Applied (GHS):"
            ws_summary[f'A{current_row}'].font = Font(bold=True)
            current_row += 1
            rates_applied = invoice_data.get('rates_applied', {})
            for key, value in rates_applied.items():
                ws_summary[f'A{current_row}'] = key.replace('_', ' ').title() + ":"
                ws_summary[f'B{current_row}'] = value
                ws_summary[f'B{current_row}'].number_format = currency_format # Apply currency format to rates too
                current_row +=1
            
            ws_summary.column_dimensions['A'].width = 35
            ws_summary.column_dimensions['B'].width = 30

            # --- Detailed Breakdown Sheet ---
            if invoice_data['invoice_breakdown']:
                df_breakdown = pd.DataFrame(invoice_data['invoice_breakdown'])
                cols_order = [
                    'id', 'ndc', 'type', 'start_block', 'end_block', 
                    'total_allocated', 'active_subscriber', 'active_non_subscriber',
                    'inactive', 'reserved', 'rate_applied_description', 'cost_ghc'
                ]
                # Ensure all columns exist, add if not for safety
                for col in cols_order:
                    if col not in df_breakdown.columns:
                        df_breakdown[col] = None 
                df_breakdown = df_breakdown[cols_order]
                
                # Write dataframe to a new sheet (pandas handles the data part)
                df_breakdown.to_excel(writer, sheet_name='Detailed Breakdown', index=False, startrow=1) # Start data from row 2
                ws_breakdown = writer.sheets['Detailed Breakdown']

                # Add a title to the breakdown sheet
                ws_breakdown['A1'] = "Detailed Invoice Breakdown"
                ws_breakdown['A1'].font = subtitle_font
                ws_breakdown['A1'].alignment = center_alignment
                ws_breakdown.merge_cells(start_row=1, start_column=1, end_row=1, end_column=len(cols_order))


                # Style Header Row (now row 2 because data starts at row 2, header is written by pandas at row 2)
                for col_num, column_title in enumerate(df_breakdown.columns, 1):
                    cell = ws_breakdown.cell(row=2, column=col_num)
                    cell.value = column_title # Pandas already wrote this, but we re-apply to ensure style
                    cell.font = header_font
                    cell.fill = header_fill
                    cell.alignment = center_alignment
                    cell.border = thin_border
                
                # Apply styles to data cells and format currency
                for row_idx, row_data in enumerate(df_breakdown.values, 3): # Data starts from row 3
                    for col_idx, cell_value in enumerate(row_data, 1):
                        cell = ws_breakdown.cell(row=row_idx, column=col_idx)
                        cell.border = thin_border
                        if df_breakdown.columns[col_idx-1] == 'cost_ghc':
                            cell.number_format = currency_format
                            cell.alignment = right_alignment
                        elif isinstance(cell_value, (int, float)):
                             cell.alignment = right_alignment
                        else:
                            cell.alignment = left_alignment
                
                # Create an Excel Table for the breakdown
                table_range = f"A2:{get_column_letter(ws_breakdown.max_column)}{ws_breakdown.max_row}"
                excel_table = Table(displayName="InvoiceBreakdownTable", ref=table_range)
                style = TableStyleInfo(name="TableStyleMedium9", showFirstColumn=False,
                                       showLastColumn=False, showRowStripes=True, showColumnStripes=False)
                excel_table.tableStyleInfo = style
                ws_breakdown.add_table(excel_table)

                # Auto-adjust column widths for breakdown sheet
                for col_idx, column_title in enumerate(df_breakdown.columns, 1):
                    column_letter = get_column_letter(col_idx)
                    max_len = len(str(column_title))
                    for i in range(len(df_breakdown)):
                        max_len = max(max_len, len(str(df_breakdown.iloc[i, col_idx-1])))
                    ws_breakdown.column_dimensions[column_letter].width = max_len + 2


            else: # No breakdown data
                ws_no_breakdown = workbook.create_sheet(title="Detailed Breakdown")
                if "Sheet" in workbook.sheetnames and len(workbook.sheetnames) > 1 and ws_no_breakdown.title != "Sheet":
                     del workbook["Sheet"] # clean up default sheet if it exists and isn't our target
                ws_no_breakdown['A1'] = "No billable items in this submission."
                ws_no_breakdown['A1'].font = Font(italic=True)
        
        # Ensure the writer saves the workbook content to the BytesIO stream
        # This happens automatically when exiting the 'with' block for pd.ExcelWriter

    except Exception as e:
        current_app.logger.error(f"Error generating Excel invoice: {e}", exc_info=True)
        # Return None or raise error, depending on how you want to handle it upstream
        return None

    output.seek(0)
    return output