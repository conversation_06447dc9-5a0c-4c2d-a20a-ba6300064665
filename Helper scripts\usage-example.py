import os
import subprocess

# Configuration
excel_files = [
    "MTN FIRST HALF 2024 AUDIT SUMMARY.xlsx",
    "AT FIRST HALF 2024 AUDIT SUMMARY.xlsx",
    "TELECEL FIRST HALF 2024 AUDIT SUMMARY.xlsx"
]

# Create output directory if it doesn't exist
os.makedirs("json_output", exist_ok=True)

# Convert each Excel file to JSON
for excel_file in excel_files:
    # Extract operator name from filename
    operator_name = excel_file.split()[0]
    
    # Define output JSON file
    json_file = f"json_output/{operator_name}_2024_AUDIT.json"
    
    print(f"Converting {excel_file} to {json_file}...")
    
    # Run conversion script
    cmd = ["python", "excel_to_mongodb.py", excel_file, json_file]
    subprocess.run(cmd, check=True)
    
    print(f"Success! {json_file} created.\n")

# Import each JSON file to MongoDB
for excel_file in excel_files:
    # Extract operator name from filename
    operator_name = excel_file.split()[0]
    
    # Define input JSON file
    json_file = f"json_output/{operator_name}_2024_AUDIT.json"
    
    print(f"Importing {json_file} to MongoDB...")
    
    # Run import script
    cmd = ["python", "mongodb_import.py", json_file]
    subprocess.run(cmd, check=True)
    
    print(f"Success! {json_file} imported to MongoDB.\n")

print("All conversions and imports completed successfully!")
