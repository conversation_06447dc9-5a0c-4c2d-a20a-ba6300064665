<!-- templates/search.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">Telecom Number Management</h2>

                    <!-- Unified Search/Upload Interface -->
                    <div class="unified-interface mb-5">
                        <!-- Tabs for different search types -->
                        <ul class="nav nav-tabs mb-4" id="searchTabs">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="tab" href="#search">Text Search</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#ocr">Document OCR</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="tab" href="#csv">Spreadsheet Upload</a>
                            </li>
                        </ul>

                        <!-- Tab content -->
                        <div class="tab-content">
                            <!-- Text Search -->
                            <div class="tab-pane fade show active" id="search">
                                <form method="POST" action="{{ url_for('search') }}">
                                    <div class="input-group">
                                        <input type="text" name="query" class="form-control" 
                                               placeholder="Enter number, applicant name, or keywords..." required>
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">Search</button>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- OCR Upload -->
                            <div class="tab-pane fade" id="ocr">
                                <form method="POST" action="{{ url_for('process_form') }}" enctype="multipart/form-data">
                                    <div class="custom-file mb-3">
                                        <input type="file" name="file" class="custom-file-input" 
                                               accept="image/*,.pdf" required>
                                        <label class="custom-file-label">Choose image file...</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-block">Process Document</button>
                                </form>
                            </div>

                            <!-- CSV Upload -->
                            <div class="tab-pane fade" id="csv">
                                <form method="POST" action="{{ url_for('upload_csv') }}" enctype="multipart/form-data">
                                    <div class="custom-file mb-3">
                                        <input type="file" name="file" class="custom-file-input" 
                                               accept=".csv,.xlsx" required>
                                        <label class="custom-file-label">Choose spreadsheet...</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-block">Upload & Process</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Results Display -->
                    {% if results %}
                    <div class="results-section">
                        <h4 class="mb-4">Search Results for "{{ query }}"</h4>
                
                        <!-- Single Result Display -->
                        {% if results|length == 1 %}
                        {% set result = results[0].results %}
                        <div class="card result-card mb-4">
                            <div class="card-body">

                

                                
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div>
                                        <h5 class="card-title mb-0">Number: {{ result.snr | default('N/A') }}</h5>
                                        {% if result.purpose %}
                                        <p class="text-muted small mb-0">Purpose: {{ result.purpose }}</p>
                                        {% endif %}
                                    </div>
                                    <span class="badge 
                                        {% if result.availability == 'Available' %}badge-success
                                        {% else %}badge-danger{% endif %}">
                                        {{ result.availability | default('N/A') }}
                                    </span>
                                </div>
                                <p class="card-text">{{ result.description | default('No description available') }}</p>
                                <div class="applicant-info">
                                    <small class="text-muted">
                                        Current Status: {{ result.applicant_info.status | default('N/A') }}<br>
                                        Expiry: {{ result.applicant_info.expiry | default('N/A') }}<br>
                                        Assignee: {{ result.applicant_info.assignee | default('N/A') }}
                                    </small>
                                </div>
                                {% if result.availability == 'Available' %}
                                <div class="mt-3">
                                    <a href="{{ url_for('apply', snr=result.snr) }}" 
                                       class="btn btn-primary btn-block">
                                        Apply for This Number
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                


                    
                       <!-- Multiple Results Display -->
                       {% else %}
                       <div class="row">
     <!-- Found Numbers Column (Only shows unavailable numbers) -->
     <div class="col-md-6">
        <div class="card result-list">
            <div class="card-header">
                Unavailable Numbers ({{ results|selectattr('availability', 'ne', 'Available')|list|length }})
            </div>
            <div class="card-body">
                {% for result in results if result.availability != 'Available' %}
                <div class="result-item mb-2 p-2 unavailable">
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>{{ result.snr | default('N/A') }}</strong>
                            {% if result.purpose %}
                            <p class="mb-0 small text-muted">{{ result.purpose }}</p>
                            {% endif %}
                        </div>
                        <span class="badge badge-danger">
                            {{ result.availability | default('N/A') }}
                        </span>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>


                           <!-- Purpose Assignment Column -->
         <!-- Purpose Assignment Column (Auto-populates available numbers) -->
         <div class="col-md-6">
            <div class="card purpose-assignment">
                <div class="card-header d-flex justify-content-between align-items-center">
                    Application Purposes
                    <button type="button" class="btn btn-sm btn-primary add-purpose">
                        <i class="fas fa-plus"></i> Add Purpose
                    </button>
                </div>
                <div class="card-body" id="purposeContainer">
                    {% for result in results if result.availability == 'Available' %}
                    <div class="purpose-section mb-3">
                        <textarea class="form-control mb-2 purpose-input" 
                                  placeholder="Enter purpose description...">{{ result.purpose }}</textarea>
                        <div class="droppable-zone">
                            <div class="result-item in-purpose mb-2 p-2" 
                                 data-snr="{{ result.snr | default('') }}">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>{{ result.snr | default('N/A') }}</strong>
                                    </div>
                                    <span class="badge badge-success">
                                        {{ result.availability | default('N/A') }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                    
                    <!-- Empty template for new purposes -->
                    <div class="purpose-section mb-3" style="display: none;">
                        <textarea class="form-control mb-2 purpose-input" 
                                  placeholder="Enter purpose description..."></textarea>
                        <div class="droppable-zone"></div>
                    </div>
                </div>
               



                                    <div class="card-footer">
                                        <form method="POST" action="{{ url_for('apply_multiple_preview') }}">
                                            <input type="hidden" name="selected_snrs" id="selectedSnrs">
                                            <input type="hidden" name="purposes" id="purposesData">
                                            <button type="submit" class="btn btn-success btn-block">
                                                Apply for Selected Numbers
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
                
                
                </div>
            </div>
        </div>
    </div>
</div>



<!-- JavaScript Handling -->
<script>
    document.addEventListener("DOMContentLoaded", () => {
        let draggedItem = null;
        const resultsList = document.querySelector('.result-list .card-body');
        const purposeContainer = document.getElementById('purposeContainer');
        const addPurposeBtn = document.querySelector('.add-purpose');
    
        // Drag & Drop Functionality
        function initDraggables() {
            document.querySelectorAll('.draggable-item[draggable="true"]').forEach(item => {
                item.addEventListener('dragstart', handleDragStart);
                item.addEventListener('dragend', handleDragEnd);
            });
        }
    
        function handleDragStart(e) {
            if (this.dataset.available === 'false') {
                e.preventDefault();
                return;
            }
            draggedItem = this;
            this.classList.add('dragging');
        }
    
        function handleDragEnd() {
            draggedItem = null;
            this.classList.remove('dragging');
        }
    
        // Drop Zones
        function initDropZones() {
            document.querySelectorAll('.droppable-zone').forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('dragleave', handleDragLeave);
                zone.addEventListener('drop', handleDrop);
            });
        }
    
        function handleDragOver(e) {
            e.preventDefault();
            this.classList.add('drag-over');
        }
    
        function handleDragLeave() {
            this.classList.remove('drag-over');
        }
    
        function handleDrop(e) {
            e.preventDefault();
            this.classList.remove('drag-over');
            
            if (draggedItem) {
                const purposeSection = this.closest('.purpose-section');
                const purposeInput = purposeSection.querySelector('.purpose-input');
                const existingPurpose = draggedItem.querySelector('.text-muted')?.textContent.replace('Purpose: ', '') || '';

                // Auto-fill purpose if available and field is empty
                if (existingPurpose && !purposeInput.value.trim()) {
                    purposeInput.value = existingPurpose;
                }

                // Move item instead of cloning
                const movedItem = draggedItem.cloneNode(true);
                movedItem.classList.add('in-purpose');
                movedItem.draggable = false;
                
                // Add remove button
                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn btn-sm btn-link text-danger remove-item';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => returnToAvailable(movedItem);
                
                // Clean up purpose display in moved item
                const purposeElement = movedItem.querySelector('.text-muted');
                if (purposeElement) {
                    purposeElement.remove();
                }
                
                movedItem.querySelector('.badge').after(removeBtn);
                this.appendChild(movedItem);
                
                // Remove from available list
                draggedItem.remove();
                
                checkAddPurposeButton();
                updateFormData();
            }
        }



        function returnToAvailable(item) {
            const snr = item.dataset.snr;
            const newItem = document.createElement('div');
            newItem.className = 'result-item draggable-item mb-2 p-2';
            newItem.draggable = true;
            newItem.dataset.snr = snr;
            newItem.innerHTML = item.innerHTML.replace('<button class="btn btn-sm btn-link text-danger remove-item"><i class="fas fa-times"></i></button>', '');
            
            resultsList.appendChild(newItem);
            item.remove();
            
            initDraggables();
            checkAddPurposeButton();
            updateFormData();
        }
    
        // Purpose Management
        function checkAddPurposeButton() {
            const validSections = Array.from(document.querySelectorAll('.purpose-section'))
                .filter(section => {
                    const hasNumber = section.querySelector('.droppable-zone').children.length > 0;
                    const hasPurpose = section.querySelector('.purpose-input').value.trim() !== '';
                    return hasNumber && hasPurpose;
                });
    
            addPurposeBtn.disabled = validSections.length < document.querySelectorAll('.purpose-section').length;
        }
    
        document.querySelector('.add-purpose').addEventListener('click', () => {
            const newPurpose = document.createElement('div');
            newPurpose.className = 'purpose-section mb-3';
            newPurpose.innerHTML = `
                <div class="position-relative">
                    <textarea class="form-control mb-2 purpose-input" 
                          placeholder="Enter purpose description..." required></textarea>
                    <div class="droppable-zone"></div>
                    <button type="button" class="btn btn-sm btn-danger mt-2 remove-purpose">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            purposeContainer.appendChild(newPurpose);
            initDropZones();
            checkAddPurposeButton();
        });
    
        // Form Data Handling
        function updateFormData() {
            const purposes = [];
            const selectedSnrs = new Set();
    
            document.querySelectorAll('.purpose-section').forEach(section => {
                const purposeText = section.querySelector('.purpose-input').value.trim();
                const numbers = Array.from(section.querySelectorAll('.in-purpose'))
                                   .map(item => item.dataset.snr);
    
                if (purposeText && numbers.length > 0) {
                    purposes.push({ purpose: purposeText, snrs: numbers });
                    numbers.forEach(snr => selectedSnrs.add(snr));
                }
            });
    
            document.getElementById('selectedSnrs').value = Array.from(selectedSnrs).join(',');
            document.getElementById('purposesData').value = JSON.stringify(purposes);
        }
    
        // Event Delegation
        purposeContainer.addEventListener('input', (e) => {
            if (e.target.classList.contains('purpose-input')) {
                checkAddPurposeButton();
                updateFormData();
            }
        });
    
        purposeContainer.addEventListener('click', (e) => {
            if (e.target.closest('.remove-purpose')) {
                e.target.closest('.purpose-section').remove();
                checkAddPurposeButton();
                updateFormData();
            }
        });
    
        // Initial setup
        initDraggables();
        initDropZones();
        checkAddPurposeButton();
    });

    // Bootstrap file input handling
document.querySelectorAll('.custom-file-input').forEach(input => {
    input.addEventListener('change', function(e) {
        let fileName = e.target.files[0] ? e.target.files[0].name : "Choose file...";
        let label = this.nextElementSibling;
        label.classList.add("selected");
        label.innerHTML = fileName;
    });
});
    </script>
<style>
.result-card {
    border: 1px solid rgba(0,0,0,0.125);
    border-radius: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.result-list .result-item {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    cursor: move;
    transition: all 0.3s ease;
}

.result-item:hover {
    background-color: #f8f9fa;
}

.dragging {
    opacity: 0.5;
}

.droppable-zone {
    min-height: 100px;
    border: 2px dashed #dee2e6;
    border-radius: 0.25rem;
    padding: 0.5rem;
    margin-bottom: 1rem;
}


.unavailable {
        cursor: not-allowed;
        opacity: 0.7;
        background-color: #f8f9fa;
    }
.unavailable:hover {
        background-color: #f8f9fa;
    }


.drag-over {
    border-color: #007bff;
    background-color: rgba(0,123,255,0.05);
}

.purpose-section textarea {
    resize: none;
    min-height: 80px;
}

.remove-purpose {
    float: right;
}

.result-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.in-purpose {
    background-color: #e9f5ff;
    border-color: #b8daff !important;
}

.remove-item {
    /* position: absolute; */
    right: 5px;
    top: 5px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.in-purpose:hover .remove-item {
    opacity: 1;
}

.purpose-section:not(:last-child) {
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 1rem;
} 

.custom-file-input:lang(en)~.custom-file-label::after {
    content: "Browse";
}

.custom-file-label {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.custom-file-label.selected {
    color: #495057;
}
</style>
{% endblock %}