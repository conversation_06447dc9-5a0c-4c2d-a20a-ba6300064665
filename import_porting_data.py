#!/usr/bin/env python
"""
Import porting data from JSON file and associate with existing submissions.
Usage: python import_porting_data.py <json_file> <operator_id>
"""

import sys
import json
from datetime import datetime, timezone
from pymongo import MongoClient
from bson import ObjectId

# Configure MongoDB connection
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

def import_porting_data(json_file, operator_id=None):
    """Import porting data from JSON file and associate with submissions"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Load JSON data
    with open(json_file, 'r') as f:
        porting_data = json.load(f)
    
    if not porting_data:
        print("No data found in the JSON file.")
        return
    
    # Find the latest submission for the operator(s)
    # This will be used to associate the porting records
    submissions_map = {}
    
    if operator_id:
        # Find submission for specific operator
        latest_submission = db.e164_submissions.find_one(
            {'operator_id': operator_id, 'status': 'approved'},
            sort=[('reporting_period', -1)]
        )
        
        if latest_submission:
            submissions_map[operator_id] = str(latest_submission['_id'])
    else:
        # Find latest submission for each operator
        pipeline = [
            {"$match": {"status": "approved"}},
            {"$sort": {"reporting_period": -1}},
            {"$group": {
                "_id": "$operator_id",
                "submission_id": {"$first": "$_id"}
            }}
        ]
        
        operator_submissions = list(db.e164_submissions.aggregate(pipeline))
        for sub in operator_submissions:
            submissions_map[sub['_id']] = str(sub['submission_id'])
    
    # Get operator names to find their IDs
    operators = list(db.operators.find())
    operator_name_to_id = {}
    
    for op in operators:
        op_name = op.get('name')
        if op_name:
            operator_name_to_id[op_name] = str(op['_id'])
    
    # For each record, find the appropriate submission
    records_imported = 0
    records_skipped = 0
    
    for record in porting_data:
        # Get source operator
        source_op = record.get('source_operator')
        source_id = operator_name_to_id.get(source_op)
        
        if source_id and source_id in submissions_map:
            # Found a submission for this operator
            submission_id = submissions_map[source_id]
            
            # Add submission ID to the record
            record['submission_id'] = submission_id
            
            # Ensure created_at and updated_at are proper datetime objects
            if '$date' in record.get('created_at', {}):
                record['created_at'] = datetime.fromisoformat(record['created_at']['$date'].replace('Z', '+00:00'))
            else:
                record['created_at'] = datetime.now(timezone.utc)
            
            if '$date' in record.get('updated_at', {}):
                record['updated_at'] = datetime.fromisoformat(record['updated_at']['$date'].replace('Z', '+00:00'))
            else:
                record['updated_at'] = datetime.now(timezone.utc)
            
            # Insert the record
            db.e164_porting_details.insert_one(record)
            records_imported += 1
        else:
            # No submission found for this operator
            print(f"Skipping record for {source_op}: No matching submission found")
            records_skipped += 1
    
    print(f"Import complete: {records_imported} records imported, {records_skipped} records skipped")

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python import_porting_data.py <json_file> [operator_id]")
        sys.exit(1)
    
    json_file = sys.argv[1]
    operator_id = sys.argv[2] if len(sys.argv) > 2 else None
    
    import_porting_data(json_file, operator_id)