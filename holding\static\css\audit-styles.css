/* audit-styles.css - Place this in your static/css folder */

/* Glassmorphic UI Styles */
.glass-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.glass-card:hover {
    box-shadow: 0 10px 40px 0 rgba(31, 38, 135, 0.2);
}

.glass-form .form-control, 
.glass-form .form-select {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #333;
    transition: all 0.3s ease;
}

.glass-form .form-control:focus, 
.glass-form .form-select:focus {
    background: rgba(255, 255, 255, 0.25);
    box-shadow: 0 0 10px rgba(31, 38, 135, 0.25);
    border-color: rgba(72, 149, 239, 0.5);
}

.glass-form label {
    color: #333;
    font-weight: 500;
    margin-bottom: 5px;
}

.btn-glass {
    background: rgba(72, 149, 239, 0.6);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
}

.btn-glass:hover {
    background: rgba(72, 149, 239, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(31, 38, 135, 0.2);
}

.section-title {
    position: relative;
    font-size: 1.5rem;
    color: #333;
    margin-bottom: 20px;
    border-bottom: 2px solid rgba(31, 38, 135, 0.2);
    padding-bottom: 10px;
}

.header-underline {
    height: 3px;
    width: 50px;
    background: linear-gradient(90deg, rgba(72, 149, 239, 0.8), rgba(72, 149, 239, 0.2));
    margin-bottom: 20px;
}

.stat-card {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 10px 30px rgba(31, 38, 135, 0.1);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(31, 38, 135, 0.2);
}

.stat-card .stat-icon {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 24px;
    opacity: 0.2;
}

.stat-card .stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0;
}

.stat-card .stat-label {
    font-size: 1rem;
    color: #666;
    text-align: center;
}

.table-glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.table-glass thead {
    background: rgba(72, 149, 239, 0.1);
}

.table-glass thead th {
    font-weight: 600;
    color: #333;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.table-glass tbody tr:hover {
    background: rgba(255, 255, 255, 0.15);
}

.badge-glass {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border-radius: 10px;
    padding: 5px 10px;
    font-weight: 500;
}

.badge-glass.good {
    background: rgba(75, 192, 192, 0.2);
    color: rgba(75, 192, 192, 1);
    border: 1px solid rgba(75, 192, 192, 0.3);
}

.badge-glass.warning {
    background: rgba(255, 205, 86, 0.2);
    color: rgba(255, 159, 64, 1);
    border: 1px solid rgba(255, 205, 86, 0.3);
}

.badge-glass.critical {
    background: rgba(255, 99, 132, 0.2);
    color: rgba(255, 99, 132, 1);
    border: 1px solid rgba(255, 99, 132, 0.3);
}

.nav-tabs .nav-link {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #495057;
}

.nav-tabs .nav-link.active {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.3);
    color: #495057;
}

.chart-container {
    height: 400px;
    position: relative;
}

.progress-bar-container {
    height: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    overflow: hidden;
    margin-top: 5px;
}

.progress-bar {
    height: 100%;
    border-radius: 5px;
    transition: width 0.5s ease;
}

.progress-good {
    background: linear-gradient(90deg, rgba(75, 192, 192, 0.5), rgba(75, 192, 192, 0.8));
}

.progress-average {
    background: linear-gradient(90deg, rgba(255, 205, 86, 0.5), rgba(255, 205, 86, 0.8));
}

.progress-poor {
    background: linear-gradient(90deg, rgba(255, 99, 132, 0.5), rgba(255, 99, 132, 0.8));
}

/* Custom styling for tooltips */
.info-tooltip {
    font-size: 14px;
    color: rgba(72, 149, 239, 0.8);
    cursor: pointer;
    margin-left: 5px;
}

/* Force-directed graph styling */
#networkGraph {
    width: 100%;
    height: 400px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip {
    position: absolute;
    padding: 10px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    border-radius: 5px;
    pointer-events: none;
    z-index: 1000;
}

/* Background gradient for all audit pages */
body.audit-page {
    background-color: #f8f9fc;
    background-image: 
        radial-gradient(at 47% 33%, rgba(72, 149, 239, 0.1) 0, transparent 59%), 
        radial-gradient(at 82% 65%, rgba(72, 149, 239, 0.15) 0, transparent 55%);
}

/* Input field focus glow effect */
.glow-focus:focus {
    box-shadow: 0 0 15px rgba(72, 149, 239, 0.25);
    border-color: rgba(72, 149, 239, 0.5);
}

/* Animation for cards and elements */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animated-card {
    animation: fadeInUp 0.5s ease forwards;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
    .glass-card {
        padding: 15px;
    }
    
    .stat-card .stat-value {
        font-size: 2rem;
    }
    
    .chart-container {
        height: 300px;
    }
}
