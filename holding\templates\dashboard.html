{% extends "base.html" %}
{% block content %}
<div class="container mt-4">
    <!-- Search and Sort Controls -->
    <div class="card glassmorphic-card mb-4">
        <div class="card-body">
            <form id="filterForm" method="GET" action="{{ url_for('dashboard') }}">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <input type="text" 
                               class="form-control" 
                               name="search" 
                               placeholder="Search applications..." 
                               value="{{ search_query }}">
                    </div>
                    <div class="col-md-4 mb-3">
                        <select class="form-control" name="sort" onchange="this.form.submit()">
                            <option value="application_date" {% if current_sort == 'application_date' %}selected{% endif %}>
                                Sort by Date
                            </option>
                            <option value="total_numbers" {% if current_sort == 'total_numbers' %}selected{% endif %}>
                                Sort by Number Count
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <select class="form-control" name="order" onchange="this.form.submit()">
                            <option value="-1" {% if current_order == -1 %}selected{% endif %}>Newest First</option>
                            <option value="1" {% if current_order == 1 %}selected{% endif %}>Oldest First</option>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-filter"></i> Apply Filters
                        </button>
                        {% if search_query %}
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Clear Filters
                        </a>
                        {% endif %}
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Application Cards -->
    <div class="row">
        {% if applications %}
            {% for app in applications %}
            <div class="col-12 mb-4">
                <div class="card glassmorphic-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h5 class="card-title">
                                    Application #{{ app.application_id }}
                                    <span class="badge badge-secondary ml-2">
                                        {{ app.total_numbers }} number(s)
                                    </span>
                                </h5>
                                <p class="text-muted mb-1">
                                    Applied on: {{ app.application_date }}
                                </p>
                            </div>
                            <a href="{{ url_for('application_details', application_id=app.application_id) }}" 
                               class="btn btn-primary btn-sm">
                                View Details
                            </a>
                        </div>

                        <div class="mt-3">
                            {% for purpose, details in app.purposes.items() %}
                            <div class="mb-2">
                                <strong>{{ purpose }}</strong>
                                <span class="badge badge-info ml-2">{{ details.count }} numbers</span>
                                <div class="status-container mt-1">
                                    {% for status in details.statuses %}
                                    <span class="badge 
                                        {% if status == 'Approved' %}badge-success
                                        {% elif status == 'Pending Review' %}badge-warning
                                        {% else %}badge-secondary{% endif %}">
                                        {{ status }}
                                    </span>
                                    {% endfor %}
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="alert alert-info">
                    No applications found matching your criteria.
                </div>
            </div>
        {% endif %}
    </div>
</div>

<script>
document.addEventListener("DOMContentLoaded", function() {
    // Preserve form state on refresh
    const filterForm = document.getElementById('filterForm');
    const params = new URLSearchParams(window.location.search);
    
    // Add active filter indicators
    if(params.toString()) {
        const filterBadges = document.createElement('div');
        filterBadges.className = 'mb-3';
        
        params.forEach((value, key) => {
            if(value && key !== 'order') {
                const badge = document.createElement('span');
                badge.className = 'badge badge-info mr-2';
                badge.innerHTML = `${key}: ${value} <i class="fas fa-times ml-1"></i>`;
                badge.style.cursor = 'pointer';
                badge.onclick = () => {
                    params.delete(key);
                    window.location.search = params.toString();
                };
                filterBadges.appendChild(badge);
            }
        });
        
        filterForm.parentNode.insertBefore(filterBadges, filterForm);
    }
});
</script>

<style>
.glassmorphic-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s;
}

.glassmorphic-card:hover {
    transform: translateY(-2px);
}

.badge {
    transition: opacity 0.2s;
}

.badge:hover {
    opacity: 0.8;
}

.status-container {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}
</style>
{% endblock %}