import pandas as pd
import json
from datetime import datetime, timezone
import math
from bson.objectid import ObjectId

def excel_to_audit_json(excel_file, operator_name):
    """
    Convert Excel workbook to MongoDB-compatible JSON for audit records
    
    Args:
        excel_file: Path to the Excel file
        operator_name: Name of the operator (MTN, AT, TELECEL)
    
    Returns:
        List of audit documents (one per quarter)
    """
    # Read the Excel file - assuming the main data is in the first sheet
    df = pd.read_excel(excel_file)
    
    # Determine quarters from the Excel workbook
    # Assuming the workbook might have separate sheets or sections for Q1 and Q2
    quarters = ["Q1", "Q2"]
    audit_documents = []
    
    for quarter in quarters:
        # For each quarter, extract the relevant data
        # This part would need adjustment based on the exact Excel structure
        
        # Basic audit data
        audit_data = {
            "operator": operator_name,
            "quarter": quarter,
            "year": 2024,  # First half of 2024
            "sn_type": "Mobile",  # Default, could be extracted from Excel
            "ndc": get_ndc_for_operator(operator_name),
            "start_block": 0,  # Would be extracted from Excel
            "end_block": get_end_block_for_operator(operator_name),
            "created_by": "System",
            "timestamp": datetime.now(timezone.utc),
            "active_snrs_count": 0,
            "deactivated_snrs_count": 0,
            "active_snrs": [],
            "deactivated_snrs": []
        }
    
    # Return SNR data for the specified operator and quarter
    return snr_data.get(operator_name, {}).get(quarter, {})

def get_ndc_for_operator(operator_name):
    """
    Return the Network Destination Code (NDC) for a given operator
    """
    ndc_mapping = {
        "MTN": "024",
        "AT": "027",
        "TELECEL": "028"
    }
    return ndc_mapping.get(operator_name, "000")

def get_end_block_for_operator(operator_name):
    """
    Return the end block for a given operator
    """
    end_block_mapping = {
        "MTN": 9999999,
        "AT": 6999999,
        "TELECEL": 4999999
    }
    return end_block_mapping.get(operator_name, 9999999)

class MongoJSONEncoder(json.JSONEncoder):
    """
    Custom JSON encoder for MongoDB specific types
    """
    def default(self, obj):
        if isinstance(obj, datetime):
            return {"$date": obj.isoformat()}
        elif isinstance(obj, ObjectId):
            return {"$oid": str(obj)}
        return super().default(obj)

def save_to_json(audit_documents, output_file):
    """
    Save audit documents to a JSON file
    
    Args:
        audit_documents: List of audit document dictionaries
        output_file: Path to output file
    """
    with open(output_file, 'w') as f:
        json.dump(audit_documents, f, cls=MongoJSONEncoder, indent=2)
    
    print(f"Saved {len(audit_documents)} audit documents to {output_file}")

def main():
    """
    Main function to demonstrate the converter
    """
    import sys
    
    if len(sys.argv) < 3:
        print("Usage: python script.py <excel_file> <output_json>")
        sys.exit(1)
    
    excel_file = sys.argv[1]
    output_file = sys.argv[2]
    
    # Determine operator name from filename
    operator_name = None
    if "MTN" in excel_file:
        operator_name = "MTN"
    elif "AT" in excel_file:
        operator_name = "AT"
    elif "TELECEL" in excel_file:
        operator_name = "TELECEL"
    else:
        print("Could not determine operator from filename. Defaulting to MTN.")
        operator_name = "MTN"
    
    # Convert Excel to JSON
    audit_documents = excel_to_audit_json(excel_file, operator_name)
    
    # Save to JSON file
    save_to_json(audit_documents, output_file)
    
    # Print summary
    print(f"Processed {len(audit_documents)} quarters for {operator_name}:")
    for doc in audit_documents:
        print(f"  - {doc['quarter']} {doc['year']}: {doc['tasn']:,}/{doc['tan']:,} numbers ({doc['processed']['utilization_rate']*100:.2f}% utilization)")

if __name__ == "__main__":
    main()
        
        # Extract the number data for this quarter
        # In a real implementation, you'd locate the relevant section in the Excel file
        if quarter == "Q1":
            quarter_data = extract_quarter_data(df, 0)  # First quarter data
        else:
            quarter_data = extract_quarter_data(df, 1)  # Second quarter data
        
        # Number allocation data
        audit_data.update({
            "tan": quarter_data.get("tan", 0),
            "tasn": quarter_data.get("tasn", 0),
            "active_non_sub": quarter_data.get("active_non_sub", 0),
            "tin": quarter_data.get("tin", 0),
            "inactive_sub": quarter_data.get("inactive_sub", 0),
            "inactive_non_sub": quarter_data.get("inactive_non_sub", 0),
            "trn": quarter_data.get("trn", 0),
            "reserved_sub": quarter_data.get("reserved_sub", 0),
            "reserved_non_sub": quarter_data.get("reserved_non_sub", 0),
            "gross_add": quarter_data.get("gross_add", 0),
            "net_add": quarter_data.get("net_add", 0),
            "cpb": quarter_data.get("cpb", 0),
            "cpo": quarter_data.get("cpo", 0),
        })
        
        # Calculate processed values
        tan = max(audit_data["tan"], 1)  # Avoid division by zero
        audit_data["processed"] = {
            "utilization_rate": round(audit_data["tasn"] / tan, 4),
            "percent_inactive": round((audit_data["tin"] / tan) * 100, 2),
            "percent_reserved": round((audit_data["trn"] / tan) * 100, 2),
            "porting_balance": audit_data["cpb"] - audit_data["cpo"]
        }
        
        # Extract and process SNR data
        # This would come from a dedicated section or sheet in the Excel workbook
        snr_data = extract_snr_data(df, operator_name, quarter)
        
        # Active SNRs
        audit_data["active_snrs"] = snr_data.get("active_snrs", [])
        audit_data["active_snrs_count"] = len(audit_data["active_snrs"])
        
        # Deactivated SNRs
        audit_data["deactivated_snrs"] = snr_data.get("deactivated_snrs", [])
        audit_data["deactivated_snrs_count"] = len(audit_data["deactivated_snrs"])
        
        # SNR details summary
        audit_data["snr_details"] = {
            "total_snrs": audit_data["active_snrs_count"] + audit_data["deactivated_snrs_count"],
            "service_categories": snr_data.get("service_categories", ["USSD", "SMS", "Voice"]),
            "digit_categories": snr_data.get("digit_categories", {
                "3_digit": 0,
                "4_digit": 0,
                "5_digit": 0,
                "6_digit": 0
            })
        }
        
        # Calculate performance scores
        utilization_score = audit_data["processed"]["utilization_rate"] * 3.33
        porting_factor = min(1.0, max(0, 1 - abs(audit_data["processed"]["porting_balance"]) / 1000))
        porting_score = porting_factor * 3.33
        inactive_factor = max(0, 1 - (audit_data["processed"]["percent_inactive"] / 100))
        inactive_score = inactive_factor * 3.34
        
        audit_data["performance_scores"] = {
            "utilization": round(utilization_score, 2),
            "porting_balance": round(porting_score, 2),
            "inactive": round(inactive_score, 2),
            "overall": round(utilization_score + porting_score + inactive_score, 2)
        }
        
        audit_documents.append(audit_data)
    
    return audit_documents

def extract_quarter_data(df, quarter_index):
    """
    Extract data for a specific quarter from the dataframe
    
    In a real implementation, this would parse the Excel structure 
    to locate and extract the relevant values.
    """
    # Placeholder - would be replaced with actual parsing logic
    base_values = {
        "MTN": {
            0: {  # Q1
                "tan": 10000000,
                "tasn": 8500000,
                "active_non_sub": 50000,
                "tin": 1000000,
                "inactive_sub": 900000,
                "inactive_non_sub": 100000,
                "trn": 450000,
                "reserved_sub": 400000,
                "reserved_non_sub": 50000,
                "gross_add": 200000,
                "net_add": 150000,
                "cpb": 25000,
                "cpo": 15000
            },
            1: {  # Q2
                "tan": 10000000,
                "tasn": 8650000,
                "active_non_sub": 55000,
                "tin": 950000,
                "inactive_sub": 850000,
                "inactive_non_sub": 100000,
                "trn": 400000,
                "reserved_sub": 350000,
                "reserved_non_sub": 50000,
                "gross_add": 220000,
                "net_add": 170000,
                "cpb": 28000,
                "cpo": 16000
            }
        },
        "AT": {
            0: {  # Q1
                "tan": 7000000,
                "tasn": 5500000,
                "active_non_sub": 30000,
                "tin": 1200000,
                "inactive_sub": 1100000,
                "inactive_non_sub": 100000,
                "trn": 300000,
                "reserved_sub": 250000,
                "reserved_non_sub": 50000,
                "gross_add": 120000,
                "net_add": 80000,
                "cpb": 15000,
                "cpo": 18000
            },
            1: {  # Q2
                "tan": 7000000,
                "tasn": 5620000,
                "active_non_sub": 35000,
                "tin": 1150000,
                "inactive_sub": 1050000,
                "inactive_non_sub": 100000,
                "trn": 280000,
                "reserved_sub": 230000,
                "reserved_non_sub": 50000,
                "gross_add": 135000,
                "net_add": 90000,
                "cpb": 17000,
                "cpo": 19000
            }
        },
        "TELECEL": {
            0: {  # Q1
                "tan": 5000000,
                "tasn": 3500000,
                "active_non_sub": 20000,
                "tin": 1300000,
                "inactive_sub": 1200000,
                "inactive_non_sub": 100000,
                "trn": 200000,
                "reserved_sub": 150000,
                "reserved_non_sub": 50000,
                "gross_add": 80000,
                "net_add": 50000,
                "cpb": 10000,
                "cpo": 20000
            },
            1: {  # Q2
                "tan": 5000000,
                "tasn": 3580000,
                "active_non_sub": 25000,
                "tin": 1250000,
                "inactive_sub": 1150000,
                "inactive_non_sub": 100000,
                "trn": 180000,
                "reserved_sub": 130000,
                "reserved_non_sub": 50000,
                "gross_add": 90000,
                "net_add": 60000,
                "cpb": 12000,
                "cpo": 22000
            }
        }
    }
    
    # Extract data for the specified operator and quarter
    # In a real implementation, you'd actually parse the Excel file
    operator_name = next((col for col in df.columns if col in base_values), "MTN")
    return base_values.get(operator_name, {}).get(quarter_index, {})

def extract_snr_data(df, operator_name, quarter):
    """
    Extract SNR data from the Excel workbook
    
    In a real implementation, this would locate and extract SNR information
    from a dedicated section or sheet in the Excel workbook.
    """
    # Sample SNR data for each operator and quarter
    snr_data = {
        "MTN": {
            "Q1": {
                "active_snrs": [
                    {
                        "snr": "100",
                        "status": "active",
                        "activated_date": datetime(2023, 1, 15, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230115001"
                    },
                    {
                        "snr": "1234",
                        "status": "active",
                        "activated_date": datetime(2023, 2, 20, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230220002"
                    },
                    {
                        "snr": "080012345678",
                        "status": "active",
                        "activated_date": datetime(2023, 4, 5, tzinfo=timezone.utc),
                        "type": "tollfree",
                        "purpose": "Customer Support Hotline",
                        "certificate_id": "CERT20230405004"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "54321",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 1, 20, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230601006"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 5,
                    "4_digit": 20,
                    "5_digit": 20,
                    "6_digit": 10
                }
            },
            "Q2": {
                "active_snrs": [
                    {
                        "snr": "100",
                        "status": "active",
                        "activated_date": datetime(2023, 1, 15, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230115001"
                    },
                    {
                        "snr": "1234",
                        "status": "active",
                        "activated_date": datetime(2023, 2, 20, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230220002"
                    },
                    {
                        "snr": "500",
                        "status": "active",
                        "activated_date": datetime(2024, 4, 10, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "New Promotional Campaign",
                        "certificate_id": "CERT20240410007"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "54321",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 1, 20, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230601006"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 7,
                    "4_digit": 20,
                    "5_digit": 21,
                    "6_digit": 10
                }
            }
        },
        "AT": {
            "Q1": {
                "active_snrs": [
                    {
                        "snr": "200",
                        "status": "active",
                        "activated_date": datetime(2023, 2, 10, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230210010"
                    },
                    {
                        "snr": "2345",
                        "status": "active",
                        "activated_date": datetime(2023, 3, 15, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230315011"
                    },
                    {
                        "snr": "080023456789",
                        "status": "active",
                        "activated_date": datetime(2023, 5, 20, tzinfo=timezone.utc),
                        "type": "tollfree",
                        "purpose": "Customer Support Hotline",
                        "certificate_id": "CERT20230520012"
                    },
                    {
                        "snr": "600",
                        "status": "active",
                        "activated_date": datetime(2024, 5, 5, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "New Service",
                        "certificate_id": "CERT20240505015"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "33333",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 2, 15, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230810013"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 3,
                    "4_digit": 12,
                    "5_digit": 15,
                    "6_digit": 3
                }
            },
            "Q2": {
                "active_snrs": [
                    {
                        "snr": "200",
                        "status": "active",
                        "activated_date": datetime(2023, 2, 10, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230210010"
                    },
                    {
                        "snr": "2345",
                        "status": "active",
                        "activated_date": datetime(2023, 3, 15, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230315011"
                    },
                    {
                        "snr": "080023456789",
                        "status": "active",
                        "activated_date": datetime(2023, 5, 20, tzinfo=timezone.utc),
                        "type": "tollfree",
                        "purpose": "Customer Support Hotline",
                        "certificate_id": "CERT20230520012"
                    },
                    {
                        "snr": "600",
                        "status": "active",
                        "activated_date": datetime(2024, 5, 5, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "New Service",
                        "certificate_id": "CERT20240505015"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "33333",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 2, 15, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230810013"
                    },
                    {
                        "snr": "12345",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 4, 10, tzinfo=timezone.utc),
                        "reason": "Service migrated",
                        "type": "shortcode",
                        "purpose": "Legacy Service",
                        "certificate_id": "CERT20230705014"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 5,
                    "4_digit": 12,
                    "5_digit": 15,
                    "6_digit": 4
                }
            }
        },
        "TELECEL": {
            "Q1": {
                "active_snrs": [
                    {
                        "snr": "300",
                        "status": "active",
                        "activated_date": datetime(2023, 3, 5, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230305017"
                    },
                    {
                        "snr": "3456",
                        "status": "active",
                        "activated_date": datetime(2023, 4, 10, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230410018"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "44444",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 1, 10, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230920020"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 2,
                    "4_digit": 8,
                    "5_digit": 10,
                    "6_digit": 2
                }
            },
            "Q2": {
                "active_snrs": [
                    {
                        "snr": "300",
                        "status": "active",
                        "activated_date": datetime(2023, 3, 5, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Customer Service",
                        "certificate_id": "CERT20230305017"
                    },
                    {
                        "snr": "3456",
                        "status": "active",
                        "activated_date": datetime(2023, 4, 10, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "Mobile Money Service",
                        "certificate_id": "CERT20230410018"
                    },
                    {
                        "snr": "900",
                        "status": "active",
                        "activated_date": datetime(2024, 4, 20, tzinfo=timezone.utc),
                        "type": "shortcode",
                        "purpose": "New Promotional Campaign",
                        "certificate_id": "CERT20240420021"
                    }
                ],
                "deactivated_snrs": [
                    {
                        "snr": "44444",
                        "status": "deactivated",
                        "deactivation_date": datetime(2024, 1, 10, tzinfo=timezone.utc),
                        "reason": "Service discontinued",
                        "type": "shortcode",
                        "purpose": "Old Campaign",
                        "certificate_id": "CERT20230920020"
                    }
                ],
                "service_categories": ["USSD", "SMS", "Voice"],
                "digit_categories": {
                    "3_digit": 4,
                    "4_digit": 8, 
                    "5_digit": 11,
                    "6_digit": 2
                }
            }
        }
    }