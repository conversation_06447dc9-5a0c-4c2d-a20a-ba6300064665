from pymongo import MongoClient, ASCENDING, DESCENDING
from datetime import datetime
from werkzeug.security import generate_password_hash
import uuid
import json
import logging

# Setup Logging
logging.basicConfig(
    filename='migration.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
)

# Connect to Old MongoDB
old_client = MongoClient("mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority")
old_db = old_client['captains_log']
old_collection = old_db['app']

# Connect to New MongoDB
new_client = MongoClient("mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0")
new_db = new_client['new_numbering']
users_collection = new_db['users']
applications_collection = new_db['applications']
snrs_collection = new_db['snrs']
vas_table = new_db['vas_table']

# Helper function to generate application ID and certificate ID
def generate_ids():
    timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
    unique_id = str(uuid.uuid4()).split('-')[0]
    return f"APP{timestamp}{unique_id}", f"CERT{timestamp}{unique_id}"

# Migration script
def migrate_data():
    try:
        old_documents = old_collection.find()
        migrated_count = 0
        skipped_count = 0

        for old_doc in old_documents:
            try:
                # Fetch VAS ID from vas_table
                vas_company_name = old_doc.get("vasp", "")
                vas_entry = vas_table.find_one({"VASP": vas_company_name})
                vas_id = vas_entry["ID"] if vas_entry else "UNKNOWN"

                # Generate application_id and certificate_id
                application_id, certificate_id = generate_ids()

                # Check if user with the same company and contact already exists
                existing_user = users_collection.find_one({
                    "vas_company": vas_company_name,
                    "contact": old_doc.get("contact", old_doc.get("applicant", "")),
                    "phone": old_doc.get("phone", "")
                })

                # Insert or use existing user data
                if not existing_user:
                    user_data = {
                        "vas_id": vas_id,
                        "email": old_doc.get("email", ""),
                        "password": generate_password_hash(password="ouken", method='scrypt'),  # Generate or assign a default password
                        "vas_company": vas_company_name,
                        "contact": old_doc.get("contact", old_doc.get("applicant", "")),
                        "created_timestamp": datetime.utcnow(),
                        "national_id": None,  # No data available, needs to be updated later
                        "photo": None,        # No data available, needs to be updated later
                        "phone": old_doc.get("phone", "")
                    }

                    user_id = users_collection.insert_one(user_data).inserted_id
                else:
                    user_id = existing_user["_id"]

                # Insert application data into `applications` collection
                application_data = {
                    "application_id": application_id,
                    "user_id": user_id,
                    "applicant_name": old_doc.get("applicant", "Unknown Applicant"),
                    "application_date": format_application_date(old_doc.get("date")),
                    "status": old_doc.get("status", "Pending Review"),
                    "remarks": " ".join(old_doc.get("comments", [])),
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow(),
                    "certificate_expiry_date": old_doc.get("cert_expiry", "N/A"),
                    "certificate_generated": True if old_doc.get("cert_date") else False,
                    "certificate_start_date": old_doc.get("cert_date", ""),
                    "processed_by": old_doc.get("reviewer", "System")
                }

                applications_collection.insert_one(application_data)

                # Insert SNR data into `snrs` collection
                snr_data = {
                    "application_id": application_id,
                    "snr": old_doc.get("snr", "N/A"),
                    "purpose": old_doc.get("purp", "No purpose specified"),
                    "status": old_doc.get("status", "Pending Review"),
                    "type": old_doc.get("tipe", "shortcode"),
                    "certificate_id": certificate_id
                }

                snrs_collection.insert_one(snr_data)

                migrated_count += 1

            except Exception as e:
                # Log the error for the document that couldn't be migrated
                logging.error(f"Failed to migrate document ID {old_doc.get('_id')}: {e}")
                skipped_count += 1

        # Final summary
        logging.info(f"Migration completed: {migrated_count} documents migrated successfully, {skipped_count} documents skipped.")

    except Exception as e:
        logging.critical(f"Critical error during migration: {e}")

# Helper function to format the application date
def format_application_date(date_str):
    if not date_str:
        return "N/A"
    try:
        return datetime.strptime(date_str, "%m/%d/%Y").strftime("%Y-%m-%d")
    except ValueError:
        logging.warning(f"Date formatting failed for value: {date_str}")
        return "N/A"



# Run the migration script
if __name__ == "__main__":
    migrate_data()
