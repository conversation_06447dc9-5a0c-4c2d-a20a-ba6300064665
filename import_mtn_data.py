#!/usr/bin/env python
"""
Import script for MTN's E.164 data for 2024-H1 to MongoDB.
Usage: python import_mtn_data.py
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime, timezone
import json
from bson import ObjectId

# MongoDB connection details - update as needed
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

# The JSON data for E.164 submissions
e164_submission = {
    "operator_id": "MTN_OP_ID",  # Will be updated if we find the real operator ID
    "operator": "MTN",
    "reporting_period": "2024-h1",
    "contact_person": "MTN Representative",
    "contact_email": "<EMAIL>",
    "notes": "First half 2024 E.164 number audit",
    "status": "approved"
}

# The JSON data for E.164 ranges - derived from the provided table
# Expanded to handle individual ranges instead of hyphenated ranges
e164_ranges = [
    # Original single ranges
    {
        "ndc": "024",
        "type": "WIRELESS DEDICATED",
        "start_block": "0240000000",
        "end_block": "0249999999",
        "total_allocated": 10000000,
        "active_subscriber": 7093192,
        "active_non_subscriber": 35900,
        "inactive": 2604335 + 9400,  # Combining subscriber and non-subscriber inactive
        "reserved": 257173,
        "gross_addition": 14669,
        "net_addition": 14310,
        "ported_in": 62,
        "ported_out": 0,
        "utilization_rate": 71.29,  # (active_subscriber + active_non_subscriber) / total_allocated * 100
        "dormancy_rate": 26.14,     # inactive / total_allocated * 100
        "reservation_rate": 2.57     # reserved / total_allocated * 100
    },
    {
        "ndc": "054",
        "type": "WIRELESS DEDICATED",
        "start_block": "0540000000",
        "end_block": "0549999999",
        "total_allocated": 10000000,
        "active_subscriber": 6186596,
        "active_non_subscriber": 99000,
        "inactive": 3451172,
        "reserved": 263232,
        "gross_addition": 12239,
        "net_addition": 12039,
        "ported_in": 15,
        "ported_out": 0,
        "utilization_rate": 62.86,
        "dormancy_rate": 34.51,
        "reservation_rate": 2.63
    },
    {
        "ndc": "055",
        "type": "WIRELESS DEDICATED",
        "start_block": "0550000000",
        "end_block": "0559999999",
        "total_allocated": 10000000,
        "active_subscriber": 6340389,
        "active_non_subscriber": 0,
        "inactive": 3370683,
        "reserved": 288928,
        "gross_addition": 5446,
        "net_addition": 5275,
        "ported_in": 1,
        "ported_out": 0,
        "utilization_rate": 63.40,
        "dormancy_rate": 33.71,
        "reservation_rate": 2.89
    },
    # Expanded 0591-0599 into 9 separate entries
    {
        "ndc": "0591",
        "type": "WIRELESS SHARED",
        "start_block": "0591000000",
        "end_block": "0591999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0592",
        "type": "WIRELESS SHARED",
        "start_block": "0592000000",
        "end_block": "0592999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0593",
        "type": "WIRELESS SHARED",
        "start_block": "0593000000",
        "end_block": "0593999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0594",
        "type": "WIRELESS SHARED",
        "start_block": "0594000000",
        "end_block": "0594999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0595",
        "type": "WIRELESS SHARED",
        "start_block": "0595000000",
        "end_block": "0595999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0596",
        "type": "WIRELESS SHARED",
        "start_block": "0596000000",
        "end_block": "0596999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0597",
        "type": "WIRELESS SHARED",
        "start_block": "0597000000",
        "end_block": "0597999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0598",
        "type": "WIRELESS SHARED",
        "start_block": "0598000000",
        "end_block": "0598999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    {
        "ndc": "0599",
        "type": "WIRELESS SHARED",
        "start_block": "0599000000",
        "end_block": "0599999999",
        "total_allocated": 1000000,
        "active_subscriber": 619665,
        "active_non_subscriber": 0,
        "inactive": 368279,
        "reserved": 12056,
        "gross_addition": 959,
        "net_addition": 947,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 61.97,
        "dormancy_rate": 36.83,
        "reservation_rate": 1.21
    },
    # Continue with other fixed ranges
    {
        "ndc": "0388",
        "type": "FIXED DEDICATED",
        "start_block": "0388000000",
        "end_block": "0388009999",
        "total_allocated": 10000,
        "active_subscriber": 200,
        "active_non_subscriber": 0,
        "inactive": 0,
        "reserved": 9800,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.00,
        "dormancy_rate": 0.00,
        "reservation_rate": 98.00
    },
    {
        "ndc": "0398",
        "type": "FIXED DEDICATED",
        "start_block": "0398000000",
        "end_block": "0398009999",
        "total_allocated": 10000,
        "active_subscriber": 200,
        "active_non_subscriber": 0,
        "inactive": 0,
        "reserved": 9800,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.00,
        "dormancy_rate": 0.00,
        "reservation_rate": 98.00
    },
    {
        "ndc": "0378",
        "type": "FIXED DEDICATED",
        "start_block": "0378000000",
        "end_block": "0378009999",
        "total_allocated": 10000,
        "active_subscriber": 205,
        "active_non_subscriber": 0,
        "inactive": 0,
        "reserved": 9795,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.05,
        "dormancy_rate": 0.00,
        "reservation_rate": 97.95
    },
    {
        "ndc": "0358",
        "type": "FIXED DEDICATED",
        "start_block": "0358000000",
        "end_block": "0358009999",
        "total_allocated": 10000,
        "active_subscriber": 204,
        "active_non_subscriber": 0,
        "inactive": 0,
        "reserved": 9796,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.04,
        "dormancy_rate": 0.00,
        "reservation_rate": 97.96
    },
    {
        "ndc": "0328",
        "type": "FIXED DEDICATED",
        "start_block": "0328000000",
        "end_block": "0328044999",
        "total_allocated": 45000,
        "active_subscriber": 1801,
        "active_non_subscriber": 0,
        "inactive": 712,
        "reserved": 42487,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 4.00,
        "dormancy_rate": 1.58,
        "reservation_rate": 94.42
    },
    {
        "ndc": "0368",
        "type": "FIXED DEDICATED",
        "start_block": "0368000000",
        "end_block": "0368009999",
        "total_allocated": 10000,
        "active_subscriber": 200,
        "active_non_subscriber": 0,
        "inactive": 0,
        "reserved": 9800,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.00,
        "dormancy_rate": 0.00,
        "reservation_rate": 98.00
    },
    {
        "ndc": "0348",
        "type": "FIXED DEDICATED",
        "start_block": "0348000000",
        "end_block": "0348009999",
        "total_allocated": 10000,
        "active_subscriber": 298,
        "active_non_subscriber": 0,
        "inactive": 100,
        "reserved": 9602,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 2.98,
        "dormancy_rate": 1.00,
        "reservation_rate": 96.02
    },
    {
        "ndc": "0318",
        "type": "FIXED DEDICATED",
        "start_block": "0318000000",
        "end_block": "0318034999",
        "total_allocated": 35000,
        "active_subscriber": 681,
        "active_non_subscriber": 0,
        "inactive": 415,
        "reserved": 33904,
        "gross_addition": 1,
        "net_addition": 1,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 1.95,
        "dormancy_rate": 1.19,
        "reservation_rate": 96.87
    },
    {
        "ndc": "0338",
        "type": "FIXED DEDICATED",
        "start_block": "0338000000",
        "end_block": "0338009999",
        "total_allocated": 10000,
        "active_subscriber": 547,
        "active_non_subscriber": 0,
        "inactive": 1780,
        "reserved": 7673,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 5.47,
        "dormancy_rate": 17.80,
        "reservation_rate": 76.73
    },
    {
        "ndc": "0308",
        "type": "FIXED DEDICATED",
        "start_block": "0308000000",
        "end_block": "0308259999",
        "total_allocated": 260000,
        "active_subscriber": 46689,
        "active_non_subscriber": 0,
        "inactive": 7738,
        "reserved": 205573,
        "gross_addition": 8313,
        "net_addition": 8313,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 17.96,
        "dormancy_rate": 2.98,
        "reservation_rate": 79.07
    },
    # Expanded 0256-0257 into 2 separate entries
    {
        "ndc": "0256",
        "type": "WIRELESS SHARED",
        "start_block": "0256000000",
        "end_block": "0256999999",
        "total_allocated": 1000000,
        "active_subscriber": 546439,
        "active_non_subscriber": 0,
        "inactive": 347780,
        "reserved": 105781,
        "gross_addition": 3839,
        "net_addition": 3774,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 54.64,
        "dormancy_rate": 34.78,
        "reservation_rate": 10.58
    },
    {
        "ndc": "0257",
        "type": "WIRELESS SHARED",
        "start_block": "0257000000",
        "end_block": "0257999999",
        "total_allocated": 1000000,
        "active_subscriber": 546439,
        "active_non_subscriber": 0,
        "inactive": 347779,
        "reserved": 105782,
        "gross_addition": 3838,
        "net_addition": 3774,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 54.64,
        "dormancy_rate": 34.78,
        "reservation_rate": 10.58
    },
    {
        "ndc": "053",
        "type": "WIRELESS DEDICATED",
        "start_block": "0530000000",
        "end_block": "0539999999",
        "total_allocated": 10000000,
        "active_subscriber": 6266010,
        "active_non_subscriber": 0,
        "inactive": 252502,
        "reserved": 3481488,
        "gross_addition": 3289172,
        "net_addition": 3288848,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 62.66,
        "dormancy_rate": 2.53,
        "reservation_rate": 34.81
    }
]

# The JSON data for porting details - derived from the provided porting table
e164_porting_details = [
    {
        "ndc": "023",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 26,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "020",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 64,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "020",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "MTN",
        "target_operator": "OTHER",
        "count": 3,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "050",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 147,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "050",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "MTN",
        "target_operator": "OTHER",
        "count": 4,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "056",
        "range_type": "WIRELESS SHARED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 7,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "026",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 75,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "027",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 55,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "057",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "MTN",
        "count": 47,
        "reporting_period": "2024-h1"
    }
]

def import_data():
    """Import the data into MongoDB"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Add timestamps and generate unique ID
    submission_id = str(ObjectId())
    now = datetime.now(timezone.utc)
    
    # Create the submission
    e164_submission.update({
        "_id": ObjectId(submission_id),
        "created_at": now,
        "updated_at": now,
        "created_by": "import_script",
        "updated_by": "import_script"
    })
    
    try:
        # Get the MTN operator ID from the database if possible
        mtn_operator = db.operators.find_one({"name": "MTN"})
        if mtn_operator:
            e164_submission["operator_id"] = str(mtn_operator["_id"])
            print(f"Found MTN operator ID: {e164_submission['operator_id']}")
    except Exception as e:
        print(f"Warning: Could not find MTN operator in database: {e}")
        print("Using placeholder operator_id")
    
    # Insert submission
    print("Inserting submission...")
    db.e164_submissions.insert_one(e164_submission)
    
    # Update ranges with submission_id and timestamps
    print(f"Processing {len(e164_ranges)} ranges...")
    for r in e164_ranges:
        r.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert ranges
    if e164_ranges:
        db.e164_ranges.insert_many(e164_ranges)
        print(f"Inserted {len(e164_ranges)} ranges")
    
    # Update porting data with submission_id and timestamps
    print(f"Processing {len(e164_porting_details)} porting records...")
    for p in e164_porting_details:
        p.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert porting data
    if e164_porting_details:
        db.e164_porting_details.insert_many(e164_porting_details)
        print(f"Inserted {len(e164_porting_details)} porting records")
    
    print(f"Import complete. Submission ID: {submission_id}")
    return submission_id

if __name__ == "__main__":
    submission_id = import_data()
    print("Data successfully imported!")
    print(f"Submission ID: {submission_id}")