"""
Import Operator Audit Data to MongoDB

This script imports the JSON files generated by the operator_audit_converter.py
script into the MongoDB 'audits' collection.

Usage:
python import_audit_data.py <json_file> [<json_file> ...]

You can specify multiple JSON files to import.
"""

import sys
import os
import json
from pymongo import MongoClient
from datetime import datetime

# MongoDB connection settings - update with your connection string
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"
COLLECTION_NAME = "audits"

def connect_to_mongodb():
    """Connect to MongoDB and return the collection object."""
    try:
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db[COLLECTION_NAME]
        return client, collection
    except Exception as e:
        print(f"Error connecting to MongoDB: {str(e)}")
        sys.exit(1)

def import_audit_data(json_file, collection):
    """Import audit data from a JSON file to MongoDB."""
    print(f"Importing data from {json_file}...")
    
    # Check if file exists
    if not os.path.exists(json_file):
        print(f"Error: File {json_file} not found.")
        return 0
    
    try:
        # Read the JSON file
        with open(json_file, 'r') as f:
            audit_data = json.load(f)
        
        if not audit_data:
            print(f"No data found in {json_file}.")
            return 0
        
        # Process dates in each document
        for doc in audit_data:
            # Convert timestamp string to datetime object if needed
            if isinstance(doc.get('timestamp'), str):
                doc['timestamp'] = datetime.fromisoformat(doc['timestamp'].replace('Z', '+00:00'))
        
        # Insert the documents
        result = collection.insert_many(audit_data)
        
        print(f"Successfully imported {len(result.inserted_ids)} documents from {json_file}.")
        return len(result.inserted_ids)
    
    except Exception as e:
        print(f"Error importing data from {json_file}: {str(e)}")
        import traceback
        traceback.print_exc()
        return 0

def main():
    if len(sys.argv) < 2:
        print("Usage: python import_audit_data.py <json_file> [<json_file> ...]")
        sys.exit(1)
    
    json_files = sys.argv[1:]
    
    # Connect to MongoDB
    client, collection = connect_to_mongodb()
    
    # Import each file
    total_imported = 0
    for json_file in json_files:
        imported = import_audit_data(json_file, collection)
        total_imported += imported
    
    print(f"Total documents imported: {total_imported}")
    
    # Close the MongoDB connection
    client.close()

if __name__ == "__main__":
    main()
