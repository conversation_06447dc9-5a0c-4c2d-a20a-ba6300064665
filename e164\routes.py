# e164_routes.py - E.164 routes using Application Factory Pattern
from collections import defaultdict
import random
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet
from io import BytesIO
from flask import send_file


from .utils import calculate_e164_invoice, generate_invoice_excel # Assuming utils.py is in the same 'e164' package


from datetime import datetime, timezone

from flask import Blueprint, jsonify, render_template, redirect, url_for, request, flash, session, current_app
from flask_login import login_required, login_user, logout_user, current_user

from werkzeug.security import check_password_hash, generate_password_hash
from bson.objectid import ObjectId
import pandas as pd
import json

from extensions import mongo
from models import User
from forms import LoginForm, RegistrationForm, UserCreationForm
from e164.utils import allowed_file, admin_required

# Import utilities - using function imports to avoid circular dependencies
from .utils import (
    log_activity, add_notification, calculate_number_metrics,
    serialize_to_json
)

# Import the blueprint from the package
from . import e164_bp  # Using relative import to get the blueprint




# Helper functions specific to e164 routes

def fetch_operator_ndcs(operator_id):
    """Fetch NDCs allocated to a specific operator"""
    try:
        # Try with ObjectId first (if operator_id is a valid ObjectId string)
        try:
            from bson.objectid import ObjectId
            query = {'operator_id': ObjectId(operator_id)}
        except:
            # If conversion fails, use the string directly
            query = {'operator_id': operator_id}

        allocations = list(mongo.db.number_allocations.find(query))

        # If no results, try with operator name as fallback
        if not allocations:
            # Try to get operator name if we have an ObjectId
            try:
                operator = mongo.db.operators.find_one({'_id': ObjectId(operator_id)})
                if operator:
                    query = {'operator': operator['name']}
                    allocations = list(mongo.db.number_allocations.find(query))
            except:
                pass

        # Debug logging
        print(f"Query: {query}, Results: {len(allocations)}")

        result = []
        for allocation in allocations:
            result.append({
                'ndc': allocation['ndc'],
                'type': allocation['type'],
                'start_block': allocation['start_block'],
                'end_block': allocation['end_block'],
                'total_allocated': allocation['total_allocated']
            })

        return result
    except Exception as e:
        print(f"Error in fetch_operator_ndcs: {e}")
        return []

def aggregate_submission_data(submission_id):
    """Aggregate data from a submission for analytics"""
    # Get the submission data
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return None

    # Get all ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))

    # Aggregate metrics
    total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
    total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
    total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
    total_inactive = sum(r.get('inactive', 0) for r in ranges)
    total_reserved = sum(r.get('reserved', 0) for r in ranges)
    total_ported_in = sum(r.get('ported_in', 0) for r in ranges)
    total_ported_out = sum(r.get('ported_out', 0) for r in ranges)

    # Calculate overall metrics
    total_active = total_active_subscriber + total_active_non_subscriber
    overall_utilization = (total_active / total_allocated * 100) if total_allocated > 0 else 0

    return {
        'submission_id': submission_id,
        'operator': submission.get('operator'),
        'period': submission.get('reporting_period'),
        'status': submission.get('status'),
        'submission_date': submission.get('created_at'),
        'metrics': {
            'total_allocated': total_allocated,
            'total_active': total_active,
            'total_active_subscriber': total_active_subscriber,
            'total_active_non_subscriber': total_active_non_subscriber,
            'total_inactive': total_inactive,
            'total_reserved': total_reserved,
            'total_ported_in': total_ported_in,
            'total_ported_out': total_ported_out,
            'utilization_rate': round(overall_utilization, 2),
            'net_porting': total_ported_in - total_ported_out
        },
        'ranges_count': len(ranges)
    }

def get_historical_data(operator_id, limit=5):
    """Get historical submission data for trend analysis"""
    # Get the latest submissions for this operator
    submissions = list(mongo.db.e164_submissions.find(
        {'operator_id': operator_id, 'status': 'approved'}
    ).sort('reporting_period', -1).limit(limit))

    results = []
    for submission in submissions:
        # Aggregate data for each submission
        aggregated = aggregate_submission_data(str(submission['_id']))
        if aggregated:
            results.append(aggregated)

    return results

def calculate_projected_exhaustion(ndc, growth_rate):
    """Calculate projected exhaustion date for an NDC based on growth rate"""
    # Get the latest data for this NDC
    latest_range = mongo.db.e164_ranges.find_one(
        {'ndc': ndc, 'status': 'approved'},
        sort=[('created_at', -1)]
    )

    if not latest_range:
        return None

    # Calculate remaining numbers
    total_allocated = latest_range.get('total_allocated', 0)
    total_active = latest_range.get('active_subscriber', 0) + latest_range.get('active_non_subscriber', 0)
    remaining = total_allocated - total_active

    # Calculate years until exhaustion (simple linear projection)
    if growth_rate <= 0:
        return float('inf')  # Never exhausts at negative growth

    years_until_exhaustion = remaining / (total_active * (growth_rate / 100))
    return round(years_until_exhaustion, 1)

def compare_operators_utilization():
    """Compare utilization rates across operators"""
    # Get the latest submission for each operator
    pipeline = [
        {"$sort": {"created_at": -1}},
        {"$group": {
            "_id": "$operator_id",
            "submission_id": {"$first": "$_id"},
            "operator": {"$first": "$operator"},
            "reporting_period": {"$first": "$reporting_period"},
            "created_at": {"$first": "$created_at"}
        }}
    ]

    latest_submissions = list(mongo.db.e164_submissions.aggregate(pipeline))

    # Get aggregated data for each submission
    results = []
    for submission in latest_submissions:
        aggregated = aggregate_submission_data(str(submission['submission_id']))
        if aggregated:
            results.append({
                'operator': submission['operator'],
                'period': submission['reporting_period'],
                'utilization_rate': aggregated['metrics']['utilization_rate'],
                'total_allocated': aggregated['metrics']['total_allocated'],
                'total_active': aggregated['metrics']['total_active']
            })

    return results



# --- NEW OR MODIFIED HELPER FUNCTION ---
def get_or_calculate_submission_summary(submission_id, operator_id):
    """
    Retrieves summary metrics for a submission. If they don't exist,
    calculates them from range data, stores them in the submission document,
    and returns them.

    This function remains unchanged as it correctly calculates individual metrics.
    Use get_grouped_trend_data_by_metric() to get data organized for grouped bar charts.
    """
    if not submission_id:
        return None

    # Ensure submission_id is ObjectId for querying ranges, but string for querying submissions if needed
    try:
        obj_submission_id = ObjectId(submission_id)
        str_submission_id = str(submission_id)
    except:
        current_app.logger.error(f"Invalid submission_id format: {submission_id}")
        return None

    submission = mongo.db.e164_submissions.find_one({'_id': obj_submission_id})

    if not submission:
        current_app.logger.warning(f"Submission not found for summary calculation: {submission_id}")
        return None

    # Check if summary already exists and is complete
    if 'summary' in submission and all(k in submission['summary'] for k in ['utilization_rate', 'dormancy_rate', 'reservation_rate']):
        # Optional: Add check for growth rate if implementing
        # Convert dates if needed before returning
        if 'calculated_at' in submission['summary'] and isinstance(submission['summary']['calculated_at'], datetime):
             submission['summary']['calculated_at'] = submission['summary']['calculated_at'].isoformat()
        return submission['summary']

    current_app.logger.info(f"Calculating summary metrics for submission: {submission_id}")

    # --- Calculation Logic ---
    ranges = list(mongo.db.e164_ranges.find({'submission_id': str_submission_id})) # Query ranges using string ID

    if not ranges:
         current_app.logger.warning(f"No ranges found for submission {submission_id} during summary calculation.")
         # Store empty summary to avoid recalculating constantly
         empty_summary = {
            'total_allocated': 0, 'total_active': 0, 'total_active_subscriber': 0,
            'total_active_non_subscriber': 0, 'total_inactive': 0, 'total_reserved': 0,
            'total_ported_in': 0, 'total_ported_out': 0, 'utilization_rate': 0,
            'dormancy_rate': 0, 'reservation_rate': 0, 'growth_rate': None, # Set growth to None initially
            'net_porting': 0, 'ranges_count': 0, 'calculated_at': datetime.now(timezone.utc).isoformat(),
            'calculation_status': 'no_ranges_found'
         }
         mongo.db.e164_submissions.update_one(
             {'_id': obj_submission_id},
             {'$set': {'summary': empty_summary, 'updated_at': datetime.now(timezone.utc)}}
         )
         return empty_summary


    # Aggregate metrics from ranges
    total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
    total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
    total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
    total_inactive = sum(r.get('inactive', 0) for r in ranges)
    total_reserved = sum(r.get('reserved', 0) for r in ranges)
    total_ported_in = sum(r.get('ported_in', 0) for r in ranges)
    total_ported_out = sum(r.get('ported_out', 0) for r in ranges)

    # Calculate derived metrics
    total_active = total_active_subscriber + total_active_non_subscriber
    utilization_rate = (total_active / total_allocated * 100) if total_allocated > 0 else 0
    dormancy_rate = (total_inactive / total_allocated * 100) if total_allocated > 0 else 0
    reservation_rate = (total_reserved / total_allocated * 100) if total_allocated > 0 else 0
    net_porting = total_ported_in - total_ported_out

    # --- Growth Rate Calculation (Optional but recommended) ---
    growth_rate = None
    previous_summary = None
    # Find the immediately preceding *approved* submission for this operator
    previous_submission_cursor = mongo.db.e164_submissions.find(
        {
            'operator_id': str(operator_id), # Ensure operator_id is string for query
            'status': 'approved',
            'reporting_period': {'$lt': submission.get('reporting_period')} # Find older periods
        }
    ).sort('reporting_period', -1).limit(1) # Get the most recent older one

    previous_submission_list = list(previous_submission_cursor)
    if previous_submission_list:
        prev_sub = previous_submission_list[0]
        # Get or calculate summary for the previous submission too
        previous_summary = get_or_calculate_submission_summary(str(prev_sub['_id']), operator_id)
        if previous_summary and previous_summary.get('total_active') is not None and previous_summary.get('total_active') > 0:
             # Simple growth: ((current_active - previous_active) / previous_active) * 100
             # Adjust logic based on how 'growth' should be defined (e.g., annualized, period-over-period)
             growth_rate = ((total_active - previous_summary['total_active']) / previous_summary['total_active']) * 100

    # Prepare the summary document
    summary = {
        'total_allocated': total_allocated,
        'total_active': total_active,
        'total_active_subscriber': total_active_subscriber,
        'total_active_non_subscriber': total_active_non_subscriber,
        'total_inactive': total_inactive,
        'total_reserved': total_reserved,
        'total_ported_in': total_ported_in,
        'total_ported_out': total_ported_out,
        'utilization_rate': round(utilization_rate, 2),
        'dormancy_rate': round(dormancy_rate, 2),
        'reservation_rate': round(reservation_rate, 2),
        'growth_rate': round(growth_rate, 2) if growth_rate is not None else None,
        'net_porting': net_porting,
        'ranges_count': len(ranges),
        'calculated_at': datetime.now(timezone.utc).isoformat(), # Store as ISO string
        'calculation_status': 'calculated'
    }

    # Store the summary back into the submission document
    try:
        update_result = mongo.db.e164_submissions.update_one(
            {'_id': obj_submission_id},
            {'$set': {'summary': summary, 'updated_at': datetime.now(timezone.utc)}}
        )
        if update_result.modified_count == 0 and update_result.matched_count > 0:
             current_app.logger.warning(f"Summary calculated but DB update failed or did not change document for submission {submission_id}")
        elif update_result.matched_count == 0:
             current_app.logger.error(f"Failed to find submission {submission_id} during summary update.")

    except Exception as e:
        current_app.logger.error(f"Error storing summary for submission {submission_id}: {e}")
        # Return the calculated summary anyway, but log the storage failure
        summary['calculation_status'] = 'calculation_complete_storage_failed'

    return summary


# --- NEW HELPER FUNCTION ---
def calculate_market_share_data():
    """
    Calculates market share based on total allocated numbers from the
    latest APPROVED reporting period for all active operators.

    Returns:
        dict: Contains 'period', 'total_allocation', 'operator_shares' list,
              or None if data is insufficient.
    """
    latest_approved_period = None
    # Find the most recent reporting period that has at least one approved submission
    latest_sub = mongo.db.e164_submissions.find_one(
        {'status': 'approved'},
        sort=[('reporting_period', -1)]
    )
    if latest_sub:
        latest_approved_period = latest_sub.get('reporting_period')
    else:
        current_app.logger.warning("Market Share: No approved submissions found to determine latest period.")
        return None # Cannot calculate without an approved period

    current_app.logger.info(f"Market Share: Calculating for period '{latest_approved_period}'")

    # Get all approved submissions for this specific period
    approved_submissions = list(mongo.db.e164_submissions.find({
        'reporting_period': latest_approved_period,
        'status': 'approved'
    }))

    if not approved_submissions:
         current_app.logger.warning(f"Market Share: No approved submissions found for the period '{latest_approved_period}'.")
         return None

    # Get operator details (ID and Name) - Cache this if possible for performance
    operators_cursor = mongo.db.operators.find({}, {'_id': 1, 'name': 1})
    operator_map = {str(op['_id']): op['name'] for op in operators_cursor}

    operator_shares = []
    total_market_allocation = 0

    for sub in approved_submissions:
        op_id_str = str(sub.get('operator_id'))
        op_name = operator_map.get(op_id_str, f"Unknown Operator ({op_id_str[:4]}...)")
        sub_id_str = str(sub['_id'])

        # Use the pre-calculated summary if available, otherwise calculate it
        summary = sub.get('summary')
        if not summary or 'total_allocated' not in summary:
            # Call the existing helper to get/calculate summary
            # Pass operator_id if needed by the helper
            summary = get_or_calculate_submission_summary(sub_id_str, op_id_str)

        allocated = summary.get('total_allocated', 0) if summary else 0

        if allocated > 0: # Only include operators with allocations in the share data
            operator_shares.append({
                'operator_id': op_id_str,
                'operator_name': op_name,
                'allocated': allocated
            })
            total_market_allocation += allocated
        else:
             current_app.logger.info(f"Market Share: Operator {op_name} has zero allocation in period {latest_approved_period}, excluding.")


    if total_market_allocation == 0:
        current_app.logger.warning(f"Market Share: Total market allocation is zero for period '{latest_approved_period}'.")
        # Return structure indicating zero total, allowing JS to handle it
        return {
            'period': latest_approved_period,
            'total_allocation': 0,
            'operator_shares': []
        }


    # Optional: Sort shares for consistent chart rendering
    operator_shares.sort(key=lambda x: x['allocated'], reverse=True)

    return {
        'period': latest_approved_period,
        'total_allocation': total_market_allocation,
        'operator_shares': operator_shares # Already contains serialized data if summary was fetched/calculated correctly
    }


def get_grouped_trend_data(metric='utilization_rate', periods_limit=8):
    """
    Fetches historical approved data and structures it for a grouped bar chart
    showing a specific metric per operator over time.

    Args:
        metric (str): The metric key within the 'summary' object to plot
                      (e.g., 'utilization_rate', 'dormancy_rate').
        periods_limit (int): Max number of recent periods to include.

    Returns:
        dict: Data structured for Chart.js grouped bar chart
              {'labels': [period1, period2,...], 'datasets': [{label: op1, data:[...]}, {label: op2, data:[...]}]}
              Returns None if no data is available.

    Note: This function groups data by operator. Use get_grouped_trend_data_by_metric()
          to group by metric type instead for a different visualization.
    """
    current_app.logger.info(f"Calculating grouped trend data for metric: {metric}")
    try:
        # Fetch all approved submissions, getting necessary fields
        # Ensure summaries are present - this might be slow if many need calculation
        submissions_cursor = mongo.db.e164_submissions.find(
            {'status': 'approved'},
            {'reporting_period': 1, 'operator': 1, 'operator_id': 1, 'summary': 1, '_id': 1}
        ).sort('reporting_period', -1) # Sort newest first initially

        # Process submissions, ensuring summaries exist and getting operator names
        processed_data = {} # Structure: {period: {operator_name: metric_value}}
        all_periods = set()
        all_operators = set()
        operator_name_map = {} # Map ID to Name

        for sub in submissions_cursor:
            period = sub.get('reporting_period')
            op_id = str(sub.get('operator_id'))
            op_name = sub.get('operator')
            sub_id = str(sub['_id'])

            if not period or not op_id or not op_name:
                continue # Skip incomplete records

            all_periods.add(period)
            all_operators.add(op_name)
            if op_id not in operator_name_map:
                 operator_name_map[op_id] = op_name

            # Get summary, calculate if missing
            summary = sub.get('summary')
            if not summary or metric not in summary:
                # This call might be slow if run frequently on many submissions
                summary = get_or_calculate_submission_summary(sub_id, op_id)

            metric_value = summary.get(metric, 0) if summary else 0 # Default to 0 if summary fails

            if period not in processed_data:
                processed_data[period] = {}
            processed_data[period][op_name] = metric_value

        if not processed_data:
            current_app.logger.warning("Grouped Trend: No processed data available.")
            return None

        # Determine periods to display (limit to most recent)
        sorted_periods = sorted(list(all_periods), reverse=True)
        display_periods = sorted_periods[:periods_limit]
        display_periods.reverse() # Reverse to get chronological order for X-axis

        # Determine operators to display (all found)
        display_operators = sorted(list(all_operators))

        # Assign consistent colors to operators
        # Basic color cycling (replace with a better palette if needed)
        colors = [
             'rgba(255, 99, 132, 0.7)', 'rgba(54, 162, 235, 0.7)', 'rgba(75, 192, 192, 0.7)',
             'rgba(255, 205, 86, 0.7)', 'rgba(153, 102, 255, 0.7)', 'rgba(255, 159, 64, 0.7)'
         ]

        # Build Chart.js datasets
        datasets = []
        for i, op_name in enumerate(display_operators):
            operator_data = []
            for period in display_periods:
                # Get the value for this operator in this period, default to 0 if missing
                value = processed_data.get(period, {}).get(op_name, 0)
                operator_data.append(value)

            datasets.append({
                'label': op_name,
                'data': operator_data,
                'backgroundColor': colors[i % len(colors)],
                # Optional: Add border color etc.
                # 'borderColor': colors[i % len(colors)].replace('0.7', '1'),
                # 'borderWidth': 1
            })



        chartjs_data = {
            'labels': display_periods,
            'datasets': datasets
        }
        print("DEBUG: get_grouped_trend_data() returning:", chartjs_data)
        current_app.logger.info(f"Grouped Trend: Generated data for {len(display_operators)} operators across {len(display_periods)} periods.")
        return chartjs_data

    except Exception as e:
        current_app.logger.error(f"Error generating grouped trend data: {e}", exc_info=True)
        return None


def get_grouped_trend_data_by_metric(periods_limit=8, metrics=None):
    """
    Fetches historical approved data and structures it for a grouped bar chart
    showing multiple metrics for each operator over time.

    This function organizes data by metric type rather than by operator,
    creating separate datasets for utilization_rate, dormancy_rate, etc.

    Args:
        periods_limit (int): Max number of recent periods to include
        metrics (list): List of metrics to include, defaults to ['utilization_rate', 'dormancy_rate', 'reservation_rate']

    Returns:
        dict: Data structured for Chart.js grouped bar chart with multiple metrics per operator
              {
                'labels': [period1, period2,...],
                'datasets': [
                  {label: 'Utilization - Op1', data:[...], backgroundColor: '...'},
                  {label: 'Dormancy - Op1', data:[...], backgroundColor: '...'},
                  {label: 'Utilization - Op2', data:[...], backgroundColor: '...'},
                  ...
                ]
              }
              Returns None if no data is available.
    """
    current_app.logger.info(f"Calculating grouped trend data by metric types")

    # Default metrics if none provided
    if metrics is None:
        metrics = ['utilization_rate', 'dormancy_rate', 'reservation_rate']

    # Define friendly names for metrics
    metric_names = {
        'utilization_rate': 'Utilization',
        'dormancy_rate': 'Dormancy',
        'reservation_rate': 'Reservation',
        'growth_rate': 'Growth'
    }

    # Define colors for each metric type (will be adjusted for each operator)
    base_colors = {
        'utilization_rate': 'rgba(75, 192, 192, {opacity})',  # Teal
        'dormancy_rate': 'rgba(255, 159, 64, {opacity})',      # Orange
        'reservation_rate': 'rgba(153, 102, 255, {opacity})',   # Purple
        'growth_rate': 'rgba(54, 162, 235, {opacity})'          # Blue
    }

    try:
        # Fetch all approved submissions, getting necessary fields
        submissions_cursor = mongo.db.e164_submissions.find(
            {'status': 'approved'},
            {'reporting_period': 1, 'operator': 1, 'operator_id': 1, 'summary': 1, '_id': 1}
        ).sort('reporting_period', -1)  # Sort newest first initially

        # Process submissions to get metrics by period and operator
        processed_data = {}
        operators_set = set()
        periods_set = set()

        for submission in submissions_cursor:
            period = submission.get('reporting_period')
            if not period:
                continue

            op_id = submission.get('operator_id')
            op_name = submission.get('operator', f"Op {op_id[-4:] if op_id else '??'}")
            operators_set.add(op_name)
            periods_set.add(period)

            # Get or calculate summary
            summary = submission.get('summary')
            if not summary:
                summary = get_or_calculate_submission_summary(str(submission['_id']), op_id)

            if not summary:
                continue

            # Initialize period data if needed
            if period not in processed_data:
                processed_data[period] = {}

            # Initialize operator data if needed
            if op_name not in processed_data[period]:
                processed_data[period][op_name] = {}

            # Store each metric value
            for metric in metrics:
                metric_value = summary.get(metric, 0) if summary else 0
                processed_data[period][op_name][metric] = metric_value

        if not processed_data:
            current_app.logger.warning("Grouped Trend By Metric: No processed data available.")
            return None

        # Get periods to display (limit to most recent N)
        display_periods = sorted(periods_set, reverse=True)[:periods_limit]
        display_periods.reverse()  # Chronological order for chart

        # Get operators to display
        display_operators = sorted(operators_set)

        # Create datasets for Chart.js
        datasets = []

        # For each operator and metric combination, create a dataset
        for op_idx, operator in enumerate(display_operators):
            for metric_idx, metric in enumerate(metrics):
                # Create a unique color for this operator-metric combination
                # Adjust opacity based on metric type to differentiate
                opacity = 0.7 - (0.1 * metric_idx)  # Different opacity for each metric
                color = base_colors.get(metric, 'rgba(128, 128, 128, {opacity})').format(opacity=opacity)

                # Adjust hue slightly for each operator to differentiate
                if op_idx > 0:
                    # Simple way to adjust color - in a real app, you might use a more sophisticated approach
                    if 'rgba(' in color:
                        parts = color.replace('rgba(', '').replace(')', '').split(',')
                        r = max(0, min(255, int(parts[0]) + (op_idx * 20) % 100))
                        g = max(0, min(255, int(parts[1]) + (op_idx * 15) % 100))
                        b = max(0, min(255, int(parts[2]) + (op_idx * 25) % 100))
                        color = f"rgba({r}, {g}, {b}, {parts[3]})"

                # Create dataset
                dataset = {
                    'label': f"{metric_names.get(metric, metric)} - {operator}",
                    'data': [],
                    'backgroundColor': color,
                    'borderColor': color.replace('{opacity}', '1.0'),
                    'borderWidth': 1
                }

                # Fill in data for each period
                for period in display_periods:
                    if period in processed_data and operator in processed_data[period] and metric in processed_data[period][operator]:
                        dataset['data'].append(processed_data[period][operator][metric])
                    else:
                        dataset['data'].append(0)  # No data for this period/operator/metric

                datasets.append(dataset)

        # Create final chart data structure
        chartjs_data = {
            'labels': display_periods,
            'datasets': datasets
        }

        print("DEBUG: get_grouped_trend_data_by_metric() returning:", chartjs_data)
        current_app.logger.info(f"Grouped Trend By Metric: Generated data for {len(display_operators)} operators with {len(metrics)} metrics across {len(display_periods)} periods.")
        return chartjs_data

    except Exception as e:
        current_app.logger.error(f"Error generating grouped trend data by metric: {e}", exc_info=True)
        return None






# routes.py

# ... (other imports like Blueprint, jsonify, mongo, current_app, login_required) ...
# from . import e164_bp # Ensure blueprint is imported

# --- NEW API ENDPOINT FOR APPROVED PERIODS ---
@e164_bp.route('/api/analytics/periods')
@login_required
def get_approved_reporting_periods():
    """
    API endpoint to retrieve a sorted list of unique reporting periods
    that have at least one 'approved' submission.
    """
    if not current_user.is_admin:
         # Optional: Restrict to admin, though operators might also benefit from seeing periods
         current_app.logger.warning(f"Non-admin user {current_user.email} tried to access approved periods API.")
         return jsonify({'success': False, 'error': 'Unauthorized access.'}), 403
        #  pass
     # Allow non-admins for now, filter later if needed

    try:
        # Find distinct 'reporting_period' values where status is 'approved'
        distinct_periods = mongo.db.e164_submissions.distinct(
            'reporting_period',
            {'status': 'approved'}
        )

        # Sort the periods, typically reverse chronologically (e.g., '2024-h1', '2023-h2', ...)
        # Simple string sort works if format is consistent YYYY-hX
        sorted_periods = sorted(distinct_periods, reverse=True)

        current_app.logger.info(f"API: Found {len(sorted_periods)} approved reporting periods.")

        # Format for dropdown if desired (matching format from dashboard route)
        formatted_periods = []
        for period_val in sorted_periods:
             try:
                 year, half = period_val.split('-')
                 half_text = "First" if half == 'h1' else "Second"
                 formatted_periods.append({'value': period_val, 'text': f"{half_text} Half {year}"})
             except ValueError:
                 # Handle unexpected format if necessary
                  formatted_periods.append({'value': period_val, 'text': period_val})


        return jsonify({
            'success': True,
            # Return both raw values and formatted text
            # 'periods': sorted_periods
            'periods': formatted_periods # Return list of objects
        })

    except Exception as e:
        current_app.logger.error(f"API: Error fetching approved reporting periods: {e}", exc_info=True)
        return jsonify({'success': False, 'error': 'An internal server error occurred.'}), 500









# --- MODIFIED dashboard() route ---

@e164_bp.route('/')
@e164_bp.route('/dashboard')
@login_required
def dashboard():
    """
    Renders the main dashboard/submission form page.
    Provides latest data for initial load for both admin and operator.
    """
    try:
        active_tab = request.args.get('tab', None)
        is_admin_user = getattr(current_user, 'is_admin', False)
        current_operator_id = str(getattr(current_user, 'operator_id', None)) if not is_admin_user else None

        # --- Fetch Operators --- (Keep existing code)
        operators_cursor = mongo.db.operators.find({}, {'_id': 1, 'name': 1}).sort('name', 1)
        all_operators = [{'id': str(op['_id']), 'name': op['name']} for op in operators_cursor]

        # --- Fetch Reporting Periods --- (Keep existing code)
        current_year = datetime.now().year
        current_month = datetime.now().month
        current_period = f"{current_year}-h1" if current_month <= 6 else f"{current_year}-h2"
        reporting_periods = [
            {'value': f"{y}-h{p}", 'text': f"{('First' if p==1 else 'Second')} Half {y}", 'selected': (f"{y}-h{p}" == current_period)}
            for y in [current_year, current_year-1] for p in [1, 2]
        ]

        # --- Initialize Variables ---
        latest_data = None # This will be populated for BOTH admin and operator initial load
        historical_data = []
        chart_data = {"labels": [], "utilization": [], "dormancy": [], "reservation": [], "growth": []}
        operator_comparison = []
        market_share_result = calculate_market_share_data() # Calculate once
        trend_chart_data = None # Initialize new variable for trend chart



        # --- Fetch Data for Initial Load ---
        if is_admin_user:
            # --- Admin View ---
            operator_comparison = compare_operators_utilization() # Fetch comparison data

            # Fetch overall latest submission for default view (e.g., status distribution)
            latest_overall_cursor = mongo.db.e164_submissions.find(
                # Optional: Filter by status: 'approved' if desired for default view
                 {'status': 'approved'}
            ).sort([('reporting_period', -1), ('created_at', -1)]).limit(1)
            latest_overall_list = list(latest_overall_cursor)

            if latest_overall_list:
                latest_raw_data = latest_overall_list[0]
                latest_submission_id = str(latest_raw_data['_id'])
                # Need operator_id of this specific submission to calculate its summary correctly
                latest_submission_operator_id = str(latest_raw_data.get('operator_id'))

                calculated_summary = get_or_calculate_submission_summary(latest_submission_id, latest_submission_operator_id)
                latest_data = serialize_to_json(latest_raw_data) # Serialize
                if calculated_summary:
                    latest_data['summary'] = calculated_summary
                else:
                    latest_data['summary'] = {}
                    current_app.logger.error(f"Admin Default: Failed to get summary for latest overall submission {latest_submission_id}")
            else:
                 current_app.logger.info("Admin Default: No submissions found to provide initial latest_data.")


            # Fetch historical submissions for the history table view (admin sees all)
            historical_cursor = mongo.db.e164_submissions.find().sort([('reporting_period', -1), ('created_at', -1)]).limit(20)
            # Ensure summaries are included for historical table/potential trends if needed later
            temp_historical_data = []
            for sub in historical_cursor:
                 sub_id_str = str(sub['_id'])
                 op_id_str = str(sub.get('operator_id'))
                 summary = sub.get('summary') or get_or_calculate_submission_summary(sub_id_str, op_id_str)
                 processed_sub = serialize_to_json(sub)
                 processed_sub['summary'] = summary or {}
                 temp_historical_data.append(processed_sub)
            historical_data = temp_historical_data


            # Populate initial admin chart data (e.g., trend chart based on comparison)
            # Note: chart_data might be less relevant now if JS fetches filtered data

            # Get data for grouped bar chart showing multiple metrics per operator
            trend_chart_data = get_grouped_trend_data_by_metric(
                periods_limit=6,  # Get data for last 6 periods
                metrics=['utilization_rate', 'dormancy_rate', 'reservation_rate']
            )


            # chart_data['labels'] = [str(op.get('operator', 'Unknown')) for op in operator_comparison]
            # chart_data['utilization'] = [op.get('utilization_rate', 0) for op in operator_comparison]

            if not active_tab:
                active_tab = 'analytics'

        else:
            # --- Operator View ---
            if current_operator_id:
                # Fetch latest submission for this operator (existing logic is good)
                latest_submission_cursor = mongo.db.e164_submissions.find(
                    {'operator_id': current_operator_id}
                    # Optional: Add 'status': 'approved' if default should only be approved
                ).sort([('reporting_period', -1), ('created_at', -1)]).limit(1)
                latest_submission_list = list(latest_submission_cursor)

                if latest_submission_list:
                    # (Keep existing operator logic for populating latest_data and summary)
                    latest_raw_data = latest_submission_list[0]
                    latest_submission_id = str(latest_raw_data['_id'])
                    calculated_summary = get_or_calculate_submission_summary(latest_submission_id, current_operator_id)
                    latest_data = serialize_to_json(latest_raw_data)
                    latest_data['summary'] = calculated_summary or {}
                    if not calculated_summary:
                         current_app.logger.error(f"Operator: Failed summary for {latest_submission_id}")

                else:
                    current_app.logger.info(f"No submissions found for operator {current_operator_id}")

                # Fetch historical submissions for this operator (existing logic is good)
                historical_cursor = mongo.db.e164_submissions.find(
                    {'operator_id': current_operator_id}
                    # Optional: Filter by status: 'approved' for trends
                ).sort([('reporting_period', -1), ('created_at', -1)]).limit(10)

                temp_historical_data = []
                for sub in historical_cursor:
                     sub_id_str = str(sub['_id'])
                     # op_id is current_operator_id here
                     summary = sub.get('summary') or get_or_calculate_submission_summary(sub_id_str, current_operator_id)
                     processed_sub = serialize_to_json(sub)
                     processed_sub['summary'] = summary or {}
                     temp_historical_data.append(processed_sub)
                historical_data = temp_historical_data


                # Prepare operator chart data (trends)
                if historical_data:
                    historical_data.reverse() # Chronological order for charts
                    chart_data['labels'] = [sub.get('reporting_period', 'N/A') for sub in historical_data]
                    chart_data['utilization'] = [sub.get('summary', {}).get('utilization_rate', 0) for sub in historical_data]
                    chart_data['dormancy'] = [sub.get('summary', {}).get('dormancy_rate', 0) for sub in historical_data]
                    chart_data['reservation'] = [sub.get('summary', {}).get('reservation_rate', 0) for sub in historical_data]
                    chart_data['growth'] = [sub.get('summary', {}).get('growth_rate', 0) for sub in historical_data]

            else:
                flash("User is not associated with an operator.", "warning")

            if not active_tab:
                active_tab = 'submission'



        # --- Prepare data payload for JavaScript ---
        js_payload = {
            'latestData': latest_data, # Now populated for admin initial load too
            'historicalData': historical_data,
            'chartData': chart_data,
            'isAdmin': is_admin_user,
            'activeTab': active_tab,
            'allOperators': all_operators,
            'reportingPeriods': reporting_periods,
            'marketShareResult': market_share_result,
            'trendChartData': trend_chart_data,
            'operatorComparison': operator_comparison if is_admin_user else None
        }

        # --- Render Template ---
        return render_template(
            'e164/submission_form.html',
            title="E.164 Dashboard",
            is_admin=is_admin_user, # Still needed for template conditionals
            E164Data=js_payload
        )

    except Exception as e:
        current_app.logger.error(f"Error loading dashboard: {e}", exc_info=True)
        flash(f"An error occurred while loading the dashboard: {e}", "danger")
        # Consider redirecting to a safer fallback URL
        return redirect(url_for('dashboard')) # Or your main app index




# Add these routes to your existing e164 routes.py file

@e164_bp.route('/admin/users', methods=['GET'])
@login_required
def list_users():
    """List all users (admin only)"""
    if not current_user.is_admin:
        flash('You do not have permission to access this page.', 'danger')
        return redirect(url_for('e164.dashboard'))

    users = list(mongo.db.users.find())

    # Add operator or VAS info to each user
    for user in users:
        user['_id'] = str(user['_id'])
        if 'operator_id' in user:
            operator = mongo.db.operators.find_one({'_id': ObjectId(user['operator_id'])})
            user['operator_name'] = operator['name'] if operator else 'Unknown'
        if 'vas_id' in user:
            vas = mongo.db.vas_table.find_one({'ID': user['vas_id']})
            user['vas_name'] = vas['VASP'] if vas else 'Unknown'

    return render_template('user_list.html', users=users)

@e164_bp.route('/admin/users/create', methods=['GET', 'POST'])
@login_required
def create_user():
    """Create a new user (admin only)"""
    if not current_user.is_admin:
        flash('You do not have permission to access this page.', 'danger')
        return redirect(url_for('e164.dashboard'))

    form = UserCreationForm()

    # Populate operator choices
    operators = list(mongo.db.operators.find({'active': True}))
    form.operator_id.choices = [('', '-- Select Operator --')] + [
        (str(op['_id']), op['name']) for op in operators
    ]

    if form.validate_on_submit():
        # Check if email already exists
        if mongo.db.users.find_one({'email': form.email.data}):
            flash('Email already registered', 'danger')
            return render_template('user_create.html', form=form)

        # Create user document
        user_data = {
            'contact': form.username.data,
            'email': form.email.data,
            'password': generate_password_hash(form.password.data),
            'created_at': datetime.now(timezone.utc)
        }

        # Set appropriate fields based on user type
        if form.user_type.data == 'admin':
            user_data['is_admin'] = True
        elif form.user_type.data == 'operator' and form.operator_id.data:
            user_data['operator_id'] = form.operator_id.data
        elif form.user_type.data == 'vas' and form.vas_id.data:
            user_data['vas_id'] = form.vas_id.data

        # Insert user
        result = mongo.db.users.insert_one(user_data)

        if result.inserted_id:
            log_activity("User Creation", f"Admin {current_user.contact} created new user: {form.username.data}")
            flash('User created successfully', 'success')
            return redirect(url_for('e164.list_users'))
        else:
            flash('Failed to create user', 'danger')

    return render_template('user_create.html', form=form)











@e164_bp.route('/submission/new', methods=['GET'])
@login_required
def new_submission():
    """Render the submission form with empty state"""
    # Check if the user is associated with an operator
    operator = None
    if not current_user.is_admin:
        operator = mongo.db.operators.find_one({'_id': ObjectId(current_user.operator_id)})
        if not operator:
            flash('You are not associated with any operator.', 'danger')
            return redirect(url_for('dashboard'))

    # Get NDCs for the operator
    ndcs = []
    if operator:
        ndcs = fetch_operator_ndcs(str(operator['_id']))

    # Get reporting periods
    current_year = datetime.now().year
    reporting_periods = [
        f"{current_year}-h1",  # First half of current year
        f"{current_year}-h2",  # Second half of current year
        f"{current_year-1}-h1",  # First half of previous year
        f"{current_year-1}-h2"   # Second half of previous year
    ]

    # Set the active tab to submission form
    active_tab = 'submission'

    return render_template(
        'e164/submission_form.html',
        operator=operator,
        ndcs=ndcs,
        reporting_periods=reporting_periods,
        active_tab=active_tab,
        page_title="New E.164 Number Range Submission"
    )

# Update the create_submission route in routes.py to handle porting data

@e164_bp.route('/submission/create', methods=['POST'])
@login_required
def create_submission():
    """Create a new E.164 submission with optional porting data"""
    # Parse request data
    data = request.json

    # Validate required fields
    if not data.get('operator_id') or not data.get('reporting_period'):
        return jsonify({'success': False, 'error': 'Missing required fields'}), 400

    # Create submission document
    submission = {
        'operator_id': data.get('operator_id'),
        'operator': data.get('operator'),
        'reporting_period': data.get('reporting_period'),
        'contact_person': data.get('contact_person'),
        'contact_email': data.get('contact_email'),
        'notes': data.get('notes'),
        'status': 'pending',  # Initial status is pending
        'created_at': datetime.now(timezone.utc),
        'updated_at': datetime.now(timezone.utc),
        'created_by': current_user.id,
        'updated_by': current_user.id
    }

    # Insert submission
    submission_result = mongo.db.e164_submissions.insert_one(submission)
    submission_id = str(submission_result.inserted_id)

    # Process ranges
    ranges = data.get('ranges', [])
    for range_data in ranges:
        # Calculate derived metrics
        metrics = calculate_number_metrics(range_data)

        # Create range document
        range_doc = {
            'submission_id': submission_id,
            'ndc': range_data.get('ndc'),
            'type': range_data.get('type'),
            'start_block': range_data.get('start_block'),
            'end_block': range_data.get('end_block'),
            'total_allocated': range_data.get('total_allocated'),
            'active_subscriber': range_data.get('active_subscriber'),
            'active_non_subscriber': range_data.get('active_non_subscriber', 0),
            'inactive': range_data.get('inactive'),
            'reserved': range_data.get('reserved', 0),
            'ported_in': range_data.get('ported_in', 0),
            'ported_out': range_data.get('ported_out', 0),
            'gross_addition': range_data.get('gross_addition', 0),
            'net_addition': range_data.get('net_addition', 0),
            'utilization_rate': metrics['utilization_rate'],
            'dormancy_rate': metrics['dormancy_rate'],
            'reservation_rate': metrics['reservation_rate'],
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }

        # Insert range
        mongo.db.e164_ranges.insert_one(range_doc)

    # Process porting data if provided
    porting_data = data.get('porting_data', [])
    for porting_record in porting_data:
        # Create porting detail document
        porting_doc = {
            'submission_id': submission_id,
            'range_type': porting_record.get('range_type'),
            'source_operator': porting_record.get('source_operator'),
            'target_operator': porting_record.get('target_operator'),
            'count': porting_record.get('count'),
            'ndc': porting_record.get('ndc'),
            'reporting_period': data.get('reporting_period'),
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc)
        }

        # Insert porting record
        mongo.db.e164_porting_details.insert_one(porting_doc)

    # --- Notify Admins about the new pending submission ---
    admin_users_cursor = mongo.db.users.find({'is_admin': True}, {'contact': 1}) # Fetch only 'contact' field
    admin_contacts_notified = set() # To avoid duplicate notifications if multiple admins have same contact


    for admin_user_doc in admin_users_cursor:
        admin_recipient_contact = admin_user_doc.get('contact')
        if admin_recipient_contact and admin_recipient_contact not in admin_contacts_notified:
            try:
                add_notification(
                    message=f"New E.164 submission ({submission_id}) for {submission.get('operator')} ({submission.get('reporting_period')}) by {current_user.contact} requires review.",
                    recipient=admin_recipient_contact
                )
                admin_contacts_notified.add(admin_recipient_contact)
                current_app.logger.info(f"Sent new E.164 submission notification to admin: {admin_recipient_contact}")
            except Exception as e:
                current_app.logger.error(f"Failed to send notification to admin {admin_recipient_contact}: {e}")
    
    if not admin_contacts_notified:
        current_app.logger.warning("No admin users found or could be notified for the new E.164 submission.")
    # --- End Admin Notification ---





    # Log activity details including porting data
    activity_details = f"User {current_user.contact} created E.164 submission for {data.get('operator')} ({data.get('reporting_period')})" #
    if porting_data:
        activity_details += f" including {len(porting_data)} porting records" #

    log_activity("E.164 Submission Created", activity_details) #

    return jsonify({
        'success': True,
        'submission_id': submission_id,
        'redirect_url': url_for('e164.view_submission', submission_id=submission_id)
    })



# routes.py





@e164_bp.route('/submission/view/<submission_id>', methods=['GET']) # Use standard Flask routing
@login_required
def operator_view_submission(submission_id):
    """View the details of a specific E.164 submission."""
    try:
        obj_id = ObjectId(submission_id)
    except Exception:
        flash('Invalid Submission ID format.', 'danger')
        return redirect(url_for('e164.dashboard')) # Redirect to dashboard or history

    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': obj_id})
    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard')) # Redirect to dashboard or history

    # --- Authorization Check ---
    is_admin_user = getattr(current_user, 'is_admin', False)
    operator_id_str = str(getattr(current_user, 'operator_id', None))
    submission_operator_id_str = str(submission.get('operator_id'))

    # try:

    if not is_admin_user and operator_id_str != submission_operator_id_str:
        flash('You are not authorized to view this submission.', 'danger')
        return redirect(url_for('e164.dashboard')) # Redirect operator to their dashboard

    # --- Fetch Associated Data ---
    # Fetch ALL ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))
    # Fetch ALL porting details for this submission
    porting_details = list(mongo.db.e164_porting_details.find({'submission_id': submission_id}))

    # Convert ObjectIds and datetimes for display in template (using serialize_to_json helper)
    submission_processed = serialize_to_json(submission)
    ranges_processed = [serialize_to_json(r) for r in ranges]
    porting_details_processed = [serialize_to_json(p) for p in porting_details]

    # Calculate summary metrics for display (optional, but good for context)
    summary_metrics = {}
    if ranges_processed:
        total_allocated = sum(r.get('total_allocated', 0) for r in ranges_processed)
        total_active_sub = sum(r.get('active_subscriber', 0) for r in ranges_processed)
        total_active_nonsub = sum(r.get('active_non_subscriber', 0) for r in ranges_processed)
        total_active = total_active_sub + total_active_nonsub
        total_inactive = sum(r.get('inactive', 0) for r in ranges_processed)
        total_reserved = sum(r.get('reserved', 0) for r in ranges_processed)
        utilization = round((total_active / total_allocated * 100), 2) if total_allocated > 0 else 0
        summary_metrics = {
            'total_allocated': total_allocated,
            'total_active': total_active,
            'total_inactive': total_inactive,
            'total_reserved': total_reserved,
            'utilization_rate': utilization
        }


    # --- Render the NEW Detail Template ---
    return render_template(
        'e164/view_submission_detail.html', # *** RENDER THE NEW TEMPLATE ***
        submission=submission_processed,
        ranges=ranges_processed,
        porting_details=porting_details_processed,
        summary_metrics=summary_metrics, # Pass calculated summary
        is_admin=is_admin_user, # Pass admin status if needed by template
        page_title=f"Submission Details - {submission_processed.get('reporting_period')}"
    )


    # except Exception as e:
    #     current_app.logger.error(f"Error viewing submission {submission_id}: {e}", exc_info=True)
    #     flash(f"An error occurred while viewing the submission details.", "danger")
    #     return redirect(url_for('e164.dashboard'))






@e164_bp.route('/submission/<submission_id>', methods=['GET'])
@login_required
def view_submission(submission_id):
    """View a specific E.164 submission"""
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard'))

    # Check if the user is authorized to view this submission
    if not current_user.is_admin and str(submission.get('operator_id')) =="":
        flash('You are not authorized to view this submission.', 'danger')
        return redirect(url_for('e164.dashboard'))

    # Get the ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))

    # Prepare submission for template
    submission['_id'] = str(submission['_id'])
    submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    submission['updated_at'] = submission['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

    # Aggregate metrics
    metrics = {
        'total_allocated': sum(r.get('total_allocated', 0) for r in ranges),
        'total_active_subscriber': sum(r.get('active_subscriber', 0) for r in ranges),
        'total_active_non_subscriber': sum(r.get('active_non_subscriber', 0) for r in ranges),
        'total_inactive': sum(r.get('inactive', 0) for r in ranges),
        'total_reserved': sum(r.get('reserved', 0) for r in ranges),
        'total_ported_in': sum(r.get('ported_in', 0) for r in ranges),
        'total_ported_out': sum(r.get('ported_out', 0) for r in ranges)
    }

    # Calculate overall metrics
    total_active = metrics['total_active_subscriber'] + metrics['total_active_non_subscriber']
    metrics['total_active'] = total_active
    metrics['overall_utilization'] = round((total_active / metrics['total_allocated'] * 100), 2) if metrics['total_allocated'] > 0 else 0

    # Get operator information
    operator = None
    if not current_user.is_admin:
        operator = mongo.db.operators.find_one({'_id': ObjectId(current_user.operator_id)})

    # Set active tab to history (where we can see submission details)
    active_tab = 'history'

    return render_template(
        'e164/submission_form.html',  # Fix the template path
        submission=submission,
        ranges=ranges,
        metrics=metrics,
        active_tab=active_tab,
        view_mode='detail',
        operator=operator,
        reporting_periods=[],  # Add missing expected variables
        ndcs=[],               # Add missing expected variables
        operator_comparison=None,  # Add missing expected variables
        chart_data={},  # Initialize empty chart data
        page_title=f"Submission Details - {submission.get('reporting_period')}",
        is_admin=current_user.is_admin  # Add is_admin flag
    )




@e164_bp.route('/submissions/pending', methods=['GET'])
@login_required
@admin_required
def review_pending_submissions():
    """Show all pending submissions for admin review"""
    # Fetch all pending submissions
    pending_submissions = list(mongo.db.e164_submissions.find(
        {'status': 'pending'}
    ).sort('created_at', -1))

    # Process submissions for template
    for submission in pending_submissions:
        submission['_id'] = str(submission['_id'])
        submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')

        # Calculate basic metrics for each submission
        ranges = list(mongo.db.e164_ranges.find({'submission_id': str(submission['_id'])}))
        submission['ranges_count'] = len(ranges)
        submission['total_allocated'] = sum(r.get('total_allocated', 0) for r in ranges)
        submission['total_active'] = sum(r.get('active_subscriber', 0) + r.get('active_non_subscriber', 0) for r in ranges)
        if submission['total_allocated'] > 0:
            submission['utilization_rate'] = round((submission['total_active'] / submission['total_allocated'] * 100), 2)
        else:
            submission['utilization_rate'] = 0

    log_activity("Admin Review", f"User {current_user.contact} viewed pending submissions")

    return render_template(
        'e164/review_pending.html',  # You'll need to create this template
        pending_submissions=pending_submissions,
        page_title="Pending E.164 Submissions"
    )

@e164_bp.route('/submission/<submission_id>/review', methods=['GET'])
@login_required
@admin_required
def review_submission(submission_id):
    """Display a submission for review with approval/rejection options"""
    # Convert string ID to ObjectId if needed
    if not isinstance(submission_id, ObjectId):
        try:
            obj_id = ObjectId(submission_id)
        except:
            obj_id = submission_id
    else:
        obj_id = submission_id

    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': obj_id})
    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard'))

    # Get the ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))

    # Calculate aggregated metrics
    total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
    total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
    total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
    total_active = total_active_subscriber + total_active_non_subscriber
    total_inactive = sum(r.get('inactive', 0) for r in ranges)
    total_reserved = sum(r.get('reserved', 0) for r in ranges)

    utilization_rate = round((total_active / total_allocated * 100), 2) if total_allocated > 0 else 0

    # Prepare submission and ranges for template
    submission['_id'] = str(submission['_id'])
    submission['created_at'] = submission['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    submission['updated_at'] = submission['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

    log_activity("Submission Review", f"User {current_user.contact} reviewed submission {submission_id}")

    return render_template(
        'e164/review_submission.html',  # You'll need to create this template
        submission=submission,
        ranges=ranges,
        metrics={
            'total_allocated': total_allocated,
            'total_active': total_active,
            'total_inactive': total_inactive,
            'total_reserved': total_reserved,
            'utilization_rate': utilization_rate
        },
        page_title=f"Review Submission - {submission.get('operator')} ({submission.get('reporting_period')})"
    )


@e164_bp.route('/submission/<submission_id>/approve', methods=['POST'])
@login_required
@admin_required
def approve_submission(submission_id):
    """Approve an E.164 submission"""
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return jsonify({'success': False, 'error': 'Submission not found'}), 404

    # Update submission status
    mongo.db.e164_submissions.update_one(
        {'_id': ObjectId(submission_id)},
        {
            '$set': {
                'status': 'approved',
                'approved_at': datetime.now(timezone.utc),
                'approved_by': current_user.id,
                'updated_at': datetime.now(timezone.utc),
                'updated_by': current_user.id
            }
        }
    )

    # Also update status on all ranges in this submission
    mongo.db.e164_ranges.update_many(
        {'submission_id': submission_id},
        {
            '$set': {
                'status': 'approved',
                'updated_at': datetime.now(timezone.utc)
            }
        }
    )

    # Log the activity
    log_activity("E.164 Submission Approved", f"User {current_user.contact} approved E.164 submission {submission_id} for {submission.get('operator')}")

    # Send notification to operator
    add_notification(
        f"Your E.164 submission for {submission.get('reporting_period')} has been approved.",
        submission.get('operator_id')
    )

    return jsonify({
        'success': True,
        'redirect_url': url_for('e164.view_submission', submission_id=submission_id)
    })



@e164_bp.route('/submission/<submission_id>/reject', methods=['POST'])
@login_required
@admin_required
def reject_submission(submission_id):
    """Reject an E.164 submission with comments"""
    # Get request data
    data = request.json
    comments = data.get('comments', 'No comments provided')

    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        return jsonify({'success': False, 'error': 'Submission not found'}), 404

    # Update submission status
    mongo.db.e164_submissions.update_one(
        {'_id': ObjectId(submission_id)},
        {
            '$set': {
                'status': 'rejected',
                'rejection_comments': comments,
                'rejected_at': datetime.now(timezone.utc),
                'rejected_by': current_user.id,
                'updated_at': datetime.now(timezone.utc),
                'updated_by': current_user.id
            }
        }
    )

    # Also update status on all ranges in this submission
    mongo.db.e164_ranges.update_many(
        {'submission_id': submission_id},
        {
            '$set': {
                'status': 'rejected',
                'updated_at': datetime.now(timezone.utc)
            }
        }
    )

    # Log the activity
    log_activity("E.164 Submission Rejected", f"User {current_user.contact} rejected E.164 submission {submission_id} for {submission.get('operator')}")

    # Send notification to operator
    add_notification(
        f"Your E.164 submission for {submission.get('reporting_period')} has been rejected. Reason: {comments}",
        submission.get('operator_id')
    )

    return jsonify({
        'success': True,
        'redirect_url': url_for('e164.view_submission', submission_id=submission_id)
    })




@e164_bp.route('/analytics')
@login_required
def analytics():
    """E.164 Number Range Analytics - simplified to use submission_form.html"""

    print(f"Analytics route accessed by user {current_user.id} - {current_user.contact}")
    print(f"Is admin: {current_user.is_admin}")

    # For operators, show only their data
    operator_id = None
    if not current_user.is_admin:
        # Try different identifiers for operator matching
        operator_id = current_user.operator_id
        vas_id = getattr(current_user, 'vas_id', None)

        # Print debug information
        print(f"User details: operator_id={operator_id}, vas_id={vas_id}, id={current_user.id}")

    # Modify the query to check multiple possible ID fields
    if not current_user.is_admin:
        query = {'status': 'approved', '$or': []}

        if operator_id:
            query['$or'].append({'operator_id': operator_id})

        if hasattr(current_user, 'vas_id') and current_user.vas_id:
            query['$or'].append({'operator_id': current_user.vas_id})

        # Also try matching against user ID as fallback
        query['$or'].append({'created_by': current_user.id})

        print(f"Using query: {query}")

        # Get latest submission matching any of these criteria
        latest_submission = mongo.db.e164_submissions.find_one(
            query, sort=[('reporting_period', -1)]
        )

        if latest_submission:
            print(f"Found submission: {latest_submission['_id']}")
            latest_data = aggregate_submission_data(str(latest_submission['_id']))
            historical_data = get_historical_data(latest_submission.get('operator_id'))
        else:
            print("No matching submission found")
            latest_data = None
            historical_data = []
    else:
        # For admins, get data across all operators
        operator_data = compare_operators_utilization()

        # Get overall metrics
        total_allocated = sum(op.get('total_allocated', 0) for op in operator_data)
        total_active = sum(op.get('total_active', 0) for op in operator_data)

        latest_data = {
            'metrics': {
                'total_allocated': total_allocated,
                'total_active': total_active,
                'utilization_rate': round((total_active / total_allocated * 100), 2) if total_allocated > 0 else 0
            }
        }

        # Get historical trends - we'll use the operator comparison data for admins
        historical_data = operator_data

    # Get operator information
    operator = None
    if not current_user.is_admin:
        operator = mongo.db.operators.find_one({'_id': ObjectId(current_user.operator_id)})

    # Process data for charts
    chart_data = {
        'labels': [],
        'utilization': [],
        'dormancy': [],
        'reservation': []
    }

    for item in historical_data:
        if 'period' in item:
            chart_data['labels'].append(item['period'])
            chart_data['utilization'].append(item.get('utilization_rate', 0))
            chart_data['dormancy'].append(100 - item.get('utilization_rate', 0))

    # Set active tab to analytics
    active_tab = 'analytics'

# Replace the porting data section in the analytics route with this code

# Fetch porting activity data for Sankey diagram
    porting_data = []
    try:
        # Get all active operators first - we need this for both admin and operator views
        all_operators = list(mongo.db.operators.find({'active': True}))
        operator_names = [op.get('name') for op in all_operators if op.get('name')]

        # We'll store the operator IDs for reference
        operator_ids = {str(op.get('_id')): op.get('name') for op in all_operators if op.get('name')}

        if current_user.is_admin:
            # ADMIN VIEW: Get data across all operators
            # For admins, we need to get porting data between all operators

            # Find latest approved submission from each operator
            latest_submissions_pipeline = [
                {"$match": {"status": "approved"}},
                {"$sort": {"reporting_period": -1}},
                {"$group": {
                    "_id": "$operator_id",
                    "submission_id": {"$first": "$_id"},
                    "operator": {"$first": "$operator"},
                    "reporting_period": {"$first": "$reporting_period"},
                    "created_at": {"$first": "$created_at"}
                }}
            ]

            latest_submissions = list(mongo.db.e164_submissions.aggregate(latest_submissions_pipeline))
            print(f"Found {len(latest_submissions)} latest submissions for each operator")

            # Get all wireless ranges from these submissions
            all_ranges = []
            submission_ids = [str(sub.get('submission_id')) for sub in latest_submissions]

            if submission_ids:
                all_ranges = list(mongo.db.e164_ranges.find({
                    'submission_id': {'$in': submission_ids},
                    'type': {'$regex': 'WIRELESS', '$options': 'i'}
                }))

            print(f"Found {len(all_ranges)} wireless ranges from latest submissions")

            # Process porting data between operators
            # First, check if we have detailed porting records
            porting_details = list(mongo.db.e164_porting_details.find({
                'submission_id': {'$in': submission_ids},
                'range_type': {'$regex': 'WIRELESS', '$options': 'i'}
            }))

            # Create sankey nodes from operator names
            sankey_nodes = [{'name': op} for op in operator_names]
            sankey_links = []

            if porting_details:
                print(f"Found {len(porting_details)} detailed porting records")
                # We have detailed porting records, process them
                porting_map = {}  # Use a map to aggregate values between the same operators

                for detail in porting_details:
                    source_operator = detail.get('source_operator')
                    target_operator = detail.get('target_operator')
                    value = detail.get('count', 0)

                    # Skip if operators aren't in our list
                    if source_operator not in operator_names or target_operator not in operator_names:
                        continue

                    # Create a unique key for this source-target pair
                    key = f"{source_operator}|{target_operator}"

                    # Add to our map, aggregating values
                    if key in porting_map:
                        porting_map[key] += value
                    else:
                        porting_map[key] = value

                # Convert the map to sankey links
                for key, value in porting_map.items():
                    source, target = key.split('|')
                    source_idx = operator_names.index(source)
                    target_idx = operator_names.index(target)

                    sankey_links.append({
                        'source': source_idx,
                        'target': target_idx,
                        'value': value
                    })
            else:
                print("No detailed porting records found, calculating from range data")
                # No detailed records, create estimates from range data
                operator_porting = {}  # Store porting data by operator

                # First, aggregate ported_in and ported_out by operator
                for submission in latest_submissions:
                    operator_id = submission.get('_id')
                    operator = submission.get('operator')

                    if not operator or operator not in operator_names:
                        continue

                    # Find wireless ranges for this submission
                    ranges = [r for r in all_ranges if r.get('submission_id') == str(submission.get('submission_id'))]

                    # Calculate totals
                    ported_in = sum(r.get('ported_in', 0) for r in ranges)
                    ported_out = sum(r.get('ported_out', 0) for r in ranges)

                    operator_porting[operator] = {
                        'ported_in': ported_in,
                        'ported_out': ported_out
                    }

                # Now distribute porting between operators based on market share
                # This is an estimate but provides realistic visualization
                for source_op, data in operator_porting.items():
                    ported_out = data.get('ported_out', 0)

                    # Skip if no porting out
                    if ported_out <= 0:
                        continue

                    source_idx = operator_names.index(source_op)
                    other_operators = [op for op in operator_names if op != source_op]

                    if not other_operators:
                        continue

                    # Calculate a weight for each target based on their ported_in total
                    total_ported_in = sum(operator_porting.get(op, {}).get('ported_in', 1) for op in other_operators)

                    for target_op in other_operators:
                        target_idx = operator_names.index(target_op)
                        target_weight = operator_porting.get(target_op, {}).get('ported_in', 1) / max(1, total_ported_in)
                        porting_value = ported_out * target_weight

                        # Only add links with significant values
                        if porting_value >= 1:
                            sankey_links.append({
                                'source': source_idx,
                                'target': target_idx,
                                'value': porting_value
                            })

            # Add the Sankey data to our response for admin view
            if sankey_nodes and sankey_links:
                porting_data.append({
                    'operator': 'All Operators',
                    'period': 'Latest Reporting Period',
                    'total_ported': sum(link.get('value', 0) for link in sankey_links),
                    'sankey_data': {
                        'nodes': sankey_nodes,
                        'links': sankey_links
                    }
                })
        else:
            # OPERATOR VIEW: Get data for the current operator
            # For operators, we focus on their porting activity
            operator_id = getattr(current_user, 'operator_id', None)

            # Get the operator name if possible
            current_operator_name = None
            if operator_id in operator_ids:
                current_operator_name = operator_ids[operator_id]
            else:
                # Try to find it another way
                op = mongo.db.operators.find_one({'_id': ObjectId(operator_id)})
                if op:
                    current_operator_name = op.get('name')

            # Find the latest submission for this operator
            query = {'status': 'approved'}
            if operator_id:
                query['operator_id'] = operator_id

            most_recent_submission = mongo.db.e164_submissions.find_one(
                query,
                sort=[('reporting_period', -1)]
            )

            if most_recent_submission and current_operator_name in operator_names:
                submission_id = str(most_recent_submission['_id'])

                # Get wireless ranges for this submission
                wireless_ranges = list(mongo.db.e164_ranges.find({
                    'submission_id': submission_id,
                    'type': {'$regex': 'WIRELESS', '$options': 'i'}
                }))

                if wireless_ranges:
                    current_operator_idx = operator_names.index(current_operator_name)
                    sankey_nodes = [{'name': op} for op in operator_names]
                    sankey_links = []

                    # Get porting details if available
                    porting_details = list(mongo.db.e164_porting_details.find({
                        'submission_id': submission_id,
                        'range_type': {'$regex': 'WIRELESS', '$options': 'i'}
                    }))

                    if porting_details:
                        # Process detailed porting records
                        for detail in porting_details:
                            source_operator = detail.get('source_operator')
                            target_operator = detail.get('target_operator')
                            value = detail.get('count', 0)

                            # Only include records involving this operator
                            if source_operator == current_operator_name or target_operator == current_operator_name:
                                # Ensure both operators are in our list
                                if source_operator in operator_names and target_operator in operator_names:
                                    source_idx = operator_names.index(source_operator)
                                    target_idx = operator_names.index(target_operator)

                                    sankey_links.append({
                                        'source': source_idx,
                                        'target': target_idx,
                                        'value': value
                                    })
                    else:
                        # No detailed records, distribute based on totals
                        total_ported_in = sum(r.get('ported_in', 0) for r in wireless_ranges)
                        total_ported_out = sum(r.get('ported_out', 0) for r in wireless_ranges)

                        other_operators = [op for op in operator_names if op != current_operator_name]

                        if other_operators:
                            # Distribute ported in (from other operators to current)
                            if total_ported_in > 0:
                                # Calculate weighted distribution based on operator index as a proxy for size
                                total_weight = sum(1/(idx+1) for idx in range(len(other_operators)))

                                for idx, other_op in enumerate(other_operators):
                                    other_idx = operator_names.index(other_op)
                                    # Weight more porting to/from larger operators (lower index)
                                    weight = (1/(idx+1)) / total_weight
                                    porting_value = total_ported_in * weight

                                    sankey_links.append({
                                        'source': other_idx,
                                        'target': current_operator_idx,
                                        'value': porting_value
                                    })

                            # Distribute ported out (from current to other operators)
                            if total_ported_out > 0:
                                # Calculate weighted distribution based on operator index as a proxy for size
                                total_weight = sum(1/(idx+1) for idx in range(len(other_operators)))

                                for idx, other_op in enumerate(other_operators):
                                    other_idx = operator_names.index(other_op)
                                    # Weight more porting to/from larger operators (lower index)
                                    weight = (1/(idx+1)) / total_weight
                                    porting_value = total_ported_out * weight

                                    sankey_links.append({
                                        'source': current_operator_idx,
                                        'target': other_idx,
                                        'value': porting_value
                                    })

                    # Add the Sankey data to our response
                    porting_data.append({
                        'operator': current_operator_name,
                        'period': most_recent_submission.get('reporting_period'),
                        'ported_in': sum(r.get('ported_in', 0) for r in wireless_ranges),
                        'ported_out': sum(r.get('ported_out', 0) for r in wireless_ranges),
                        'net_position': sum(r.get('ported_in', 0) - r.get('ported_out', 0) for r in wireless_ranges),
                        'sankey_data': {
                            'nodes': sankey_nodes,
                            'links': sankey_links
                        }
                    })

        # Log success
        print(f"Generated porting data with {len(porting_data)} entries")
        if porting_data:
            print(f"Sankey data has {len(porting_data[0].get('sankey_data', {}).get('nodes', []))} nodes and {len(porting_data[0].get('sankey_data', {}).get('links', []))} links")
    except Exception as e:
        print(f"Error generating porting data: {e}")
        # Will fall back to empty list

    # If we still have no porting data, create meaningful placeholder data
    if not porting_data:
        import random

        # We still need to provide visualization data
        try:
            # If we don't have operator names yet, get them
            if not operator_names:
                all_operators = list(mongo.db.operators.find({'active': True}))
                operator_names = [op.get('name') for op in all_operators if op.get('name')]

            if operator_names:
                # Create Sankey nodes from operator names
                sankey_nodes = [{'name': op} for op in operator_names]
                sankey_links = []

                # Create realistic looking links
                total_links = 0
                for i in range(len(operator_names)):
                    for j in range(len(operator_names)):
                        if i != j:  # Don't port to self
                            # Larger operators (lower index) have more porting activity
                            base_value = 100 * (1 - (i / len(operator_names))) * (1 - (j / len(operator_names)))
                            # Add some randomness but ensure reasonable values
                            value = max(10, int(base_value * (0.8 + 0.4 * random.random())))

                            sankey_links.append({
                                'source': i,
                                'target': j,
                                'value': value
                            })
                            total_links += value

                # Add to porting data - admin or operator view
                view_type = "All Operators" if current_user.is_admin else "Operator View"
                porting_data = [{
                    'operator': view_type,
                    'period': 'Latest Available Period',
                    'ported_in': total_links // 2,  # Approximate for display
                    'ported_out': total_links // 2,
                    'net_position': 0,
                    'sankey_data': {
                        'nodes': sankey_nodes,
                        'links': sankey_links
                    }
                }]

                print(f"Created placeholder Sankey data with {len(sankey_nodes)} nodes and {len(sankey_links)} links")
        except Exception as e:
            print(f"Error creating placeholder porting data: {e}")

    # Add porting data to chart_data
    if porting_data and 'sankey_data' in porting_data[0]:
        chart_data['porting_sankey'] = porting_data[0]['sankey_data']
        print(f"Added porting_sankey data to chart_data with {len(chart_data['porting_sankey']['nodes'])} nodes")


    print(f"Rendering template with: latest_data={latest_data is not None}, "
          f"historical_data_count={len(historical_data)}, "
          f"chart_data_labels_count={len(chart_data['labels'])}")


    return render_template(
        'e164/submission_form.html',
        latest_data=latest_data,
        historical_data=historical_data,
        chart_data=chart_data,
        is_admin=current_user.is_admin,
        active_tab=active_tab,
        operator=operator,
        page_title="E.164 Number Range Analytics"
    )




@e164_bp.route('/api/operator/<operator_id>/ndcs')
@login_required
def get_operator_ndcs_api(operator_id):
    """API to get NDCs for a specific operator"""
    ndcs = fetch_operator_ndcs(operator_id)
    return jsonify(ndcs)




@e164_bp.route('/api/submissions/history')
@login_required
def get_submission_history():
    """
    API endpoint to retrieve submission history data for the history table.
    Handles admin (all submissions) and operator (own submissions) views.
    URL: /e164/api/submissions/history
    """
    # ... (rest of history route code remains the same) ...
    current_app.logger.info(f"API: Submission history called by user: {current_user.email}")
    submissions_list = []
    query = {} # Base query

    try:
        # Determine query based on user role
        if not current_user.is_admin:
            operator_id = getattr(current_user, 'operator_id', None)
            if not operator_id:
                current_app.logger.warning(f"API: Operator user {current_user.email} has no operator_id associated for history.")
                # Return empty list if user has no operator ID
                return jsonify({'success': True, 'submissions': []})
            query['operator_id'] = str(operator_id)
            current_app.logger.info(f"API: Fetching submission history for operator_id: {operator_id}")
        else:
            current_app.logger.info("API: Fetching all submission history for admin.")

        # Fetch submissions from DB, limit fields and sort
        # Adjust fields as needed by the history table in e164-audit.js
        projection = {
            '_id': 1,
            'operator': 1, # Operator Name
            'reporting_period': 1,
            'created_at': 1, # Or submitted_at if that's more relevant
            'status': 1,
            'summary.total_allocated': 1, # Example: Get summary data
            'summary.utilization_rate': 1 # Example: Get summary data
        }
        history_cursor = mongo.db.e164_submissions.find(query, projection).sort(
            [('reporting_period', -1), ('created_at', -1)]
        ).limit(50) # Add a reasonable limit for performance

        # Process results and convert ObjectIds
        for sub in history_cursor:
            processed_sub = {
                'id': str(sub['_id']), # Convert ObjectId to string
                'operator_name': sub.get('operator'),
                'reporting_period': sub.get('reporting_period'),
                'submitted_at': sub.get('created_at'), # Or use submitted_at field if available
                'status': sub.get('status'),
                'total_allocated': sub.get('summary', {}).get('total_allocated'),
                'utilization_rate': sub.get('summary', {}).get('utilization_rate')
            }
            # Convert datetime objects to ISO format strings for JSON serialization
            if isinstance(processed_sub['submitted_at'], datetime):
                 processed_sub['submitted_at'] = processed_sub['submitted_at'].isoformat()

            submissions_list.append(processed_sub)

        current_app.logger.info(f"API: Found {len(submissions_list)} submission history records.")

        return jsonify({
            'success': True,
            'submissions': submissions_list
        })

    except Exception as e:
        current_app.logger.error(f"API: Error fetching submission history: {e}", exc_info=True)
        return jsonify({'success': False, 'error': f'An internal server error occurred while fetching history.'}), 500




@e164_bp.route('/api/analytics/operator-comparison')
@login_required
@admin_required
def get_operator_comparison():
    """API to get comparison data for all operators"""
    comparison = compare_operators_utilization()
    return jsonify(comparison)


def calculate_market_share_for_period(period):
    """
    Calculate market share data for a specific reporting period.

    Args:
        period (str): The reporting period to calculate market share for (e.g., '2023-Q1')

    Returns:
        dict: Market share data with structure:
            {
                'period': str,
                'total_allocation': int,
                'operator_shares': [
                    {'operator_id': str, 'operator_name': str, 'allocated': int, 'percentage': float},
                    ...
                ]
            }
        or None if no data is available
    """
    current_app.logger.info(f"Calculating market share for period: {period}")

    # Validate period parameter
    if not period:
        current_app.logger.warning("Cannot calculate market share: No period specified")
        return None

    # Get all approved submissions for this specific period
    query = {'status': 'approved', 'reporting_period': period}
    submissions = list(mongo.db.e164_submissions.find(query))

    if not submissions:
        current_app.logger.warning(f"No approved submissions found for period '{period}'")
        return None

    # Calculate market share
    operator_shares = []
    total_market_allocation = 0

    for submission in submissions:
        op_id = str(submission.get('operator_id', ''))

        # Get operator name
        try:
            operator = mongo.db.operators.find_one({'_id': ObjectId(op_id)})
            op_name = operator.get('name') if operator else f"Operator {op_id[-4:] if len(op_id) >= 4 else op_id}"
        except:
            op_name = f"Operator {op_id[-4:] if len(op_id) >= 4 else op_id}"

        # Get allocation data - first check if summary exists
        allocated = 0
        if submission.get('summary') and 'total_allocated' in submission['summary']:
            # Use pre-calculated summary if available
            allocated = submission['summary'].get('total_allocated', 0)
        else:
            # Calculate from ranges if summary not available
            ranges = list(mongo.db.e164_ranges.find({'submission_id': str(submission['_id'])}))
            allocated = sum(r.get('total_allocated', 0) for r in ranges)

        if allocated > 0:  # Only include operators with allocations
            operator_shares.append({
                'operator_id': op_id,
                'operator_name': op_name,
                'allocated': allocated,
                'percentage': 0  # Will calculate after total is known
            })
            total_market_allocation += allocated

    # Calculate percentages
    if total_market_allocation > 0:
        for share in operator_shares:
            share['percentage'] = round((share['allocated'] / total_market_allocation * 100), 2)

    # Sort by allocation (descending)
    operator_shares.sort(key=lambda x: x['allocated'], reverse=True)

    result = {
        'period': period,
        'total_allocation': total_market_allocation,
        'operator_shares': operator_shares
    }

    current_app.logger.info(f"Market share calculation complete for period '{period}': {len(operator_shares)} operators, {total_market_allocation} total allocation")
    return result


def get_filtered_operator_comparison(period, operator_id=None):
    """
    Get comparison data for operators based on period and optional operator filter.

    Args:
        period (str): The reporting period to filter by, or None for 'all periods'
        operator_id (str, optional): Specific operator ID to filter by

    Returns:
        list: List of operator comparison data with structure:
            [
                {
                    'operator_id': str,
                    'operator_name': str,
                    'reporting_period': str,
                    'total_allocated': int,
                    'total_active': int,
                    'utilization_rate': float,
                    'dormancy_rate': float,
                    'reservation_rate': float
                },
                ...
            ]
    """
    current_app.logger.info(f"Getting operator comparison data for period: {period or 'all'}, operator_id: {operator_id or 'all'}")

    # Build base query
    query = {'status': 'approved'}
    if period:
        query['reporting_period'] = period
    if operator_id:
        query['operator_id'] = operator_id

    # Handle different scenarios based on filters
    if period and operator_id:
        # Specific period and operator - just get that one submission
        submissions = list(mongo.db.e164_submissions.find(query))
        if not submissions:
            current_app.logger.warning(f"No submissions found for period '{period}' and operator '{operator_id}'")
            return []

        # Process the single submission
        return [process_submission_for_comparison(submissions[0])]

    elif period and not operator_id:
        # Specific period, all operators - get one submission per operator for that period
        submissions = list(mongo.db.e164_submissions.find(query))
        if not submissions:
            current_app.logger.warning(f"No submissions found for period '{period}'")
            return []

        # Group by operator (in case there are multiple submissions per operator in this period)
        operator_submissions = {}
        for sub in submissions:
            op_id = str(sub.get('operator_id', ''))
            if op_id not in operator_submissions:
                operator_submissions[op_id] = sub

        # Process each operator's submission
        return [process_submission_for_comparison(sub) for sub in operator_submissions.values()]

    elif not period and operator_id:
        # All periods, specific operator - get latest submission for each period
        submissions = list(mongo.db.e164_submissions.find(
            query, sort=[('reporting_period', -1)]
        ))
        if not submissions:
            current_app.logger.warning(f"No submissions found for operator '{operator_id}'")
            return []

        # Group by period (get latest for each period)
        period_submissions = {}
        for sub in submissions:
            period = sub.get('reporting_period')
            if period and period not in period_submissions:
                period_submissions[period] = sub

        # Process each period's submission
        return [process_submission_for_comparison(sub) for sub in period_submissions.values()]

    else:  # not period and not operator_id
        # All periods, all operators - get latest submission for each operator
        pipeline = [
            {"$match": {"status": "approved"}},
            {"$sort": {"reporting_period": -1}},
            {"$group": {
                "_id": "$operator_id",
                "submission_id": {"$first": "$_id"},
                "operator": {"$first": "$operator"},
                "operator_id": {"$first": "$operator_id"},
                "reporting_period": {"$first": "$reporting_period"},
                "summary": {"$first": "$summary"}
            }}
        ]
        latest_submissions = list(mongo.db.e164_submissions.aggregate(pipeline))

        if not latest_submissions:
            current_app.logger.warning("No submissions found for comparison")
            return []

        # Process each operator's latest submission
        return [process_submission_for_comparison(sub) for sub in latest_submissions]


def process_submission_for_comparison(submission):
    """
    Process a submission document to extract comparison metrics.
    Helper function for get_filtered_operator_comparison().

    Args:
        submission (dict): The submission document from MongoDB

    Returns:
        dict: Processed metrics for comparison
    """
    op_id = str(submission.get('operator_id', ''))

    # Get operator name
    try:
        operator = mongo.db.operators.find_one({'_id': ObjectId(op_id)})
        op_name = operator.get('name') if operator else submission.get('operator', f"Operator {op_id[-4:] if len(op_id) >= 4 else op_id}")
    except:
        op_name = submission.get('operator', f"Operator {op_id[-4:] if len(op_id) >= 4 else op_id}")

    # Initialize metrics
    metrics = {
        'operator_id': op_id,
        'operator_name': op_name,
        'reporting_period': submission.get('reporting_period', 'Unknown'),
        'total_allocated': 0,
        'total_active': 0,
        'total_inactive': 0,
        'total_reserved': 0,
        'utilization_rate': 0,
        'dormancy_rate': 0,
        'reservation_rate': 0
    }

    # Check if summary exists and use it if available
    if submission.get('summary'):
        summary = submission['summary']
        metrics['total_allocated'] = summary.get('total_allocated', 0)
        metrics['total_active'] = summary.get('total_active', 0)
        metrics['total_inactive'] = summary.get('total_inactive', 0)
        metrics['total_reserved'] = summary.get('total_reserved', 0)
        metrics['utilization_rate'] = summary.get('utilization_rate', 0)
        metrics['dormancy_rate'] = summary.get('dormancy_rate', 0)
        metrics['reservation_rate'] = summary.get('reservation_rate', 0)
    else:
        # Calculate metrics from ranges if summary not available
        ranges = list(mongo.db.e164_ranges.find({'submission_id': str(submission['_id'])}))

        if ranges:
            total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
            total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
            total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
            total_inactive = sum(r.get('inactive', 0) for r in ranges)
            total_reserved = sum(r.get('reserved', 0) for r in ranges)

            total_active = total_active_subscriber + total_active_non_subscriber

            # Calculate rates
            if total_allocated > 0:
                utilization_rate = round((total_active / total_allocated * 100), 2)
                dormancy_rate = round((total_inactive / total_allocated * 100), 2)
                reservation_rate = round((total_reserved / total_allocated * 100), 2)
            else:
                utilization_rate = dormancy_rate = reservation_rate = 0

            # Update metrics
            metrics['total_allocated'] = total_allocated
            metrics['total_active'] = total_active
            metrics['total_inactive'] = total_inactive
            metrics['total_reserved'] = total_reserved
            metrics['utilization_rate'] = utilization_rate
            metrics['dormancy_rate'] = dormancy_rate
            metrics['reservation_rate'] = reservation_rate

    return metrics







@e164_bp.route('/api/analytics/filtered_data')
@login_required
@admin_required # Protect this endpoint for admins
def get_filtered_analytics_data():
    """
    API endpoint to retrieve filtered analytics data based on period and operator.
    Returns JSON with multiple analytics components based on filters.
    """
    current_app.logger.info(f"API: Filtered analytics data called by user: {current_user.email}")

    # Get query parameters with defaults
    period_param = request.args.get('period', 'latest')
    operator_id_param = request.args.get('operator_id', 'all')

    current_app.logger.info(f"API: Requested filters - period='{period_param}', operator_id='{operator_id_param}'")

    try:
        # Initialize response structure with defaults
        analytics_data = {
            'statusDistributionData': None,
            'marketShareResult': None,
            'operatorComparison': [],
            'sankeyData': {'nodes': [], 'links': []}, # Default empty Sankey structure
            'aggregateMetrics': None,
            'rangeDetails': []
        }

        # --- 1. Determine Effective Period Filter ---
        filtered_period = None
        if period_param == 'latest':
            latest_sub = mongo.db.e164_submissions.find_one({'status': 'approved'}, sort=[('reporting_period', -1)])
            if latest_sub:
                filtered_period = latest_sub.get('reporting_period')
                current_app.logger.info(f"API: Determined 'latest' period as: {filtered_period}")
            else:
                current_app.logger.warning("API: No approved submissions found. Cannot determine latest period.")
                return jsonify({'success': True, 'analyticsData': analytics_data}) # Return default empty data
        elif period_param != 'all':
            filtered_period = period_param # Use the specific period provided
            current_app.logger.info(f"API: Using specific period: {filtered_period}")
        # If period_param == 'all', filtered_period remains None, indicating no period filter for some queries

        # --- 2. Determine Effective Operator Filter ---
        filtered_operator_id = operator_id_param if operator_id_param != 'all' else None
        if filtered_operator_id:
            current_app.logger.info(f"API: Filtering by operator_id: {filtered_operator_id}")
        else:
            current_app.logger.info("API: Aggregating for all operators.")

        # --- 3. Determine Relevant Submission IDs ---
        # Base query for approved submissions
        submission_query = {'status': 'approved'}
        # Apply period filter if a specific or latest period was determined
        if filtered_period:
            submission_query['reporting_period'] = filtered_period
        # Apply operator filter if a specific operator was requested
        if filtered_operator_id:
            submission_query['operator_id'] = filtered_operator_id

        # Fetch the IDs of submissions matching the criteria
        relevant_submissions = list(mongo.db.e164_submissions.find(submission_query, {'_id': 1}))
        submission_ids = [str(sub['_id']) for sub in relevant_submissions]

        if not submission_ids:
            current_app.logger.warning(f"API: No submission IDs found matching query: {submission_query}")
            return jsonify({'success': True, 'analyticsData': analytics_data}) # Return default empty data

        current_app.logger.info(f"API: Found {len(submission_ids)} relevant submission IDs.")

        # --- 4. Fetch All Necessary Data Using Submission IDs ---

        # Fetch Operator Map Once
        operators_cursor = mongo.db.operators.find({}, {'_id': 1, 'name': 1})
        operator_name_map = {str(op['_id']): op['name'] for op in operators_cursor}

        # Fetch All Relevant Ranges Once
        # Project only the fields needed for calculations and the range detail chart
        projection_ranges = {
            '_id': 0, 'submission_id': 1, 'ndc': 1, 'type': 1, 'total_allocated': 1,
            'active_subscriber': 1, 'active_non_subscriber': 1, 'inactive': 1, 'reserved': 1
        }
        all_ranges = list(mongo.db.e164_ranges.find(
            {'submission_id': {'$in': submission_ids}},
            projection_ranges
        ))
        current_app.logger.info(f"API: Fetched {len(all_ranges)} range documents.")

        # Fetch All Relevant Porting Details Once
        all_porting_details = list(mongo.db.e164_porting_details.find(
            {'submission_id': {'$in': submission_ids}},
            {'source_operator': 1, 'target_operator': 1, 'count': 1, '_id': 0} # Assuming operator IDs are stored
        ))

        print('all portig details', all_porting_details)
        current_app.logger.info(f"API: Fetched {len(all_porting_details)} porting detail records.")


        # --- 5. Calculate Aggregated Metrics & Status Distribution ---
        if all_ranges:
            total_allocated = sum(r.get('total_allocated', 0) for r in all_ranges)
            total_active_subscriber = sum(r.get('active_subscriber', 0) for r in all_ranges)
            total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in all_ranges)
            total_inactive = sum(r.get('inactive', 0) for r in all_ranges)
            total_reserved = sum(r.get('reserved', 0) for r in all_ranges)
            total_active = total_active_subscriber + total_active_non_subscriber

            analytics_data['statusDistributionData'] = {
                'total_active_subscriber': total_active_subscriber,
                'total_active_non_subscriber': total_active_non_subscriber,
                'total_inactive': total_inactive,
                'total_reserved': total_reserved
            }

            util_rate = round((total_active / total_allocated * 100), 1) if total_allocated > 0 else 0
            dorm_rate = round((total_inactive / total_allocated * 100), 1) if total_allocated > 0 else 0
            res_rate = round((total_reserved / total_allocated * 100), 1) if total_allocated > 0 else 0

            analytics_data['aggregateMetrics'] = {
                'utilization_rate': util_rate,
                'dormancy_rate': dorm_rate,
                'reservation_rate': res_rate,
                'growth_rate': None, # Growth/Trend calculation requires comparing periods - skip for now
                'utilization_change': None,
                'dormancy_change': None,
                'reservation_change': None
            }
            current_app.logger.info(f"API: Calculated aggregate metrics and status distribution.")
        else:
            current_app.logger.warning("API: No range data found to calculate aggregate metrics or status distribution.")


        # --- 6. Prepare Detailed Range Data ---
        # (Data already fetched in 'all_ranges')
        # Add utilization rate to each range dict if not already present
        processed_ranges = []
        for r in all_ranges:
             r_copy = r.copy() # Work on a copy
             active = r_copy.get('active_subscriber', 0) + r_copy.get('active_non_subscriber', 0)
             allocated = r_copy.get('total_allocated', 0)
             r_copy['utilization_rate'] = round((active / allocated * 100), 1) if allocated > 0 else 0
             processed_ranges.append(serialize_to_json(r_copy)) # Serialize if needed
        analytics_data['rangeDetails'] = processed_ranges


        # --- 7. Generate Sankey Data (Net Flow) ---
        if all_porting_details:

            sankey_nodes = set()
            net_link_counts = defaultdict(int)

            for detail in all_porting_details:
                source_id = detail.get('source_operator')
                target_id = detail.get('target_operator')
                count = detail.get('count', 0)

                source_name = operator_name_map.get(source_id, f"Op {source_id[-7:] if source_id else '??'}")
                target_name = operator_name_map.get(target_id, f"Op {target_id[-7:] if target_id else '??'}")

                # Only skip self-loops and zero/negative counts
                if source_name == target_name or count <= 0:
                    continue

                # Debug: Print what we're keeping
                print(f"Keeping link: {source_name} -> {target_name} with count {count}")

                sankey_nodes.add(source_name)
                sankey_nodes.add(target_name)

                op1, op2 = sorted((source_name, target_name))
                if source_name == op1: net_link_counts[(op1, op2)] += count
                else: net_link_counts[(op1, op2)] -= count

            nodes_list = sorted(list(sankey_nodes))
            node_indices = {node: i for i, node in enumerate(nodes_list)}
            final_nodes = [{'name': node} for node in nodes_list]
            final_links = []

            for (op1, op2), net_value in net_link_counts.items():
                if net_value == 0: continue
                source_node, target_node, link_value = (op1, op2, net_value) if net_value > 0 else (op2, op1, -net_value)
                if source_node in node_indices and target_node in node_indices:
                    final_links.append({
                        'source': node_indices[source_node],
                        'target': node_indices[target_node],
                        'value': link_value
                    })

            analytics_data['sankeyData'] = {'nodes': final_nodes, 'links': final_links}
            current_app.logger.info(f"API: Generated Sankey data with {len(final_nodes)} nodes, {len(final_links)} links.")
        else:
            current_app.logger.info("API: No porting details found for Sankey.")
            # analytics_data['sankeyData'] remains default {'nodes': [], 'links': []}


        # --- 8. Market Share & Operator Comparison (Call Helpers) ---
        # Use our new helper functions to get market share and operator comparison data
        if filtered_period: # Market share requires a specific period
             analytics_data['marketShareResult'] = calculate_market_share_for_period(filtered_period)
        else: # Default to latest if period was 'all'
             analytics_data['marketShareResult'] = calculate_market_share_data() # Assumes this gets latest

        # Pass determined period (None if 'all') and operator ID to our new helper function
        analytics_data['operatorComparison'] = get_filtered_operator_comparison(filtered_period, filtered_operator_id)

        # --- 9. Trend Chart Data (Multiple Metrics per Operator) ---
        # Get trend chart data with multiple metrics per operator
        if filtered_operator_id != 'all':
            # If a specific operator is selected, only include that operator's data
            # First get all data, then filter it to only include the selected operator
            all_trend_data = get_grouped_trend_data_by_metric(
                periods_limit=6,
                metrics=['utilization_rate', 'dormancy_rate', 'reservation_rate']
            )

            if all_trend_data and 'datasets' in all_trend_data:
                # Filter datasets to only include those for the selected operator
                operator_name = None
                try:
                    # Get operator name for filtering
                    operator = mongo.db.operators.find_one({'_id': ObjectId(filtered_operator_id)})
                    if operator:
                        operator_name = operator.get('name')
                except Exception as e:
                    current_app.logger.error(f"Error getting operator name: {e}")

                if operator_name:
                    # Filter datasets to only include those for this operator
                    filtered_datasets = [ds for ds in all_trend_data['datasets'] if operator_name in ds['label']]
                    all_trend_data['datasets'] = filtered_datasets

                analytics_data['trendChartData'] = all_trend_data
        else:
            # Include data for all operators
            analytics_data['trendChartData'] = get_grouped_trend_data_by_metric(
                periods_limit=6,
                metrics=['utilization_rate', 'dormancy_rate', 'reservation_rate']
            )


        # --- Return Combined Data ---
        return jsonify({
            'success': True,
            'analyticsData': analytics_data
        })

    except Exception as e:
        current_app.logger.error(f"API: Error fetching filtered analytics data: {e}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'An internal server error occurred: {str(e)}'
        }), 500


@e164_bp.route('/api/analytics/ndc-utilization')
@login_required
def get_ndc_utilization():
    """API to get utilization data by NDC"""
    # Get operator restriction if applicable
    operator_id = None
    if not current_user.is_admin:
        operator_id = current_user.operator_id

    # Construct the database query
    match_stage = {}
    if operator_id:
        # Get latest submission ID for this operator
        latest_submission = mongo.db.e164_submissions.find_one(
            {'operator_id': operator_id, 'status': 'approved'},
            sort=[('reporting_period', -1)]
        )

        if not latest_submission:
            return jsonify([])

        match_stage['submission_id'] = str(latest_submission['_id'])
    else:
        # For admin, get the latest submission for each operator
        # This requires a more complex aggregation
        latest_submissions = mongo.db.e164_submissions.aggregate([
            {"$match": {"status": "approved"}},
            {"$sort": {"reporting_period": -1}},
            {"$group": {
                "_id": "$operator_id",
                "submission_id": {"$first": "$_id"}
            }}
        ])

        submission_ids = [str(sub['submission_id']) for sub in latest_submissions]
        if not submission_ids:
            return jsonify([])

        match_stage['submission_id'] = {"$in": submission_ids}

    # Get utilization data for each NDC
    utilization_data = list(mongo.db.e164_ranges.find(
        match_stage,
        {
            'ndc': 1,
            'type': 1,
            'total_allocated': 1,
            'active_subscriber': 1,
            'active_non_subscriber': 1,
            'utilization_rate': 1
        }
    ))

    # Process data
    result = []
    for item in utilization_data:
        item['_id'] = str(item['_id'])
        result.append(item)

    return jsonify(result)


@e164_bp.route('/api/historical-trends')
@login_required
def get_historical_trends():
    """API to get historical utilization trends by operator and period"""
    # Get operator restriction if applicable
    operator_id = None
    if not current_user.is_admin:
        operator_id = current_user.operator_id


    # Define the maximum periods to return
    max_periods = 5

    # Get all reporting periods, sorted
    all_periods = list(mongo.db.e164_submissions.distinct('reporting_period',
                                                        {'status': 'approved'}))
    all_periods.sort(reverse=True)  # Most recent first

    # Take just the most recent periods
    periods = all_periods[:max_periods]

    # Build query to get approved submissions for these periods
    query = {'status': 'approved', 'reporting_period': {'$in': periods}}

    # Add operator filter if needed
    if operator_id:
        query['operator_id'] = operator_id

    # Find all relevant submissions
    submissions = list(mongo.db.e164_submissions.find(query,
                                                    {'_id': 1, 'operator': 1, 'reporting_period': 1}))

    # Process each submission
    results = []
    for submission in submissions:
        # Get aggregated data for this submission
        aggregated = aggregate_submission_data(str(submission['_id']))
        if aggregated:
            # Add to results
            results.append({
                'operator': submission['operator'],
                'period': submission['reporting_period'],
                'utilization_rate': aggregated['metrics']['utilization_rate'],
                'dormancy_rate': 100 - aggregated['metrics']['utilization_rate'],  # Inverse of utilization
                'total_allocated': aggregated['metrics']['total_allocated'],
                'total_active': aggregated['metrics']['total_active']
            })

    # Sort by period for better visualization
    results.sort(key=lambda x: x['period'])
    return jsonify(results)




@e164_bp.route('/api/porting-activity')
@login_required
def get_porting_activity():
    """
    API endpoint to retrieve number porting activity data formatted for Sankey diagram.
    Includes aggregated counts and details (NDC, Range Type) for each link.
    Handles both admin (aggregated) and operator (specific) views based on the latest approved submission.
    URL: /e164/api/porting-activity
    """
    current_app.logger.info(f"API: Porting activity called by user: {current_user.email}")
    operator_name = "Aggregated" # Default title part for admin view
    reporting_period = "N/A" # Default period

    try:
        # Determine query based on user role
        query = {'status': 'approved'} # Base query for approved submissions

        if not current_user.is_admin:
            if not hasattr(current_user, 'operator_id') or not current_user.operator_id:
                 current_app.logger.warning(f"API: Operator user {current_user.email} has no operator_id associated.")
                 return jsonify({'success': False, 'error': 'User not associated with an operator.'}), 400
            query['operator_id'] = str(current_user.operator_id)
            current_app.logger.info(f"API: Fetching porting data for operator_id: {current_user.operator_id}")
        else:
            current_app.logger.info("API: Fetching aggregated porting data for admin.")

        # Find the most recent approved submission matching the query
        most_recent_submission = mongo.db.e164_submissions.find_one(
            query,
            sort=[('reporting_period', -1), ('created_at', -1)]
        )

        if not most_recent_submission:
            current_app.logger.warning(f"API: No approved submission found for query: {query}")
            return jsonify({'success': True, 'nodes': [], 'links': [], 'operator': operator_name, 'period': reporting_period})

        submission_id = str(most_recent_submission['_id'])
        operator_name = most_recent_submission.get('operator', 'Unknown Operator') if not current_user.is_admin else "Aggregated"
        reporting_period = most_recent_submission.get('reporting_period', 'Unknown Period')
        current_app.logger.info(f"API: Found submission ID: {submission_id} for period {reporting_period} (Operator: {operator_name})")

        # Retrieve porting details for this submission, including ndc and range_type
        porting_details = list(mongo.db.e164_porting_details.find(
            {'submission_id': submission_id},
            # Projection to include necessary fields
            {'source_operator': 1, 'target_operator': 1, 'count': 1, 'ndc': 1, 'range_type': 1, '_id': 0}
        ))

        if not porting_details:
            current_app.logger.info(f"API: No porting details found for submission ID: {submission_id}")
            return jsonify({'success': True, 'nodes': [], 'links': [], 'operator': operator_name, 'period': reporting_period})

        current_app.logger.info(f"API: Found {len(porting_details)} porting detail records.")

        # Process data: Aggregate counts AND details per source/target pair
        # Use defaultdict for easier aggregation
        # Structure: {(source_op, target_op): {'value': total_count, 'details': set_of_detail_strings}}
        aggregated_links = defaultdict(lambda: {'value': 0, 'details': set()})
        all_operators = set()

        for record in porting_details:
            source = record.get('source_operator')
            target = record.get('target_operator')
            ndc = record.get('ndc', 'N/A')
            range_type = record.get('range_type', 'N/A')
            try:
                count = int(record.get('count', 0))
            except (ValueError, TypeError):
                count = 0

            if source and target and count > 0:
                link_key = (source, target)
                aggregated_links[link_key]['value'] += count
                # Add detail string (e.g., "NDC 020 (WIRELESS)") to a set to avoid duplicates
                aggregated_links[link_key]['details'].add(f"NDC {ndc} ({range_type})")
                all_operators.add(source)
                all_operators.add(target)

        # Create nodes list [{ 'name': 'Operator A' }, ...]
        operator_list = sorted(list(all_operators))
        node_map = {name: i for i, name in enumerate(operator_list)} # Map name to index
        sankey_nodes = [{'name': name} for name in operator_list]

        # Create links list [{ 'source': index1, 'target': index2, 'value': count, 'details': '...' }, ...]
        sankey_links = []
        for (source_op, target_op), data in aggregated_links.items():
            if source_op in node_map and target_op in node_map:
                 sankey_links.append({
                    'source': node_map[source_op], # Use index from map
                    'target': node_map[target_op], # Use index from map
                    'value': data['value'],
                    # Join the set of details into a comma-separated string
                    'details': ", ".join(sorted(list(data['details'])))
                })

        current_app.logger.info(f"API: Generated {len(sankey_nodes)} nodes and {len(sankey_links)} links for Sankey.")

        return jsonify({
            'success': True,
            'nodes': sankey_nodes,
            'links': sankey_links, # Links now include 'details'
            'operator': operator_name,
            'period': reporting_period
        })

    except Exception as e:
        current_app.logger.error(f"API: Error fetching porting activity: {e}", exc_info=True)
        return jsonify({'success': False, 'error': f'An internal server error occurred while fetching porting data.'}), 500





@e164_bp.route('/api/resource-planning')
@login_required
def get_resource_planning():
    """API to get resource planning data including projected exhaustion"""
    # Get operator restriction if applicable
    operator_id = None
    if not current_user.is_admin:
        operator_id = current_user.operator_id

    # Get the latest submission for each operator (or just the current operator)
    if operator_id:
        latest_submissions = list(mongo.db.e164_submissions.find(
            {'operator_id': operator_id, 'status': 'approved'},
            sort=[('reporting_period', -1)],
            limit=1
        ))
    else:
        # For admin, get the latest submission for each operator
        pipeline = [
            {"$match": {"status": "approved"}},
            {"$sort": {"reporting_period": -1}},
            {"$group": {
                "_id": "$operator_id",
                "submission_id": {"$first": "$_id"},
                "operator": {"$first": "$operator"},
                "reporting_period": {"$first": "$reporting_period"}
            }}
        ]
        latest_submissions = list(mongo.db.e164_submissions.aggregate(pipeline))

    # Initialize results array
    results = []

    # Process each submission
    for submission in latest_submissions:
        submission_id = submission.get('submission_id') or submission.get('_id')
        operator = submission.get('operator')

        # Get all ranges for this submission
        ranges = list(mongo.db.e164_ranges.find({'submission_id': str(submission_id)}))

        # Process each NDC in this submission
        for range_doc in ranges:
            ndc = range_doc.get('ndc')

            # Calculate projected exhaustion
            projected_exhaustion = calculate_exhaustion_for_ndc(range_doc)

            # Add to results
            results.append({
                'operator': operator,
                'ndc': ndc,
                'type': range_doc.get('type'),
                'total_allocated': range_doc.get('total_allocated', 0),
                'total_active': range_doc.get('active_subscriber', 0) + range_doc.get('active_non_subscriber', 0),
                'utilization_rate': range_doc.get('utilization_rate', 0),
                'projected_exhaustion': projected_exhaustion,
                'efficiency': calculate_block_efficiency(range_doc)
            })

    return jsonify(results)



# Helper function to calculate projected exhaustion
def calculate_exhaustion_for_ndc(range_doc):
    """Calculate projected years until exhaustion for an NDC"""
    # Get current utilization
    total_allocated = range_doc.get('total_allocated', 0)
    total_active = range_doc.get('active_subscriber', 0) + range_doc.get('active_non_subscriber', 0)

    # If no numbers are allocated or all are used, return appropriate values
    if total_allocated == 0:
        return 0
    if total_active >= total_allocated:
        return 0  # Already exhausted

    # Get growth rate from historical data
    # If we have gross_addition data, use that
    gross_addition = range_doc.get('gross_addition', 0)
    net_addition = range_doc.get('net_addition', 0)

    # Calculate annual growth rate (use net_addition or gross_addition)
    growth_rate = max(net_addition, gross_addition / 2)  # Estimate half-year growth

    # If no growth or negative growth, return a large number (effectively "never")
    if growth_rate <= 0:
        return 99.9

    # Calculate remaining numbers
    remaining = total_allocated - total_active

    # Calculate years until exhaustion (simple linear projection)
    years_until_exhaustion = remaining / growth_rate

    # Cap at reasonable limit
    return min(round(years_until_exhaustion, 1), 99.9)

# Helper function to calculate block efficiency
def calculate_block_efficiency(range_doc):
    """Calculate block efficiency percentage"""
    # Simple version: utilization rate with penalties for fragmentation
    utilization_rate = range_doc.get('utilization_rate', 0)

    # For now, we'll just return utilization rate
    # In a more sophisticated implementation, we could add penalties for:
    # - High dormancy (numbers that were active but now inactive)
    # - Excessive reservations
    # - Fragmented number blocks

    return round(utilization_rate, 1)



@e164_bp.route('/submission/<submission_id>/report', methods=['GET'])
@login_required
def download_report(submission_id):
    """Generate and download a report for a specific E.164 submission"""
    # Get the submission
    submission = mongo.db.e164_submissions.find_one({'_id': ObjectId(submission_id)})
    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard'))

    # Check if the user is authorized to download this report
    if not current_user.is_admin and str(submission.get('operator_id')) != current_user.operator_id:
        flash('You are not authorized to download this report.', 'danger')
        return redirect(url_for('e164.dashboard'))

    # Get the ranges for this submission
    ranges = list(mongo.db.e164_ranges.find({'submission_id': submission_id}))

    # Create a filename for the report
    filename = f"Numbering_Audit_report_{submission.get('operator')}_{submission.get('reporting_period')}.pdf"

    # Create a buffer for the PDF
    buffer = BytesIO()

    # Create the PDF document
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()

    # Build content for the PDF
    content = []

    # Add title
    title = Paragraph(f"E.164 Number Range Audit Report", styles['Title'])
    content.append(title)
    content.append(Spacer(1, 12))

    # Add submission details
    content.append(Paragraph(f"Operator: {submission.get('operator')}", styles['Heading2']))
    content.append(Paragraph(f"Reporting Period: {submission.get('reporting_period')}", styles['Normal']))
    content.append(Paragraph(f"Submission Date: {submission.get('created_at').strftime('%Y-%m-%d %H:%M:%S')}", styles['Normal']))
    content.append(Paragraph(f"Status: {submission.get('status').upper()}", styles['Normal']))
    content.append(Spacer(1, 12))

    # Calculate summary metrics
    total_allocated = sum(r.get('total_allocated', 0) for r in ranges)
    total_active_subscriber = sum(r.get('active_subscriber', 0) for r in ranges)
    total_active_non_subscriber = sum(r.get('active_non_subscriber', 0) for r in ranges)
    total_active = total_active_subscriber + total_active_non_subscriber
    total_inactive = sum(r.get('inactive', 0) for r in ranges)

    # Add summary metrics
    content.append(Paragraph("Summary Metrics", styles['Heading2']))
    metrics_data = [
        ["Metric", "Value"],
        ["Total Allocated Numbers", f"{total_allocated:,}"],
        ["Total Active Numbers", f"{total_active:,}"],
        ["Active Subscriber Numbers", f"{total_active_subscriber:,}"],
        ["Active Non-Subscriber Numbers", f"{total_active_non_subscriber:,}"],
        ["Inactive Numbers", f"{total_inactive:,}"],
        ["Overall Utilization Rate", f"{(total_active / total_allocated * 100) if total_allocated > 0 else 0:.2f}%"]
    ]

    metrics_table = Table(metrics_data)
    metrics_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (1, 0), 12),
        ('BACKGROUND', (0, 1), (1, -1), colors.beige),
        ('GRID', (0, 0), (1, -1), 1, colors.black)
    ]))
    content.append(metrics_table)
    content.append(Spacer(1, 12))

    # Add ranges table
    content.append(Paragraph("Number Ranges", styles['Heading2']))

    # Prepare data for ranges table
    ranges_data = [["NDC", "Type", "Total Allocated", "Active", "Inactive", "Utilization"]]
    for r in ranges:
        active = r.get('active_subscriber', 0) + r.get('active_non_subscriber', 0)
        utilization = f"{(active / r.get('total_allocated', 1) * 100):.2f}%"
        ranges_data.append([
            r.get('ndc', ''),
            r.get('type', ''),
            f"{r.get('total_allocated', 0):,}",
            f"{active:,}",
            f"{r.get('inactive', 0):,}",
            utilization
        ])

    ranges_table = Table(ranges_data)
    ranges_table.setStyle(TableStyle([
        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('FONTSIZE', (0, 0), (-1, 0), 12),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
        ('GRID', (0, 0), (-1, -1), 1, colors.black)
    ]))
    content.append(ranges_table)

    # Build the PDF
    doc.build(content)

    # Get the PDF data
    pdf_data = buffer.getvalue()
    buffer.close()

    # Log the activity
    log_activity("Report Downloaded", f"User {current_user.contact} downloaded report for {submission.get('operator')} ({submission.get('reporting_period')})")

    # Send the PDF file
    return send_file(
        BytesIO(pdf_data),
        mimetype='application/pdf',
        as_attachment=True,
        download_name=filename

    )

@e164_bp.app_template_global()
def get_porting_records(submission_id):
    """
    Retrieve porting records for a submission
    This function is used in the template to get porting data
    """
    try:
        # Convert string ID to ObjectId if needed
        if not isinstance(submission_id, ObjectId):
            try:
                obj_id = ObjectId(submission_id)
            except:
                obj_id = submission_id
        else:
            obj_id = submission_id

        # Query the database
        records = list(mongo.db.e164_porting_details.find({
            'submission_id': str(obj_id)
        }).sort('source_operator', 1))

        return records
    except Exception as e:
        print(f"Error retrieving porting records: {e}")
        return []


@e164_bp.route('/submission/<submission_id>/invoice/download')
@login_required # Or @admin_required if only admins can download
def download_e164_invoice(submission_id):
    """
    Generates and serves an Excel invoice for a given E.164 submission.
    """
    # Authorization: Ensure the user is an admin or owns the submission if it's an operator
    try:
        obj_submission_id = ObjectId(submission_id)
    except:
        flash('Invalid Submission ID.', 'danger')
        return redirect(url_for('e164.dashboard')) # Or relevant error page

    submission = mongo.db.e164_submissions.find_one({'_id': obj_submission_id})

    if not submission:
        flash('Submission not found.', 'danger')
        return redirect(url_for('e164.dashboard'))

    is_admin_user = getattr(current_user, 'is_admin', False)
    # Assuming operator_id in submission is stored as string
    submission_operator_id_str = str(submission.get('operator_id'))
    current_user_operator_id_str = str(getattr(current_user, 'operator_id', None))

    if not is_admin_user and submission_operator_id_str != current_user_operator_id_str:
        flash('You are not authorized to generate this invoice.', 'danger')
        return redirect(url_for('e164.dashboard')) # Or specific operator dashboard

    invoice_data = calculate_e164_invoice(submission_id)

    if not invoice_data:
        flash('Could not calculate invoice data for this submission.', 'danger')
        return redirect(url_for('e164.view_submission', submission_id=submission_id))

    excel_file_io = generate_invoice_excel(invoice_data)
    if not excel_file_io:
        flash('Could not generate the Excel file for the invoice.', 'danger')
        return redirect(url_for('e164.view_submission', submission_id=submission_id))

    operator_name_safe = "".join(c if c.isalnum() else "_" for c in invoice_data['submission_details']['operator_name'])
    period_safe = invoice_data['submission_details']['reporting_period'].replace("-", "_")
    
    filename = f"E164_Invoice_{operator_name_safe}_{period_safe}.xlsx"
    
    log_activity(
        "E.164 Invoice Downloaded",
        f"User {current_user.contact} downloaded invoice for submission {submission_id} (Operator: {operator_name_safe}, Period: {period_safe})"
    )

    return send_file(
        excel_file_io,
        as_attachment=True,
        download_name=filename, # Use the new attribute for Flask 2.0+
        mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )










def get_status_badge_class(status):
    """
    Maps a submission status string to a Bootstrap background color class.
    Used as a Jinja filter.
    """
    status_lower = status.lower() if status else ''
    if status_lower == 'approved':
        return 'success'
    elif status_lower == 'pending':
        return 'warning'
    elif status_lower == 'rejected':
        return 'danger'
    elif status_lower == 'draft':
        return 'secondary'
    else:
        return 'light' # Default or unknown status


# Register the blueprint with the app - keep this at the module level
def register_e164_blueprint(app):
    app.register_blueprint(e164_bp)


