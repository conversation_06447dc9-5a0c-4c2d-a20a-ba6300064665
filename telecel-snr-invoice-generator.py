# Install these dependencies before running the script:
# pip install pymongo pandas numpy openpyxl

import pandas as pd
import numpy as np
from pymongo import MongoClient
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import os
from datetime import datetime
import re

# MongoDB connection
def connect_to_mongodb():
    uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
    client = MongoClient(uri)
    return client

# Function to retrieve app data from MongoDB
def get_app_data():
    client = connect_to_mongodb()
    db = client.get_database("captains_log")
    collection = db.get_collection(name="app")
    data = list(collection.find())
    app_df = pd.DataFrame(data)
    return app_df

# Fee structure for SNR audit
fees = {
    '3': 2100,  # 3-digit codes
    '4': 500,   # 4-digit codes
    '5': 200,   # 5-digit codes
    '6': 150    # 6-digit codes
}

def standardize_snr(snr):
    """Standardize SNR format for consistent processing"""
    if pd.isna(snr) or snr is None:
        return ''

    snr_str = str(snr).strip()

    # Remove spaces, hyphens, etc.
    snr_str = re.sub(r'[\s\-\.]', '', snr_str)

    # For toll-free numbers, keep the format
    if snr_str.startswith('0800'):
        return snr_str

    # Otherwise, remove leading zeros
    return snr_str.lstrip('0')

def appDbLookup(snr, app_df):
    """
    Look up SNR in the application database

    Args:
        snr: The SNR to look up
        app_df: DataFrame containing application data

    Returns:
        dict: Information about the SNR from the database
    """
    std_snr = standardize_snr(snr)

    # Check if this SNR exists in the app database
    matches = app_df[app_df['snr'].apply(lambda x: standardize_snr(x) == std_snr)]

    if matches.empty:
        return {
            'applicant': 'Unknown',
            'vasp': 'Unknown',
            'auth_date': None,
            'telecel_check': False,
            'applications': []
        }

    # Get the most recent record
    latest = matches.iloc[-1]

    applications = []
    if latest.get('ussdcheck') == 'on':
        applications.append('USSD')
    if latest.get('smscheck') == 'on':
        applications.append('SMS')
    if latest.get('odacheck') == 'on':
        applications.append('Other Data Services')

    return {
        'applicant': latest.get('applicant', 'Unknown'),
        'vasp': latest.get('vasp', 'Unknown'),
        'auth_date': latest.get('cert_date', None),
        'telecel_check': latest.get('telecelcheck') == 'on',  # Assuming telecelcheck field exists
        'applications': applications
    }

def determine_fee_and_description(snr, snr_type, db_info):
    """
    Determine the appropriate fee and description for an SNR

    Args:
        snr: The SNR code
        snr_type: Type of SNR (from submission)
        db_info: Database information for the SNR

    Returns:
        tuple: (fee_amount, description, category)
    """
    std_snr = standardize_snr(snr)
    length = len(std_snr)

    # Special cases for toll-free and premium numbers
    if 'toll free' in str(snr_type).lower() or std_snr.startswith('0800') or std_snr.startswith('800'):
        return 0, "Toll-Free Number", "Toll-Free"

    if 'premium' in str(snr_type).lower() or std_snr.startswith('0900') or std_snr.startswith('900'):
        return fees.get('4', 500), "Premium Rate Service", "Premium"

    # Harmonized short codes (usually 3 digits) - free
    if length == 3:
        # Emergency codes
        if std_snr in ['110', '111', '112', '191', '192', '193', '194', '198', '199']:
            return 0, "Emergency Service (Harmonized)", "Emergency"
        # Service codes
        elif std_snr in ['100', '101', '102', '103', '104', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']:
            return 0, "Service Code (Harmonized)", "Service"
        else:
            return fees.get('3', 2100), "3-Digit Short Code", "3-Digit"

    # 4-digit codes
    elif length == 4:
        if std_snr.startswith('2'):
            return fees.get('4', 500), "Mobile Network Operator Service", "MNO Service"
        else:
            return fees.get('4', 500), "4-Digit Short Code", "4-Digit"

    # 5-digit codes
    elif length == 5:
        return fees.get('5', 200), "5-Digit Short Code", "5-Digit"

    # 6-digit codes
    elif length == 6:
        return fees.get('6', 150), "6-Digit Short Code", "6-Digit"

    # Default case
    else:
        return fees.get('4', 500), f"{length}-Digit Code", "Other"

def process_telecel_snr_data(file_path, app_df):
    """
    Process Telecel SNR data and calculate fees

    Args:
        file_path: Path to the Telecel SNR Excel file
        app_df: DataFrame containing application data

    Returns:
        DataFrame: Processed SNR data with fees and descriptions
    """
    print(f"Processing Telecel SNR data from: {file_path}")

    # Read the Excel file
    telecel_df = pd.read_excel(file_path)

    # Clean column names
    telecel_df.columns = [col.strip() for col in telecel_df.columns]

    # Auto-detect important columns
    snr_column = 'SNR'
    type_column = None
    user_column = None
    service_column = None

    # Find type column
    for col in telecel_df.columns:
        if 'TYPE' in col.upper():
            type_column = col
            break

    # Find user column
    possible_user_columns = [
        'USER(OPERATOR OR THIRD PARTY)',
        'USER',
        'OPERATOR',
        'THIRD PARTY',
        'USER/OPERATOR',
        'OPERATOR/THIRD PARTY'
    ]

    for col in possible_user_columns:
        if col in telecel_df.columns:
            user_column = col
            break

    # Find service column
    for col in telecel_df.columns:
        if 'SERVICE' in col.upper() or 'DESCRIPTION' in col.upper():
            service_column = col
            break

    print(f"Detected columns - SNR: {snr_column}, Type: {type_column}, User: {user_column}, Service: {service_column}")

    # Process each SNR
    processed_data = []

    for _, row in telecel_df.iterrows():
        snr = row[snr_column]
        snr_type = row[type_column] if type_column else None
        user = row[user_column] if user_column else 'Telecel'
        service = row[service_column] if service_column else 'Not specified'

        # Look up in database
        db_info = appDbLookup(snr, app_df)

        # Determine fee and description
        fee, description, category = determine_fee_and_description(snr, snr_type, db_info)

        processed_data.append({
            'SNR': snr,
            'Type': snr_type,
            'User': user,
            'Service': service,
            'Fee_GHS': fee,
            'Description': description,
            'Category': category,
            'DB_Applicant': db_info['applicant'],
            'DB_VASP': db_info['vasp'],
            'DB_Applications': ', '.join(db_info['applications']) if db_info['applications'] else 'None',
            'Telecel_Authorized': db_info['telecel_check']
        })

    return pd.DataFrame(processed_data)

def generate_telecel_invoice(processed_df, output_path="Telecel_SNR_Invoice_2024"):
    """
    Generate invoice Excel file for Telecel SNR audit

    Args:
        processed_df: DataFrame with processed SNR data
        output_path: Output directory path
    """
    print(f"Generating Telecel invoice...")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Calculate totals
    total_snrs = len(processed_df)
    total_fee = processed_df['Fee_GHS'].sum()

    # Group by category for summary
    category_summary = processed_df.groupby('Category').agg({
        'SNR': 'count',
        'Fee_GHS': 'sum'
    }).reset_index()
    category_summary.columns = ['Category', 'Count', 'Total_Fee_GHS']

    # Create Excel workbook
    wb = Workbook()

    # Invoice Summary Sheet
    ws_summary = wb.active
    ws_summary.title = "Invoice Summary"

    # Add header information
    ws_summary['A1'] = "TELECEL SNR AUDIT INVOICE - 2024"
    ws_summary['A1'].font = Font(bold=True, size=16)
    ws_summary['A3'] = f"Invoice Date: {datetime.now().strftime('%Y-%m-%d')}"
    ws_summary['A4'] = f"Total SNRs: {total_snrs}"
    ws_summary['A5'] = f"Total Fee: GHS {total_fee:,.2f}"

    # Add category breakdown
    ws_summary['A7'] = "Category Breakdown:"
    ws_summary['A7'].font = Font(bold=True)

    # Headers for category table
    headers = ['Category', 'Count', 'Fee per Unit (GHS)', 'Total Fee (GHS)']
    for col, header in enumerate(headers, 1):
        cell = ws_summary.cell(row=8, column=col, value=header)
        cell.font = Font(bold=True)

    # Add category data
    for idx, row in category_summary.iterrows():
        ws_summary.cell(row=9+idx, column=1, value=row['Category'])
        ws_summary.cell(row=9+idx, column=2, value=row['Count'])

        # Calculate fee per unit
        if row['Count'] > 0:
            fee_per_unit = row['Total_Fee_GHS'] / row['Count']
        else:
            fee_per_unit = 0

        ws_summary.cell(row=9+idx, column=3, value=f"{fee_per_unit:.2f}")
        ws_summary.cell(row=9+idx, column=4, value=f"{row['Total_Fee_GHS']:.2f}")

    # Detailed SNR List Sheet
    ws_detail = wb.create_sheet("Detailed SNR List")

    # Add headers
    detail_headers = ['SNR', 'Type', 'User', 'Service', 'Category', 'Fee (GHS)', 'Description', 'DB_Applicant', 'DB_VASP', 'Telecel_Authorized']
    for col, header in enumerate(detail_headers, 1):
        cell = ws_detail.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)

    # Add data
    for idx, row in processed_df.iterrows():
        ws_detail.cell(row=idx+2, column=1, value=row['SNR'])
        ws_detail.cell(row=idx+2, column=2, value=row['Type'])
        ws_detail.cell(row=idx+2, column=3, value=row['User'])
        ws_detail.cell(row=idx+2, column=4, value=row['Service'])
        ws_detail.cell(row=idx+2, column=5, value=row['Category'])
        ws_detail.cell(row=idx+2, column=6, value=row['Fee_GHS'])
        ws_detail.cell(row=idx+2, column=7, value=row['Description'])
        ws_detail.cell(row=idx+2, column=8, value=row['DB_Applicant'])
        ws_detail.cell(row=idx+2, column=9, value=row['DB_VASP'])
        ws_detail.cell(row=idx+2, column=10, value=row['Telecel_Authorized'])

    # Auto-adjust column widths
    for ws in [ws_summary, ws_detail]:
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook
    output_file = os.path.join(output_path, "Telecel_SNR_Invoice_2024.xlsx")
    wb.save(output_file)

    print(f"Invoice generated successfully: {output_file}")
    print(f"Total SNRs: {total_snrs}")
    print(f"Total Fee: GHS {total_fee:,.2f}")

    return output_file, total_fee

def main():
    """Main function to process Telecel SNR data and generate invoice"""

    # File paths
    telecel_file = "Telecel_SNR_Invoice_2024.xlsx"
    output_dir = "Telecel_SNR_Invoice_2024_Output"

    print("=== Telecel SNR Invoice Generator ===")
    print(f"Looking for file: {telecel_file}")

    if not os.path.exists(telecel_file):
        print(f"Error: File '{telecel_file}' not found!")
        print("Please ensure the Telecel SNR file exists in the current directory.")
        return

    try:
        # Load application database
        print("Loading NCA application database...")
        app_df = get_app_data()
        print(f"Loaded {len(app_df)} records from database")

        # Process Telecel SNR data
        print("Processing Telecel SNR data...")
        processed_df = process_telecel_snr_data(telecel_file, app_df)

        # Generate invoice
        print("Generating invoice...")
        invoice_file, total_fee = generate_telecel_invoice(processed_df, output_dir)

        # Save processed data as CSV for reference
        csv_file = os.path.join(output_dir, "Telecel_SNR_Processed_Data.csv")
        processed_df.to_csv(csv_file, index=False)

        print("\n=== PROCESSING COMPLETE ===")
        print(f"Invoice file: {invoice_file}")
        print(f"Processed data: {csv_file}")
        print(f"Total SNRs processed: {len(processed_df)}")
        print(f"Total invoice amount: GHS {total_fee:,.2f}")

        # Print category breakdown
        print("\nCategory Breakdown:")
        category_summary = processed_df.groupby('Category').agg({
            'SNR': 'count',
            'Fee_GHS': 'sum'
        }).reset_index()

        for _, row in category_summary.iterrows():
            print(f"  {row['Category']}: {row['SNR']} SNRs, GHS {row['Fee_GHS']:,.2f}")

    except Exception as e:
        print(f"Error processing data: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
