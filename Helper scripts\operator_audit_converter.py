"""
Convert Operator Audit Excel files to MongoDB JSON format.

Usage:
python operator_audit_converter.py <excel_file> <operator_name>

This script will read the audit Excel file for the specified operator and
convert it to JSON format that can be imported into the MongoDB 'audit' collection.
"""

import sys
import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
import re

def determine_snr_type(snr):
    """Determine SNR type based on the number pattern."""
    snr = str(snr).strip()
    
    if snr.startswith('0800'):
        return 'tollfree'
    elif snr.startswith('0900'):
        return 'premium'
    elif len(snr) <= 6:
        return 'shortcode'
    else:
        return 'unknown'

def clean_number(value):
    """Convert number strings to integers, handling NaN and invalid values."""
    if pd.isna(value) or value is None:
        return 0
    
    if isinstance(value, (int, float)):
        return int(value)
    
    try:
        # Remove non-numeric characters and convert to int
        value = re.sub(r'[^\d.-]', '', str(value))
        return int(float(value)) if value else 0
    except (ValueError, TypeError):
        return 0

def clean_block_number(value):
    """Clean block numbers, removing leading zeros."""
    if pd.isna(value) or value is None:
        return 0
    
    value_str = str(value).strip()
    # Remove leading zeros
    value_str = re.sub(r'^0+', '', value_str)
    
    try:
        return int(value_str) if value_str else 0
    except (ValueError, TypeError):
        return 0

def process_operator_audit(excel_file, operator_name):
    """
    Process an operator's audit Excel file and convert to MongoDB format.
    
    Args:
        excel_file: Path to the Excel file
        operator_name: Name of the operator (e.g., "MTN Ghana")
        
    Returns:
        list: List of dictionaries in MongoDB format
    """
    print(f"Processing {operator_name} data from {excel_file}...")
    
    # Check if file exists
    if not os.path.exists(excel_file):
        print(f"Error: File {excel_file} not found.")
        return []
    
    try:
        # Read the Excel file
        xls = pd.ExcelFile(excel_file)
        
        # Check for required sheets
        required_sheets = ["E.164 TYPE NUMBERS"]
        for sheet in required_sheets:
            if sheet not in xls.sheet_names:
                print(f"Error: Required sheet '{sheet}' not found in {excel_file}.")
                return []
        
        # Read E.164 TYPE NUMBERS sheet
        e164_df = pd.read_excel(xls, "E.164 TYPE NUMBERS")
        
        # Check if SNR sheet exists
        snr_details = {
            "total_snrs": 0,
            "service_categories": [],
            "digit_categories": {
                "3_digit": 0,
                "4_digit": 0,
                "5_digit": 0,
                "6_digit": 0
            }
        }
        
        if "SNR" in xls.sheet_names:
            snr_df = pd.read_excel(xls, "SNR")
            
            # Count SNRs by digit length
            if "SNR" in snr_df.columns:
                snr_details["total_snrs"] = len(snr_df)
                
                # Count digit categories
                for snr in snr_df["SNR"]:
                    snr_str = str(snr).strip()
                    if len(snr_str) == 3:
                        snr_details["digit_categories"]["3_digit"] += 1
                    elif len(snr_str) == 4:
                        snr_details["digit_categories"]["4_digit"] += 1
                    elif len(snr_str) == 5:
                        snr_details["digit_categories"]["5_digit"] += 1
                    elif len(snr_str) == 6:
                        snr_details["digit_categories"]["6_digit"] += 1
            
            # Get unique service categories
            if "SERVICE" in snr_df.columns:
                services = snr_df["SERVICE"].dropna().unique()
                snr_details["service_categories"] = [s for s in services if s]
        
        # Initialize porting data mapping
        porting_map = {}
        if "PORTING DATA" in xls.sheet_names:
            porting_df = pd.read_excel(xls, "PORTING DATA")
            
            # Check for required columns
            if all(col in porting_df.columns for col in ["Subscriber NDC e.g. 02A", "Cumulative Ported In Numbers within the Audit Period", "Cumulative Ported Out Numbers within the Audit Period"]):
                for _, row in porting_df.iterrows():
                    ndc = str(row["Subscriber NDC e.g. 02A"])
                    if not ndc:
                        continue
                    
                    ported_in = clean_number(row["Cumulative Ported In Numbers within the Audit Period"])
                    ported_out = clean_number(row["Cumulative Ported Out Numbers within the Audit Period"])
                    
                    porting_map[ndc] = {
                        "ported_in": ported_in,
                        "ported_out": ported_out
                    }
        
        # Process E.164 data to create audit documents
        mongodb_documents = []
        
        # Map column names to schema fields
        column_mapping = {
            "MNO": None,  # Operator name
            "TYPE of NDC": "sn_type",
            "NDC": "ndc",
            "STARTING BLOCK": "start_block",
            "ENDING BLOCK": "end_block",
            "TOTAL ASSIGNED NUMBERS (TAN)": "tan",
            "TAcN - SUBSCRIBER NUMBERS ": "tasn",  # There's a space at the end
            "TAcN - NON-SUBSCRIBER NUMBERS": "active_non_sub",
            "TOTAL INACTIVE NUMBERS (TIN) - SUBSCRIBER NUMBERS": "inactive_sub",
            "TOTAL INACTIVE NUMBERS (TIN) - NON-SUBSCRIBER NUMBERS": "inactive_non_sub",
            "[RESERVED SUBSCRIBER NUMBERS]": "reserved_sub",
            "[RESERVED NON-SUBSCRIBER NUMBERS]": "reserved_non_sub",
            "*GROSS ADDITION": "gross_add",
            "* NET ADDITION": "net_add",
            "*CUMULATIVE PORTED BACK (CPB)": "cpb",
            "*CUMULATIVE PORTED OUT (CPO)": "cpo"
        }
        
        # Iterate through each row
        for _, row in e164_df.iterrows():
            # Skip rows without operator name
            if pd.isna(row["MNO"]):
                continue
            
            # Extract data using the column mapping
            data = {}
            for excel_col, schema_field in column_mapping.items():
                if excel_col in row:
                    if schema_field:
                        if schema_field in ["start_block", "end_block"]:
                            data[schema_field] = clean_block_number(row[excel_col])
                        else:
                            data[schema_field] = clean_number(row[excel_col])
            
            # Skip rows without NDC
            if "ndc" not in data or not data["ndc"]:
                continue
            
            # Normalize NDC to string
            ndc = str(data["ndc"])

            
            
            
            # Calculate TAN (Total Assigned Numbers)
            tan = clean_number(row.get("TOTAL ASSIGNED NUMBERS (TAN)", 0))

            tasn = clean_number(row.get("TAcN - SUBSCRIBER NUMBERS ", 0))
            
            # Calculate derivatives
            tin = data.get("inactive_sub", 0) + data.get("inactive_non_sub", 0)
            trn = data.get("reserved_sub", 0) + data.get("reserved_non_sub", 0)
            
            # Calculate range size
            start_block = data.get("start_block", 0)
            end_block = data.get("end_block", 0)
            
            try:
                number_range_size = end_block - start_block + 1
                if number_range_size <= 0:
                    number_range_size = tan or 1000000  # Fallback if calculation is invalid
            except:
                number_range_size = tan or 1000000  # Fallback on error
            
            # Calculate metrics
            utilization_rate = (data.get("tasn", 0) + data.get("active_non_sub", 0)) / max(number_range_size, 1)
            percent_inactive = (tin / max(number_range_size, 1)) * 100
            percent_reserved = (trn / max(number_range_size, 1)) * 100
            
            # Get porting data for this NDC
            porting_data = porting_map.get(ndc, {"ported_in": 0, "ported_out": 0})
            porting_balance = porting_data["ported_in"] - porting_data["ported_out"]
            
            # Calculate performance scores
            utilization_score = min(3, utilization_rate * 3.33)
            porting_factor = min(1.0, max(0, 1 - abs(porting_balance) / 1000))
            porting_score = porting_factor * 3.33
            inactive_factor = max(0, 1 - (percent_inactive / 100))
            inactive_score = inactive_factor * 3.34
            overall_score = utilization_score + porting_score + inactive_score
            
            # Create audit document
            audit_doc = {
                "operator": operator_name,
                "quarter": "H1",  # First half of 2024
                "year": 2024,
                "sn_type": str(row.get("TYPE of NDC","")),
                "ndc": ndc,
                "start_block": data.get("start_block", 0),
                "end_block": data.get("end_block", 0),
                "tan": tan,
                "tasn": data.get("tasn", 0),
                "active_non_sub": data.get("active_non_sub", 0),
                "tin": tin,
                "inactive_sub": data.get("inactive_sub", 0),
                "inactive_non_sub": data.get("inactive_non_sub", 0),
                "trn": trn,
                "reserved_sub": data.get("reserved_sub", 0),
                "reserved_non_sub": data.get("reserved_non_sub", 0),
                "gross_add": data.get("gross_add", 0),
                "net_add": data.get("net_add", 0),
                "cpb": data.get("cpb", 0),
                "cpo": data.get("cpo", 0),
                "created_by": "admin",
                "timestamp": datetime.now().isoformat(),
                "processed": {
                    "utilization_rate": round(utilization_rate, 2),
                    "percent_inactive": round(percent_inactive, 2),
                    "percent_reserved": round(percent_reserved, 2),
                    "porting_balance": porting_balance
                },
                "snr_details": snr_details,
                "performance_scores": {
                    "utilization": round(utilization_score, 2),
                    "porting_balance": round(porting_score, 2),
                    "inactive": round(inactive_score, 2),
                    "overall": round(overall_score, 2)
                },
                # New fields for SNR tracking
                "active_snrs_count": 0,
                "deactivated_snrs_count": 0,
                "active_snrs": [],
                "deactivated_snrs": []
            }
            
            mongodb_documents.append(audit_doc)
        
        print(f"Processed {len(mongodb_documents)} records for {operator_name}.")
        return mongodb_documents
    
    except Exception as e:
        print(f"Error processing {excel_file}: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def main():
    if len(sys.argv) != 3:
        print("Usage: python operator_audit_converter.py <excel_file> <operator_name>")
        sys.exit(1)
    
    excel_file = sys.argv[1]
    operator_name = sys.argv[2]
    
    # Process the operator data
    mongodb_documents = process_operator_audit(excel_file, operator_name)
    
    if not mongodb_documents:
        print("No documents generated. Exiting.")
        sys.exit(1)
    
    # Save to JSON file
    output_file = f"{operator_name.replace(' ', '_')}_audit_data.json"
    with open(output_file, 'w') as f:
        json.dump(mongodb_documents, f, indent=2)
    
    print(f"Converted data saved to {output_file}")
    print(f"Total documents: {len(mongodb_documents)}")

if __name__ == "__main__":
    main()
