#!/usr/bin/env python
"""
Import script for AT's E.164 data for 2024-H2 to MongoDB.
Usage: python import_at_data_2024h2.py
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime, timezone
import json
from bson import ObjectId

# MongoDB connection details - update as needed
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

# The JSON data for E.164 submissions
e164_submission = {
    "operator_id": "AT_OP_ID",  # Will be updated if we find the real operator ID
    "operator": "AT",
    "reporting_period": "2024-h2",
    "contact_person": "Kwabena Anaafi",
    "contact_email": "<EMAIL>",
    "notes": "Second half 2024 E.164 number audit",
    "status": "approved"
}

# The JSON data for E.164 ranges - extracted from AT's Excel submission (exact values)
e164_ranges = [
    {
        "ndc": "056",
        "type": "WIRELESS SHARED",
        "start_block": "0560000000",
        "end_block": "0561999999",
        "total_allocated": 2000000,
        "active_subscriber": 66387,
        "active_non_subscriber": 0,
        "inactive": 1933613,
        "reserved": 0,
        "gross_addition": 10302.25,
        "net_addition": -41209,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 3.32,
        "dormancy_rate": 96.68,
        "reservation_rate": 0.0
    },
    {
        "ndc": "026",
        "type": "WIRELESS DEDICATED",
        "start_block": "0260000000",
        "end_block": "0269999999",
        "total_allocated": 10000000,
        "active_subscriber": 1290049,
        "active_non_subscriber": 0,
        "inactive": 8709951,
        "reserved": 0,
        "gross_addition": 179340.5,
        "net_addition": 286343.5,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 12.9,
        "dormancy_rate": 87.1,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0307",
        "type": "FIXED SHARED",
        "start_block": "0307000000",
        "end_block": "0307099999",
        "total_allocated": 100000,
        "active_subscriber": 458,
        "active_non_subscriber": 0,
        "inactive": 99542,
        "reserved": 0,
        "gross_addition": 56,
        "net_addition": -224,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.46,
        "dormancy_rate": 99.54,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0317",
        "type": "FIXED SHARED",
        "start_block": "0317000000",
        "end_block": "0317099999",
        "total_allocated": 100000,
        "active_subscriber": 31,
        "active_non_subscriber": 0,
        "inactive": 99969,
        "reserved": 0,
        "gross_addition": 4.5,
        "net_addition": 18,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0327",
        "type": "FIXED SHARED",
        "start_block": "0327000000",
        "end_block": "0327099999",
        "total_allocated": 100000,
        "active_subscriber": 37,
        "active_non_subscriber": 0,
        "inactive": 99963,
        "reserved": 0,
        "gross_addition": 5,
        "net_addition": -20,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.04,
        "dormancy_rate": 99.96,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0337",
        "type": "FIXED SHARED",
        "start_block": "0337000000",
        "end_block": "0337099999",
        "total_allocated": 100000,
        "active_subscriber": 26,
        "active_non_subscriber": 0,
        "inactive": 99974,
        "reserved": 0,
        "gross_addition": 2.75,
        "net_addition": -11,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0347",
        "type": "FIXED SHARED",
        "start_block": "0347000000",
        "end_block": "0347099999",
        "total_allocated": 100000,
        "active_subscriber": 16,
        "active_non_subscriber": 0,
        "inactive": 99984,
        "reserved": 0,
        "gross_addition": 2.25,
        "net_addition": 3,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0357",
        "type": "FIXED SHARED",
        "start_block": "0357000000",
        "end_block": "0357099999",
        "total_allocated": 100000,
        "active_subscriber": 5,
        "active_non_subscriber": 0,
        "inactive": 99995,
        "reserved": 0,
        "gross_addition": 0.5,
        "net_addition": -2,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.01,
        "dormancy_rate": 100.0,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0367",
        "type": "FIXED SHARED",
        "start_block": "0367000000",
        "end_block": "0367099999",
        "total_allocated": 100000,
        "active_subscriber": 4,
        "active_non_subscriber": 0,
        "inactive": 99996,
        "reserved": 0,
        "gross_addition": 1,
        "net_addition": -4,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.0,
        "dormancy_rate": 100.0,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0377",
        "type": "FIXED SHARED",
        "start_block": "0377000000",
        "end_block": "0377099999",
        "total_allocated": 100000,
        "active_subscriber": 21,
        "active_non_subscriber": 0,
        "inactive": 99979,
        "reserved": 0,
        "gross_addition": 2.5,
        "net_addition": -10,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0387",
        "type": "FIXED SHARED",
        "start_block": "0387000000",
        "end_block": "0387099999",
        "total_allocated": 100000,
        "active_subscriber": 3,
        "active_non_subscriber": 0,
        "inactive": 99997,
        "reserved": 0,
        "gross_addition": 0.25,
        "net_addition": -1,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.0,
        "dormancy_rate": 100.0,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0397",
        "type": "FIXED SHARED",
        "start_block": "0397000000",
        "end_block": "0397099999",
        "total_allocated": 100000,
        "active_subscriber": 8,
        "active_non_subscriber": 0,
        "inactive": 99992,
        "reserved": 0,
        "gross_addition": 0.75,
        "net_addition": -3,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.01,
        "dormancy_rate": 99.99,
        "reservation_rate": 0.0
    },
    {
        "ndc": "027",
        "type": "WIRELESS DEDICATED",
        "start_block": "0270000000",
        "end_block": "0279999999",
        "total_allocated": 10000000,
        "active_subscriber": 1318341,
        "active_non_subscriber": 0,
        "inactive": 8681659,
        "reserved": 0,
        "gross_addition": 188879,
        "net_addition": 279711,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 13.18,
        "dormancy_rate": 86.82,
        "reservation_rate": 0.0
    },
    {
        "ndc": "057",
        "type": "WIRELESS DEDICATED",
        "start_block": "0570000000",
        "end_block": "0579999999",
        "total_allocated": 10000000,
        "active_subscriber": 401770,
        "active_non_subscriber": 0,
        "inactive": 9598230,
        "reserved": 0,
        "gross_addition": 58283,
        "net_addition": -233132,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 4.02,
        "dormancy_rate": 95.98,
        "reservation_rate": 0.0
    }
]

# No porting details data in the 2024-H2 Excel file
e164_porting_details = []

def import_data():
    """Import the data into MongoDB"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Add timestamps and generate unique ID
    submission_id = str(ObjectId())
    now = datetime.now(timezone.utc)
    
    # Create the submission
    e164_submission.update({
        "_id": ObjectId(submission_id),
        "created_at": now,
        "updated_at": now,
        "created_by": "import_script",
        "updated_by": "import_script"
    })
    
    try:
        # Get the AT operator ID from the database if possible
        at_operator = db.operators.find_one({"name": "AT"})
        if at_operator:
            e164_submission["operator_id"] = str(at_operator["_id"])
            print(f"Found AT operator ID: {e164_submission['operator_id']}")
    except Exception as e:
        print(f"Warning: Could not find AT operator in database: {e}")
        print("Using placeholder operator_id")
    
    # Insert submission
    print("Inserting submission...")
    db.e164_submissions.insert_one(e164_submission)
    
    # Update ranges with submission_id and timestamps
    print(f"Processing {len(e164_ranges)} ranges...")
    for r in e164_ranges:
        r.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert ranges
    if e164_ranges:
        db.e164_ranges.insert_many(e164_ranges)
        print(f"Inserted {len(e164_ranges)} ranges")
    
    # Update porting data with submission_id and timestamps (empty for this dataset)
    print(f"Processing {len(e164_porting_details)} porting records...")
    for p in e164_porting_details:
        p.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert porting data (will be empty for this dataset)
    if e164_porting_details:
        db.e164_porting_details.insert_many(e164_porting_details)
        print(f"Inserted {len(e164_porting_details)} porting records")
    else:
        print("No porting records to insert for 2024-H2 dataset")
    
    print(f"Import complete. Submission ID: {submission_id}")
    return submission_id

def print_summary():
    """Print a summary of the data to be imported"""
    print("=" * 60)
    print("AT 2024-H2 E.164 AUDIT DATA IMPORT SUMMARY")
    print("=" * 60)
    print(f"Operator: {e164_submission['operator']}")
    print(f"Reporting Period: {e164_submission['reporting_period']}")
    print(f"Contact Person: {e164_submission['contact_person']}")
    print(f"Contact Email: {e164_submission['contact_email']}")
    print(f"Total Number Ranges: {len(e164_ranges)}")
    print(f"Total Porting Records: {len(e164_porting_details)}")
    
    # Calculate totals
    total_allocated = sum(r['total_allocated'] for r in e164_ranges)
    total_active = sum(r['active_subscriber'] + r['active_non_subscriber'] for r in e164_ranges)
    total_inactive = sum(r['inactive'] for r in e164_ranges)
    total_reserved = sum(r['reserved'] for r in e164_ranges)
    
    print(f"\nOverall Statistics:")
    print(f"  Total Allocated Numbers: {total_allocated:,}")
    print(f"  Total Active Numbers: {total_active:,}")
    print(f"  Total Inactive Numbers: {total_inactive:,}")
    print(f"  Total Reserved Numbers: {total_reserved:,}")
    print(f"  Overall Utilization Rate: {(total_active / total_allocated * 100):.2f}%")
    print(f"  Overall Dormancy Rate: {(total_inactive / total_allocated * 100):.2f}%")
    
    print(f"\nRange Breakdown:")
    wireless_ranges = [r for r in e164_ranges if "WIRELESS" in r['type']]
    fixed_ranges = [r for r in e164_ranges if "FIXED" in r['type']]
    
    print(f"  Wireless Ranges: {len(wireless_ranges)} (1 SHARED + {len([r for r in wireless_ranges if 'DEDICATED' in r['type']])} DEDICATED)")
    print(f"  Fixed Shared Ranges: {len(fixed_ranges)}")
    
    # Show top performing ranges
    top_ranges = sorted(e164_ranges, key=lambda x: x['utilization_rate'], reverse=True)[:3]
    print(f"\nTop 3 Utilization Rates:")
    for i, r in enumerate(top_ranges, 1):
        print(f"  {i}. NDC {r['ndc']} ({r['type']}): {r['utilization_rate']}%")
    
    print("=" * 60)

if __name__ == "__main__":
    print_summary()
    
    # Ask for confirmation before proceeding
    confirm = input("\nProceed with import? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        submission_id = import_data()
        print("Data successfully imported!")
        print(f"Submission ID: {submission_id}")
    else:
        print("Import cancelled.")
