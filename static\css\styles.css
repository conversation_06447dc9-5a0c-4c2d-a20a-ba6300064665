/* static/css/styles.css */

/* Body Background with Image and Overlay */
body {
    background-image: url('../image/background1.jpg'); /* Adjust the filename as needed */
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
    min-height: 100vh;
    margin: 0;
    padding: 0;
    position: relative;
    z-index: 0;
}

/* Overlay for Enhanced Contrast */
body::before {
    content: "";
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4); /* Adjust opacity and color */
    z-index: -1;
    pointer-events: none; /* Allow interactions to pass through */
}

/* Glassmorphic Card Styling */
.glassmorphic-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    /* Width is controlled via inline styles in HTML templates */
}



/* Form Labels and Titles */
.form-label {
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7); /* Optional: Enhance readability */
}

.card-title {
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7); /* Optional: Enhance readability */
}

/* Error Messages */
.text-danger {
    color: #ff4d4d !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7); /* Optional: Enhance readability */
}

/* Primary Button Styling */
.btn-primary {
    background-color: #8a5fff98;
    border: none;
    transition: background-color 0.3s ease;
}

.btn-primary:hover {
    background-color: #7b52f5;
}

/* Link Styling */
a {
    color: #fff;
    transition: color 0.3s ease;
}

a:hover {
    color: #7bdffec1;
    text-decoration: none;
}

/* Form Control Styling */
.form-control,
.form-control-file,
textarea {
    background: rgba(255, 255, 255, 0.2);
    color: #4e48f3;
    border: none;
    transition: background 0.3s ease, color 0.3s ease;
}

.form-control::placeholder {
    color: #0a0101a6;
}

/* Button Block Styling */
.btn-block {
    /* Ensures buttons span the full width of the card */
    display: block;
    width: 100%;
}

/* Additional Enhancements */

/* Smooth Transitions for Form Controls */
.form-control,
.form-control-file {
    transition: background 0.3s ease, color 0.3s ease;
}

/* Hover Effect for Cards */
.glassmorphic-card:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* form control default border color */
.form-control {
    border-color: #4e48f3;
}

/* Focus States for Accessibility */
.form-control:focus {
    border-color: #1d1a87;
    box-shadow: 0 0 0 0.2rem rgba(123, 173, 254, 0.67);
}



.glassmorphic-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    border: 1px solid rgba(255, 255, 255, 0.18);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.glassmorphic-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 16px 32px 0 rgba(31, 38, 135, 0.37);
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.card-text {
    font-size: 0.875rem;
    color: #555;
}

.list-group-item {
    background: transparent;
    border: none;
    font-size: 0.85rem;
}

/* Add to your stylesheet */
.audit-metric-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: #0d6efd;
}

/* Ensure dark dropdown items are styled correctly for navbar-dark */
.navbar-dark .dropdown-menu {
    background-color: #343a40; /* Match navbar bg-dark */
    border-color: rgba(255, 255, 255, 0.15);
}

.navbar-dark .dropdown-item {
    color: rgba(255, 255, 255, 0.75);
}

.navbar-dark .dropdown-item:hover,
.navbar-dark .dropdown-item:focus {
    color: #ffffff;
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .dropdown-divider {
    border-top-color: rgba(255, 255, 255, 0.15);
}