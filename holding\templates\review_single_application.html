<!-- templates/review_single_application.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Review Your Application</h2>
                    <p class="text-muted text-center">
                        Please review the details below before confirming your application.
                    </p>
                    
                    <!-- Applicant Information -->
                    <div class="mb-4">
                        <h4>Applicant Information</h4>
                        <p><strong>Applicant Name:</strong> {{ applicant_name }}</p>
                        <p><strong>Email:</strong> {{ email }}</p>
                        <p><strong>Phone:</strong> {{ phone }}</p>
                        <p><strong>Company:</strong> {{ company }}</p>
                        <p><strong>Application Date:</strong> {{ application_date }}</p>
                    </div>

                    <!-- <PERSON><PERSON> Details -->
                    <div class="card mt-3">
                        <div class="card-body">
                            <h5><strong>SNR:</strong> {{ snr_info[0]['snr'][0] }}</h5>
                            <p><strong>Type:</strong> {{ snr_info[0].type }}</p>
                            <p><strong>Purpose:</strong> {{ snr_info[0].purpose }}</p>
                            <p><strong>Status:</strong> {{ snr_info[0].status }}</p>
                            <p><strong>Certificate ID:</strong> {{ snr_info[0].certificate_id }}</p>
                        </div>
                    </div>

                    <!-- Confirmation form -->
                    <form method="POST" action="{{ url_for('confirm_apply') }}">
                        <input type="hidden" name="application_id" value="{{ application_id }}">
                        <input type="hidden" name="snr" value="{{ snr_info.snr }}">
                        <input type="hidden" name="purpose" value="{{ snr_info.purpose }}">
                        <input type="hidden" name="certificate_id" value="{{ snr_info.certificate_id }}">
                        <div class="mt-4 d-flex justify-content-between">
                            <!-- <a href="{{ url_for('apply', snr=snr_info.snr) }}" class="btn btn-secondary">Back to Edit</a> -->
                             <!-- templates/review_multiple_application.html -->
                            <a href="{{ url_for('search', from_review=1) }}" class="btn btn-secondary">Back to Edit</a>

                            <button type="submit" class="btn btn-success">Confirm and Submit Application</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
