"""
Import Telecel Audit Data to MongoDB

This script imports the Telecel audit data JSON file into the MongoDB 'audits' collection.

Usage:
python import_telecel_audit.py
"""

import os
import json
import sys
from pymongo import MongoClient
from datetime import datetime

# MongoDB connection settings - update with your connection string
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"
COLLECTION_NAME = "audits"

def import_telecel_audit():
    """Import Telecel audit data to MongoDB."""
    json_file = "Telecel_Ghana_audit_data.json"
    
    # Check if file exists
    if not os.path.exists(json_file):
        print(f"Error: File {json_file} not found. Run telecel_converter.py first.")
        return
    
    try:
        # Connect to MongoDB
        print("Connecting to MongoDB...")
        client = MongoClient(MONGO_URI)
        db = client[DB_NAME]
        collection = db[COLLECTION_NAME]
        
        # Read the JSON file
        print(f"Reading data from {json_file}...")
        with open(json_file, 'r') as f:
            audit_data = json.load(f)
        
        if not audit_data:
            print(f"No data found in {json_file}.")
            return
        
        # Process dates in each document
        for doc in audit_data:
            # Convert timestamp string to datetime object
            if isinstance(doc.get('timestamp'), str):
                doc['timestamp'] = datetime.fromisoformat(doc['timestamp'].replace('Z', '+00:00'))
        
        # Insert the documents
        print(f"Importing {len(audit_data)} documents to MongoDB...")
        result = collection.insert_many(audit_data)
        
        print(f"Successfully imported {len(result.inserted_ids)} Telecel audit records.")
        
        # Close the MongoDB connection
        client.close()
        
    except Exception as e:
        print(f"Error importing Telecel audit data: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    import_telecel_audit()
