{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <div class="unified-interface mb-5">
                        <ul class="nav nav-tabs mb-4" id="searchTabs">
                            <li class="nav-item"><a class="nav-link active" data-toggle="tab" href="#search">Text Search</a></li>
                            <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#ocr">Document OCR</a></li>
                            <li class="nav-item"><a class="nav-link" data-toggle="tab" href="#csv">Spreadsheet Upload</a></li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane fade show active" id="search">
                                <form method="POST" action="{{ url_for('search') }}">
                                    <div class="input-group">
                                        <input type="text" name="query" class="form-control" 
                                               placeholder="Enter number, applicant name, or keywords..." value="{{ query if query else '' }}" required>
                                        <div class="input-group-append">
                                            <button class="btn btn-primary" type="submit">Search</button>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="tab-pane fade" id="ocr">
                                <form method="POST" action="{{ url_for('process_form') }}" enctype="multipart/form-data">
                                    <div class="custom-file mb-3">
                                        <input type="file" name="file" class="custom-file-input" accept="image/*,.pdf" required>
                                        <label class="custom-file-label">Choose image file...</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-block">Process Document</button>
                                </form>
                            </div>
                            <div class="tab-pane fade" id="csv">
                                <form method="POST" action="{{ url_for('upload_csv') }}" enctype="multipart/form-data">
                                    <div class="custom-file mb-3">
                                        <input type="file" name="file" class="custom-file-input" accept=".csv,.xlsx" required>
                                        <label class="custom-file-label">Choose spreadsheet...</label>
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-block">Upload & Process</button>
                                </form>
                            </div>
                        </div>
                    </div>

                    {# Results Display - results is now always a list #}
                    {% if results %}
                        <div class="results-section">
                            <h4 class="mb-4">Search Results for "{{ query }}" ({{ results|length }} found)</h4>

                            {% if results|length == 1 %}
                                {# Single Result Display #}
                                {% set result = results[0] %}
                                <div class="card result-card mb-4">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <div>
                                                <h5 class="card-title mb-0">Number: {{ result.snr | default('N/A') }}</h5>
                                                {% if result.purpose %}
                                                    <p class="text-muted small mb-0">Purpose: {{ result.purpose }}</p>
                                                {% endif %}
                                            </div>
                                            <span class="badge 
                                                {% if result.availability == 'Available' %}badge-success
                                                {% elif result.availability == 'Not Available' %}badge-danger
                                                {% else %}badge-secondary{% endif %}">
                                                {{ result.availability | default('Unknown') }}
                                            </span>
                                        </div>
                                        <p class="card-text">{{ result.description | default('No description available') }}</p>
                                        {% if result.applicant_info %}
                                        <div class="applicant-info">
                                            <small class="text-muted">
                                                Current Status: {{ result.applicant_info.status | default('N/A') }}<br>
                                                Expiry: {{ result.applicant_info.expiry | default('N/A') }}<br>
                                                Assignee: {{ result.applicant_info.assignee | default('N/A') }}
                                            </small>
                                        </div>
                                        {% endif %}
                                        {% if result.availability == 'Available' %}
                                        <div class="mt-3">
                                            <a href="{{ url_for('apply', snr=result.snr) }}" 
                                               class="btn btn-primary btn-block">
                                                Apply for This Number
                                            </a>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% else %}
                                {# Multiple Results Display #}
                                <div class="row">
                                    <div class="col-md-6"> {# Available and Unavailable Numbers #}
                                        <div class="card result-list">
                                            <div class="card-header">
                                                Available Numbers ({{ results|selectattr('availability', 'eq', 'Available')|list|length }})
                                            </div>
                                            <div class="card-body available-numbers-container">
                                                {% for item in results if item.availability == 'Available' %}
                                                <div class="result-item draggable-item mb-2 p-2" draggable="true" data-snr="{{ item.snr | default('') }}" data-purpose="{{ item.purpose | default('') }}" data-available="true">
                                                    <div class="d-flex justify-content-between">
                                                        <div><strong>{{ item.snr | default('N/A') }}</strong>
                                                            {% if item.purpose %}<p class="mb-0 small text-muted">{{ item.purpose }}</p>{% endif %}
                                                        </div>
                                                        <span class="badge badge-success">Available</span>
                                                    </div>
                                                </div>
                                                {% else %}
                                                    {% if loop.first %}<p class="text-muted">No available numbers.</p>{% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                        <div class="card result-list mt-3">
                                            <div class="card-header">
                                                Unavailable/Unknown ({{ results|rejectattr('availability', 'eq', 'Available')|list|length }})
                                            </div>
                                            <div class="card-body">
                                                {% for item in results if item.availability != 'Available' %}
                                                <div class="result-item mb-2 p-2 unavailable" data-available="false">
                                                    <div class="d-flex justify-content-between">
                                                        <div><strong>{{ item.snr | default('N/A') }}</strong>
                                                            {% if item.purpose %}<p class="mb-0 small text-muted">{{ item.purpose }}</p>{% endif %}
                                                             {% if item.applicant_info and item.applicant_info.assignee and item.applicant_info.assignee != "N/A" %}<p class="mb-0 small text-info">Assignee: {{ item.applicant_info.assignee }}</p>{% endif %}
                                                        </div>
                                                        <span class="badge {% if item.availability == 'Not Available' %}badge-danger{% else %}badge-secondary{% endif %}">{{ item.availability | default('Unknown') }}</span>
                                                    </div>
                                                </div>
                                                {% else %}
                                                    {% if loop.first %}<p class="text-muted">No unavailable numbers.</p>{% endif %}
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6"> {# Purpose Assignment #}
                                        <div class="card purpose-assignment">
                                            <div class="card-header d-flex justify-content-between align-items-center">
                                                Application Purposes
                                                <button type="button" class="btn btn-sm btn-primary add-purpose"><i class="fas fa-plus"></i> Add Purpose</button>
                                            </div>
                                            <div class="card-body" id="purposeContainer">
                                                {% if results|selectattr('availability', 'eq', 'Available')|list|length > 0 %}
                                                    <div class="purpose-section mb-3"> <div class="position-relative">
                                                        <textarea class="form-control mb-2 purpose-input" placeholder="Enter purpose for dragged numbers..." required></textarea>
                                                        <div class="droppable-zone"><small class="text-muted drop-hint">Drag available numbers here</small></div>
                                                    </div></div>
                                                {% else %}
                                                    <p class="text-muted text-center p-3">No available numbers to assign.</p>
                                                {% endif %}
                                            </div>
                                            <div class="card-footer">
                                                <form method="POST" action="{{ url_for('apply_multiple_preview') }}" id="applyMultipleForm">
                                                    <input type="hidden" name="selected_snrs" id="selectedSnrs">
                                                    <input type="hidden" name="purposes" id="purposesData">
                                                    <button type="submit" class="btn btn-success btn-block">Apply for Selected</button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %} {# End of results|length == 1 #}
                        </div>
                    {% elif query %} {# query was made but results list is empty #}
                        <div class="alert alert-info mt-4">No results found for your query: "{{ query }}".</div>
                    {% endif %} {# End of if results (the list) #}
                </div> {# card-body #}
            </div> {# card #}
        </div> {# col #}
    </div> {# row #}
</div> {# container #}

{# Template for new purpose sections (used by JavaScript) - KEEP THIS #}
<template id="purposeSectionTemplate">
    <div class="purpose-section mb-3">
        <div class="position-relative">
            <textarea class="form-control mb-2 purpose-input" 
                      placeholder="Enter purpose description..." required></textarea>
            <div class="droppable-zone">
                 <small class="text-muted drop-hint">Drag available numbers here</small>
            </div>
            <button type="button" class="btn btn-sm btn-danger mt-2 remove-purpose-btn" style="position: absolute; top:0; right:0;">
                <i class="fas fa-times"></i>
            </button>
        </div>
    </div>
</template>

{# Your existing script and style tags for search.html should follow here #}
{# Ensure the JavaScript for drag-and-drop correctly references the new container for available numbers #}
<script>
    document.addEventListener("DOMContentLoaded", () => {
        let draggedItem = null;
        const availableNumbersContainer = document.querySelector('.available-numbers-container'); // Target this specifically
        const purposeContainer = document.getElementById('purposeContainer');
        const addPurposeBtn = document.querySelector('.add-purpose');
        const applyMultipleForm = document.getElementById('applyMultipleForm');
        const emptyPurposeTemplate = document.getElementById('purposeSectionTemplate');

        function initDraggables() {
            if (availableNumbersContainer) { // Check if the container exists
                availableNumbersContainer.querySelectorAll('.draggable-item[draggable="true"]').forEach(item => {
                    item.addEventListener('dragstart', handleDragStart);
                    item.addEventListener('dragend', handleDragEnd);
                });
            }
        }

        function handleDragStart(e) {
            if (this.dataset.available === 'false') {
                e.preventDefault();
                return;
            }
            draggedItem = this;
            setTimeout(() => this.classList.add('dragging'), 0); // Use timeout to allow browser to render drag image
        }

        function handleDragEnd() {
            if (draggedItem) { // Ensure draggedItem is not null
                draggedItem.classList.remove('dragging');
            }
            draggedItem = null;
        }

        function initDropZones() {
            document.querySelectorAll('.droppable-zone').forEach(zone => {
                zone.addEventListener('dragover', handleDragOver);
                zone.addEventListener('dragleave', handleDragLeave);
                zone.addEventListener('drop', handleDrop);
            });
        }

        function handleDragOver(e) {
            e.preventDefault();
            const targetZone = e.currentTarget;
            if (targetZone.classList.contains('droppable-zone')) { // Ensure it's a droppable zone
                 targetZone.classList.add('drag-over');
            }
        }

        function handleDragLeave(e) {
            const targetZone = e.currentTarget;
            if (targetZone.classList.contains('droppable-zone')) {
                targetZone.classList.remove('drag-over');
            }
        }

        function handleDrop(e) {
            e.preventDefault();
            const targetZone = e.currentTarget;
            if (!targetZone.classList.contains('droppable-zone')) return;

            targetZone.classList.remove('drag-over');
            
            if (draggedItem) {
                const purposeSection = targetZone.closest('.purpose-section');
                const purposeInput = purposeSection.querySelector('.purpose-input');
                const originalPurpose = draggedItem.dataset.purpose; // Get original purpose from data attribute

                if (originalPurpose && !purposeInput.value.trim()) {
                    purposeInput.value = originalPurpose;
                }
                
                // Remove hint if present
                const hint = targetZone.querySelector('.drop-hint');
                if (hint) hint.remove();

                targetZone.appendChild(draggedItem); // Move the original dragged item
                draggedItem.draggable = false; // Make it non-draggable once dropped
                draggedItem.classList.remove('draggable-item');
                draggedItem.classList.add('in-purpose');

                // Add remove button to the item
                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn btn-sm btn-link text-danger remove-item-btn';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.style.position = 'absolute';
                removeBtn.style.top = '2px';
                removeBtn.style.right = '2px';
                removeBtn.onclick = () => returnToAvailableList(draggedItem);
                draggedItem.style.position = 'relative'; // For positioning the remove button
                draggedItem.appendChild(removeBtn);
                
                draggedItem = null; // Clear draggedItem after successful drop
                updateFormDataAndButtonState();
            }
        }

        function returnToAvailableList(item) {
            if (availableNumbersContainer) {
                item.classList.remove('in-purpose');
                item.classList.add('draggable-item');
                item.draggable = true;
                item.querySelector('.remove-item-btn')?.remove(); // Remove the 'x' button
                availableNumbersContainer.appendChild(item);
                updateFormDataAndButtonState();
                // Re-add hint if the zone becomes empty
                const parentZone = item.closest('.droppable-zone'); // This logic is flawed, item is already moved
                                                                    // We need to check the zone it came from
            }
        }
        
        document.querySelectorAll('.droppable-zone').forEach(zone => {
            if (zone.children.length === 0 || (zone.children.length === 1 && zone.children[0].classList.contains('drop-hint'))) {
                if (!zone.querySelector('.drop-hint')) {
                    const hint = document.createElement('small');
                    hint.className = 'text-muted drop-hint';
                    hint.textContent = 'Drag available numbers here';
                    zone.appendChild(hint);
                }
            }
        });


        if (addPurposeBtn) {
            addPurposeBtn.addEventListener('click', () => {
                if (emptyPurposeTemplate) {
                    const newPurposeSection = emptyPurposeTemplate.content.cloneNode(true).firstElementChild;
                    purposeContainer.appendChild(newPurposeSection);
                    initDropZones(); // Initialize drop zone on the new section
                    newPurposeSection.querySelector('.remove-purpose-btn').addEventListener('click', function() {
                        // Move items back before removing section
                        const itemsInZone = this.closest('.purpose-section').querySelectorAll('.in-purpose');
                        itemsInZone.forEach(item => returnToAvailableList(item));
                        this.closest('.purpose-section').remove();
                        updateFormDataAndButtonState();
                    });
                    updateFormDataAndButtonState(); // Check button state after adding
                }
            });
        }

        purposeContainer.addEventListener('input', (e) => {
            if (e.target.classList.contains('purpose-input')) {
                updateFormDataAndButtonState();
            }
        });
        
        function updateFormDataAndButtonState() {
            const purposes = [];
            const selectedSnrsForForm = new Set();
            let allPurposeSectionsValid = true;
            let hasAtLeastOnePurposeWithNumber = false;

            document.querySelectorAll('#purposeContainer .purpose-section').forEach(section => {
                const purposeText = section.querySelector('.purpose-input').value.trim();
                const numbersInPurpose = Array.from(section.querySelectorAll('.in-purpose'))
                                           .map(item => item.dataset.snr);

                if (numbersInPurpose.length > 0) { // Only consider sections with numbers for validation
                    if (!purposeText) {
                        allPurposeSectionsValid = false; // Invalid if numbers present but no purpose text
                    }
                    purposes.push({ purpose: purposeText || "Untitled Purpose", snrs: numbersInPurpose });
                    numbersInPurpose.forEach(snr => selectedSnrsForForm.add(snr));
                    hasAtLeastOnePurposeWithNumber = true;
                } else if (purposeText) { 
                    // If there's purpose text but no numbers, it's not a "complete" purpose for submission
                    // but also doesn't invalidate other complete sections unless it's the *only* section.
                    // For the apply button, we care if there's at least one valid purpose with numbers.
                }
            });
            
            const applyButton = document.querySelector('#applyMultipleForm button[type="submit"]');
            if (applyButton) {
                // Enable apply button if there's at least one purpose section with numbers AND that section has purpose text
                applyButton.disabled = !(hasAtLeastOnePurposeWithNumber && allPurposeSectionsValid);
            }


            document.getElementById('selectedSnrs').value = Array.from(selectedSnrsForForm).join(',');
            document.getElementById('purposesData').value = JSON.stringify(purposes);
        }

        // Initial setup
        initDraggables();
        initDropZones();
        updateFormDataAndButtonState(); // Initial check for the apply button
        
        // Bootstrap file input label update
        document.querySelectorAll('.custom-file-input').forEach(input => {
            input.addEventListener('change', function(e) {
                let fileName = e.target.files[0] ? e.target.files[0].name : "Choose file...";
                let label = this.nextElementSibling;
                if (label) { // Make sure label exists
                    label.classList.add("selected");
                    label.innerHTML = fileName;
                }
            });
        });

         // Handle form submission for apply_multiple_preview
        if (applyMultipleForm) {
            applyMultipleForm.addEventListener('submit', function(event) {
                updateFormDataAndButtonState(); // Ensure data is current
                const purposesData = document.getElementById('purposesData').value;
                if (purposesData === "[]" || !document.getElementById('selectedSnrs').value) {
                    alert("Please assign at least one number to a purpose and describe the purpose.");
                    event.preventDefault();
                }
            });
        }
    });
</script>
<style>
    /* (Your existing styles for result-card, result-list, etc.) */
    .result-item.draggable-item { cursor: grab; }
    .result-item.dragging { opacity: 0.5; border: 1px dashed #007bff; }
    .droppable-zone { min-height: 80px; border: 2px dashed #ccc; border-radius: 0.25rem; padding: 10px; margin-top: 5px; display: flex; flex-direction: column; align-items: center; justify-content: center;}
    .droppable-zone.drag-over { border-color: #007bff; background-color: #e9f5ff; }
    .result-item.in-purpose { background-color: #e9f5ff; border-color: #b8daff !important; cursor: default; }
    .purpose-section { border: 1px solid #eee; padding: 15px; border-radius: 5px; background-color: #fcfcfc; }
    .purpose-section + .purpose-section { margin-top: 15px; }
    .drop-hint {display: block; text-align: center; width: 100%;}
    .remove-item-btn { padding: 0.1rem 0.3rem; line-height: 1; }
</style>
{% endblock %}