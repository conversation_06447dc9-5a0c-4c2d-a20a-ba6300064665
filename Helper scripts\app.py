# app.py



from http.client import HTTPException
import os
import re
import base64
from statistics import mean
import requests
from flask import Flask, Blueprint, jsonify, render_template, make_response, redirect, send_file, send_from_directory, url_for, flash, request, session

# from . import mongo
from pymongo import ASCENDING, DESCENDING, MongoClient

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, inch
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Image, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from reportlab.lib.colors import black, grey,HexColor


from reportlab.platypus import PageTemplate, BaseDocTemplate, Frame

from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont  # Import the TTFont class
# Try to register Helvetica-Oblique (a common italic variant of Helvetica)
try:
    pdfmetrics.registerFont(TTFont('Helvetica-Oblique', 'HelveticaOblique.ttf')) # Replace 'HelveticaOblique.ttf' with the actual filename if different and if you have this font file.
    italic_font_name = 'Helvetica-Oblique' # Use this name if registration is successful
except Exception as e:
    print(f"Error registering Helvetica-Oblique: {e}. Using Helvetica for italic style as fallback.")
    italic_font_name = 'Helvetica' # Fallback to basic Helvetica if italic variant registration fails

import qrcode
from io import BytesIO

from datetime import datetime, timedelta, timezone

from flask_pymongo import PyMongo
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, SubmitField, FileField, TextAreaField, BooleanField
from functools import wraps
from wtforms.validators import DataRequired, Email, EqualTo, Length
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user, AnonymousUserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from bson.objectid import ObjectId
import openai
from dotenv import load_dotenv
import json
import pandas as pd
from math import ceil
from flask_cors import CORS

from bson.json_util import dumps
import plotly.express as px
import plotly.graph_objs as go
from typing import Dict, List, Union
from dateutil import parser

import tempfile
import subprocess
# Add to imports
from celery import Celery

from flask_caching import Cache


from snr_tracker import SNRTracker



cache = Cache(config={'CACHE_TYPE': 'SimpleCache'})



openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))



app = Flask(__name__)
app.config['SECRET_KEY'] = b'_5#y2L"F4Q8z\n\xec'

app.config["MONGO_URI"] ="mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"

app.config['UPLOAD_FOLDER'] = 'static\\uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB limit

# Add to app config
# app.config['BETTER_OCR_CONFIG'] = {
#     'text_enhance_strength': 1.5,
#     'background_removal_threshold': 240,
#     'contrast_boost': True,
#     'dpi': 300
# }

# Create temp directory if not exists
os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'tmp'), exist_ok=True)

mongo = PyMongo(app)
mongo.cx = MongoClient(app.config["MONGO_URI"])  # Explicit client creation
CORS(app)
cache.init_app(app)


# To be initialized after MongoDB connection is established
snr_tracker = SNRTracker(mongo)

# Register the blueprint




class Anonymous(AnonymousUserMixin):
    def __init__(self):
        self.id = None
        self.contact = 'Guest'
        self.email = ''
        self.company = ''






login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = "Please log in to access this page."
login_manager.login_message_category = "warning"
login_manager.anonymous_user = Anonymous  # Set the custom anonymous user






def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Example usage of the decorator
@app.route('/admin/dashboard')
@login_required
@admin_required
def admin_dashboard():
    # Admin-specific logic
    return render_template('admin_review_applications.html')



class User(UserMixin):
    def __init__(self, user_data):
        self.id = str(user_data['_id'])
        self.is_admin = user_data.get('is_admin', False)
        self.email = user_data.get('email', '')
        
        if not self.is_admin:  # Regular user fields
            self.vas_id = user_data.get('vas_id')
            if self.vas_id is None:
                raise ValueError("vas_id is required for non-admin users.")
            self.contact = user_data.get('contact', '')
            self.phone = user_data.get('phone', '')
            self.national_id = user_data.get('national_id', None)
            self.photo = user_data.get('photo', None)
            self.company = user_data.get('vas_company', '')
        else:  # Admin user-specific fields
            self.contact = user_data.get('contact', 'Admin')  # Use a default or custom username for admin
            # self.email = user_data.get('email', '')



@login_manager.user_loader
def load_user(user_id):
    user = mongo.db.users.find_one({'_id': ObjectId(user_id)})
    if user:
        return User(user)
    return None




# forms.py (You can create a separate file if preferred)
class RegistrationForm(FlaskForm):
    vas_id = StringField('VAS ID', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    contact = StringField('Contact Name', validators=[DataRequired()])
    phone = StringField('Phone Number', validators=[DataRequired()])
    national_id = FileField('National ID', validators=[DataRequired()])
    photo = FileField('Photo', validators=[DataRequired()])
    submit = SubmitField('Register')


class LoginForm(FlaskForm):
    # email = StringField('Email', validators=[DataRequired(), Email(message="Invalid email address")])
    username = StringField('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')
    remember = BooleanField('Remember Me')







ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf'}

def allowed_file(filename):
    return ('.' in filename and 
            filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS
            and not any(char in filename for char in {'/', '\\', ':'}))  # Prevent path traversal






def extract_telecom_data(ocr_text: str) -> List[Dict]:
    """
    Extracts telecom numbers and their contextual purposes from OCR text
    with validation and type classification.

    Args:
        ocr_text: Raw text output from OCR processing

    Returns:
        List of dictionaries with parsed numbers and metadata
        Example:
        [{
            'raw_number': '*5723#',
            'clean_number': '5723',
            'type': 'shortcode',
            'valid': True,
            'purpose': 'Provide access to resources, support services, and inspirational messages.',
            'context': 'Page 1, Section 2'
        }]
    """
    # Preprocess text for better pattern matching
    text = ocr_text.replace('\n', ' ')  # Remove newlines
    text = re.sub(r'\s+', ' ', text)    # Collapse multiple spaces

    # Patterns to identify the fields for preferred, first alternative, and second alternative numbers
    number_fields = [
        r'(?:Preferred SNR Requested)[:\s]*([\*#\d\s-]{3,8})',
        r'(?:First Alternative SNR Requested)[:\s]*([\*#\d\s-]{3,8})',
        r'(?:Second Alternative SNR Requested)[:\s]*([\*#\d\s-]{3,8})'
    ]

    # Telecom number patterns with contextual awareness
    patterns = {
        'shortcode': {
            'validation': r'^[\*\d-]{3,6}$',
            'format': lambda x: re.sub(r'\D', '', x)[:6]
        },
        'tollfree': {
            'validation': r'^0?800\d{6,7}$',
            'format': lambda x: '0800' + re.sub(r'\D', '', x)[-6:]
        },
        'premium': {
            'validation': r'^0?900\d{6,7}$',
            'format': lambda x: '0900' + re.sub(r'\D', '', x)[-6:]
        }
    }

    # Purpose extraction pattern
    purpose_pattern = r'(?:Program Name|Program Description)[:\s]*(.+?)(?:\.|$)'

    results = []

    # Find all potential number matches with context
    for field_pattern in number_fields:
        for match in re.finditer(field_pattern, text, re.IGNORECASE):
            raw_number = match.group(1).strip()

            # Determine the type of number
            num_type = None
            clean_number = None
            is_valid = False

            for type_name, config in patterns.items():
                formatted_number = config['format'](raw_number)
                if re.match(config['validation'], formatted_number):
                    num_type = type_name
                    clean_number = formatted_number
                    is_valid = True
                    break

            if num_type:
                # Find nearest purpose within 200 characters
                purpose_search = re.search(
                    purpose_pattern,
                    text[match.end():match.end()+200],
                    re.IGNORECASE
                )
                purpose = purpose_search.group(1).strip() if purpose_search else 'Not specified'

                # Context capture for auditing
                start = max(0, match.start()-50)
                end = min(len(text), match.end()+50)
                context = text[start:end].strip()

                results.append({
                    'raw_number': raw_number,
                    'clean_number': clean_number,
                    'type': num_type,
                    'valid': is_valid,
                    'purpose': purpose,
                    'context': f"...{context}..."  # Ellipsis show text truncation
                })

    return results



applications_schema = {

  "applicant_name": "String",       # Name of the applicant 
  "application_date": "String",     # Date of the application in ISO format (YYYY-MM-DD) 

  "status": "String",              # Status of the application (e.g., "Certificate_printable")
  "remarks": "String",              # Remarks or notes about the application (optional) 
  "created_at": {
    "$date": "Timestamp" # Timestamp of when the application was created in ISO format (YYYY-MM-DDTHH:MM:SS.sssZ) \
  },
  "updated_at": {
    "$date": "Timestamp" # Timestamp of when the application was last updated in ISO format (YYYY-MM-DDTHH:MM:SS.sssZ) \
  },
  "certificate_expiry_date": "String",   # Expiry date of the certificate in MM/DD/YY format
  "certificate_generated": "Boolean",    # Indicates whether the certificate has been generated (true or false)
  "certificate_start_date": "String", # Start date of the certificate in MM/DD/YY format
  "processed_by": "String"          # Name of the person who processed the application
}

snr_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                  # Shortcode Special Numbering Resource (SNR) from 100 to 699999
  "STATUS": "String",              # Status of the SNR ( one of the following, "AVAILABLE, EXPIRED, ASSIGNED OR RESERVED". Note that all status in this collection are in Upper Case )
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or "" if not applicable
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or "" if not assigned
}

tf_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                  # Tollfree Special Numbering Resource (SNR) from 800000000 to 800999999. The preceding'0' before each number is omitted here but appended during processing."
  "STATUS": "String",              # Status of the SNR ( one of the following, "AVAILABLE, EXPIRED, ASSIGNED OR RESERVED" ). Note that all status in this collection are in Upper Case
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or "" if not applicable
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or "" if not assigned
}

pr_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                 # Premium-rate Special Numbering Resource (SNR) from 900000000 to 900999999. The preceding'0' before each number is omitted here but appended during processing."
  "STATUS": "String",              # Status of the SNR ( one of the following, "AVAILABLE, EXPIRED, ASSIGNED OR RESERVED" ). Note that all status in this collection are in Upper Case
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or "" if not applicable
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or "" if not assigned
}

snrs_schema = {

  "application_id": "String",     # Unique identifier for the application. Documents are aggregated based on this field
  "snr": "String",                 # Special Numbering Resource (SNR) i.e. shortcodes from 100 to 699999
  "purpose": "String",             # Purpose or description of usage of the SNR
  "status": "String",              # Status of the SNR (e.g., "Certificate_printable", Approved", or "Pending Review"
  "type": "String",                # Type of the SNR (e.g., "Shortcode", "Toll-free", or "Premium-rate")
  "certificate_id": "String",     # Unique identifier for the certificate associated with the SNR
}


# Helper Function: Determine Collections to Search Programmatically
def determine_collections(query):
    """
    Determines the MongoDB collections to search based on the user's query.
    Returns a list of collection names.
    """
    # Convert query to lowercase for easier keyword searching
    query = query.lower()
    collections = ['applications']

    # Keywords for determining collections
    if re.search(r'\bshortcode\b|\b3 digit\b|\b4 digit\b|\b5 digit\b|\b6 digit\b|^[0-9]{3,6}$', query):
        collections.append('snr_status')

    if re.search(r'\btoll-?free\b|\b0800\b|^0800[0-9]{6}$', query):
        collections.append('tf_status')

    if re.search(r'\bpremium-?rate\b|\b0900\b|^0900[0-9]{6}$', query):
        collections.append('pr_status')

    # If the query contains general terms like "application", "history", "details", or an applicant's name
    if re.search(r'\bapplication\b|\bhistory\b|\bapplicant\b|\bdetails\b|^[0-9]{3,6}$', query):
        collections.append('applications')

    # If none of the specific collections are found, default to 'applications' for a general query
    if not collections:
        collections.append('applications')

    # Include 'snrs' collection to ensure completeness of SNR data
    collections.append('snrs')

    return list(set(collections))  # Remove duplicates and return the list of collections to search

# Helper Function: Aggregate Applications and SNRs
def aggregate_applications_and_snrs():
    """
    Aggregates data from the 'applications' and 'snrs' collections.
    Returns a list of merged application and SNR data.
    """
    # Perform aggregation between applications and snrs
    try:
        pipeline = [
            {
                "$lookup": {
                    "from": "snrs",
                    "localField": "application_id",
                    "foreignField": "application_id",
                    "as": "snr_data"
                }
            },
            {
                "$unwind": {
                    "path": "$snr_data",
                    "preserveNullAndEmptyArrays": True
                }
            }
        ]
        
        # Execute the aggregation pipeline
        aggregated_data = list(mongo.db.applications.aggregate(pipeline))
        return aggregated_data

    except Exception as e:
        print(f"Error aggregating applications and snrs: {e}")
        return []


# Helper Function: Create MongoDB Queries with GPT-4o-mini
def create_mongodb_queries_with_gpt(query, collections):
    """
    Sends the user query and collections to GPT-4o-mini to generate MongoDB queries.
    Returns a list of MongoDB query dictionaries.
    """
    prompt = f"""
    You are an assistant helping to create MongoDB queries based on the user's search intent. First, explain your thought process clearly based on the user’s query. Then generate the MongoDB query based on that explanation.
    - The 'SNR' field is stored as a string in the database.
    - If the user's query involves numeric ranges, you should use a regex query to ensure proper results for string-based values.
    - Generate MongoDB queries that accurately reflect the user's search while handling type mismatches appropriately.

    Collections to be queried:
    - 'snr_status' (shortcodes, including fields: SNR, STATUS, EXPIRY, ASSIGNEE). The SNRs in this collection are 3, 4, 5, and 6-digit shortcodes. Here is the schema for this collection: {snr_status_schema}. Please note that the options for the 'STATUS' field are all in uppercase.
    - 'tf_status' (toll-free numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE). Here's the schema for this collection: {tf_status_schema}.
    - 'pr_status' (premium-rate numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE). Here's the chema for this collection: {pr_status_schema}.
    - 'applications' (application history, including fields: applicant_id, user_id, applicant_name, application_date, status, remarks, created_at, updated_at, certificate_expiry_date, certificate_generated, certificate_start_date, processed_by). Here's the schema for this collection: {applications_schema}.
    - 'snrs' (SNR-specific data, including fields: application_id, snr, type, purpose, status, certificate_id, created_at, updated_at). Hers's the schema for this collection: {snrs_schema}.

    User Query: "{query}"
    Collections: {collections}

    Based on the user's query, provide the MongoDB queries for each relevant collection.

    Your response should only be in JSON format:
    {{
        "queries": {{
            "snr_status": <MongoDB query for snr_status>,
            "tf_status": <MongoDB query for tf_status>,
            "pr_status": <MongoDB query for pr_status>,
            "applications": <MongoDB query for applications>,
            "snrs": <MongoDB query for snrs>
        }},
        "limit": <Maximum number of results to return based on the query or 10 if not specified in the query>
    }}

    - If a query involves numeric ranges for SNR but the SNR field is a string, use a regex pattern to reflect the range.
    - If a collection is not relevant to the query, set its value to `null`.
    
    Explain your reasoning and formulate the appropriate query.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",  # Use GPT-4o-mini API for the task
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,  # For deterministic output
            response_format={"type": "json_object"}
        )

        # Extract the assistant's reply
        reply = response.choices[0].message.content.strip()
        print('reply', reply)  # Print the reply for debugging purposes
        # Parse the JSON output
        query_data = json.loads(reply)

        return query_data

    except Exception as e:
        print(f"Error creating MongoDB queries with GPT: {e}")
        return {}


# Iterative Feedback Loop for Query Refinement
def refine_query_with_feedback(initial_query, collections):
    """
    Uses an iterative feedback loop to refine the generated query if the initial one returns no results.
    """
    response = create_mongodb_queries_with_gpt(initial_query, collections)
    query_data = response.get('queries', {})

    # Execute the MongoDB query
    results = execute_mongo_query(query_data)
    if not results:
        # If no results, refine the query with additional context
        refined_prompt = f"""
        
        The initial MongoDB query returned no results or incorrect results.
        Please refine the query based on the fact that the 'SNR' field is stored as a string, and the numeric range must be handled accordingly. 
        First, explain your thought process clearly based on the user’s query. Then generate the MongoDB query based on that explanation.
        User Query: "{initial_query}"
        Collections: {collections}
        Explain your reasoning and formulate the appropriate query.
        """
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",  # Use GPT-4o-mini API for the task
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": refined_prompt}
            ],
            temperature=0,  # For deterministic output
            response_format={"type": "json_object"}
        )

        # Extract the assistant's reply
        reply = response.choices[0].message.content.strip()
        print('refined reply', reply)  # Print the refined reply for debugging purposes
        query_data = json.loads(reply)

    return query_data

# Helper Function: Execute MongoDB Query
def execute_mongo_query(query_data):
    """
    Executes the provided MongoDB query data.
    """
    results = []
    # Iterate through the collections and execute queries
    for collection, query in query_data.items():
        if query:
            try:
                collection_ref = mongo.db[collection]
                result = list(collection_ref.find(query).limit(query_data.get('limit', 10)))
                results.extend(result)
            except Exception as e:
                print(f"Error executing query on collection {collection}: {e}")
                # Improved error handling
                log_activity("Query Execution Error", f"Failed to execute query on collection {collection}: {str(e)}")
    return results


def create_results_with_gpt(search_results):

    """Sends the search results to GPT-4o-mini to create descriptions."""
    def serialize_dates(obj):
        """Recursively convert datetime objects to ISO strings"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: serialize_dates(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [serialize_dates(item) for item in obj]
        elif isinstance(obj, ObjectId):
            return str(obj)
        return obj
    

    prompt = f"""
    You are an assistant helping to create search result descriptions based on the search results.

    Given the following search results, if the results given to you are of the same subject e.g. the same SNR or the same applicant, then compare carefully and summarize the results into one brief summary or a timeline of summaries. if the results do not share a common SNR or Assignee, then generate a descriptive summary for each entry individually. If the results are empty, return an empty list.
    

    The summary/summaries should be in a format that is easy to understand and read, and should include key information such as number or Special Numbering Resource (SNRs), status, expiry, and assignee where available.
    All references to 'applications' are applications for Special Numbering Resource (SNRs) such as short codes for USSD and SMS mobile applications, and toll-free and premium-rate numbers for toll free voice and premium rate voice applications.

    Search Results: {json.dumps(serialize_dates(search_results))}

    Your response must be in JSON format without any extraneous words or additional explanations.
    It should strictly be a JSON array where each element is an object with the following keys:
    - "snr":SNR, the Special Numbering Resource (SNR) number i.e. the shortcode, toll-free number or premium-rate number. If the SNR is not available, this field should be "N/A".
    - "description": A descriptive summary for the SNR or the application.
    - "availability": Status of availability. compare the status of the SNR in one of the following :['snr_status', 'tf_status', and 'pr_status']  and 'app' collections to determine the availability. check the 'cert_expiry' field in the 'app' collection to determine if the SNR is expired. If the SNR is available, this field should be "Available". If the SNR is not available, this field should be "Not Available". If the availability status is not clear from the data, this field should be "Unknown".
    - "applicant_info": An object containing:
        - "status": The current status of the application (e.g., "Approved", "Pending").
        - "expiry": The expiry date of the SNR.
        - "assignee": The name of the assignee.

    """

    # try:
    response = openai_client.chat.completions.create(
        model="gpt-4o-mini",  # Use GPT-4o-mini API for the task
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ],
        temperature=0,  # For deterministic output
        response_format={"type": "json_object"}
    )

    # Extract the assistant's reply
    reply = response.choices[0].message.content.strip()
    print('formatted search results', reply)  # Print the reply for debugging purposes
    # Parse the JSON output
    
    results = json.loads(reply)

    return results


def merge_and_remove_duplicates(applications_data, snrs_data):
    """
    Merges data from the 'applications' and 'snrs' collections, removing duplicates based on 'application_id'.
    """
    merged_data = {}

    # Merge applications data
    for app in applications_data:
        app_id = app.get('application_id')
        if app_id not in merged_data:
            merged_data[app_id] = app

    # Merge SNRs data
    for snr in snrs_data:
        app_id = snr.get('application_id')
        if app_id not in merged_data:
            merged_data[app_id] = snr
        else:
            # Merge the SNR-specific data into the application data
            if 'snr_data' in merged_data[app_id]:
                merged_data[app_id]['snr_data'].append(snr)
            else:
                merged_data[app_id]['snr_data'] = [snr]

    # Convert merged data to a list
    return list(merged_data.values())


def perform_search_operations(query, collections):
    """Shared search processing logic used by both text and OCR searches"""
    mongodb_queries_full = create_mongodb_queries_with_gpt(query, collections)
    mongodb_queries = mongodb_queries_full.get('queries', {})
    limits = mongodb_queries_full.get('limit', 10)
    
    search_results = []
    for collection_name, mongo_query in mongodb_queries.items():
        if mongo_query:
            collection = mongo.db[collection_name]
            search_results += list(collection.find(mongo_query).limit(limits))

    # Convert and process results
    for result in search_results:
        result['_id'] = str(result['_id'])
        
    search_results = create_results_with_gpt(search_results)
    return pd.DataFrame(search_results).to_dict(orient='records')



def generate_report_prose_with_gpt(data: Union[pd.DataFrame, list], report_type: str = "General"):

    # Custom JSON serializer for DataFrame elements
    def custom_serializer(obj):
        if isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        raise TypeError(f"Type {type(obj)} not serializable")

    # Convert data to JSON-serializable format
    if isinstance(data, pd.DataFrame):
        # Use DataFrame's built-in JSON conversion with proper date handling
        json_data = data.to_json(orient='records', date_format='iso')
    else:
        # Handle list input with custom serialization
        json_data = json.dumps(data, indent=2, default=custom_serializer)

    # Prepare a prompt with a description of the data
    prompt = f"""
    You are an assistant that creates detailed natural language summaries of data for a report.
    Here is the data for the report. It represents a {report_type}:

    Data: {json_data}

    Please provide an executive summary for this data. Your response should highlight key statistics, trends, and observations, 
    such as the number of assigned versus available SNRs, the total fees collected, the number of applications by category, 
    and any notable observations or anomalies. The summary should be written in a professional tone suitable for a formal report.

    Your response must be in JSON format without any extraneous words or additional explanations.
    It should strictly be a JSON array where each element is an object with the following keys:
    - "report": A detailed natural language summary of the data for the report, capturing an overview, statistics, and key findings.
    - "statistics": An object containing key statistical data, including:
        - "total_applications": Total number of applications processed.
        - "assigned_snr": Number of SNRs that have been assigned.
        - "total_fees_collected": Total amount of fees collected.
        - "applications_by_category": An object that further breaks down the applications by category (e.g., "Tollfree", "Shortcode").
    - "observations": An array of any notable observations or trends identified in the data. Each observation should be a brief natural language description.

    Ensure the output matches this structure strictly.
    """

    # Rest of the function remains the same...
    # Call GPT-4o-mini to generate the prose
    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",  # Use GPT-4o-mini API for the task
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,  # For deterministic output
            response_format={"type": "json_object"}
        )

        # Extract the assistant's reply
        reply = response.choices[0].message.content.strip()

        # Parse the reply as JSON
        results = json.loads(reply)

        # Ensure summary is a list if needed (assuming front-end expects a list)
        if not isinstance(results, list):
            results = [results]

        print('GPT Reply:', results)  # Debugging purpose
        return results

    except Exception as e:
        print(f"Error generating report prose with GPT: {e}")
        return {"error": "Error generating report prose. Please try again later."}

@app.before_request
def make_session_permanent():
    session.permanent = True
    app.permanent_session_lifetime = timedelta(minutes=60)


@app.route('/application/cleanup', methods=['POST'])
def cleanup_applications():
    session.pop('pending_application', None)
    return '', 204

@app.route('/register', methods=['GET', 'POST'])
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        vas_id = form.vas_id.data.strip()
        # Cross-check vas_id with MongoDB
        vas_entry = mongo.db.vas_table.find_one({'ID': vas_id})
        if not vas_entry:
            flash('Invalid VAS ID.')
            return redirect(url_for('register'))

        # Check if email already exists
        existing_user = mongo.db.users.find_one({'email': form.email.data})
        if existing_user:
            flash('Email already registered.')
            return redirect(url_for('register'))

        # Handle file uploads
        national_id_file = form.national_id.data
        photo_file = form.photo.data

        if not (allowed_file(national_id_file.filename) and allowed_file(photo_file.filename)):
            flash('Invalid file format.')
            return redirect(url_for('register'))

        national_id_filename = secure_filename(national_id_file.filename)
        photo_filename = secure_filename(photo_file.filename)

        # Save files
        national_id_path = os.path.join(app.config['UPLOAD_FOLDER'], 'national_ids', national_id_filename)
        photo_path = os.path.join(app.config['UPLOAD_FOLDER'], 'photos', photo_filename)

        national_id_file.save(national_id_path)
        photo_file.save(photo_path)

        # Hash the password
        hashed_password = generate_password_hash(form.password.data)

        # Create user document
        user_data = {
            'vas_id': vas_id,
            'email': form.email.data,
            'password': hashed_password,
            'vas_company': vas_entry['VASP'],
            'contact': form.contact.data,
            'created_timestamp': datetime.now(timezone.utc),
            'national_id': national_id_path,
            'photo': photo_path,
            'phone': form.phone.data
        }

        # Insert into MongoDB
        mongo.db.users.insert_one(user_data)
        flash('Registration successful.')
        return redirect(url_for('search'))

    return render_template('register.html', form=form)

@app.route('/update_account', methods=['GET', 'POST'])
@login_required
def update_account():
    if request.method == 'POST':
        # Update user details
        email = request.form['email']
        phone = request.form['phone']

        # Update in MongoDB (we ensure we don't modify the vas_company by mistake)
        mongo.db.users.update_one(
            {'_id': ObjectId(current_user.id)},
            {'$set': {'email': email, 'phone': phone}}
        )
        flash('Account updated successfully.', 'success')
        return redirect(url_for('update_account'))

    # Fetch current user data for populating the form
    user_data = mongo.db.users.find_one({'_id': ObjectId(current_user.id)})
    return render_template('update_account.html', user_data=user_data)

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('search'))

    form = LoginForm()
    if form.validate_on_submit():
        username = form.username.data.strip()
        password = form.password.data.strip()

        # Fetch user from database
        user_data = mongo.db.users.find_one({'contact': username})
        if user_data and check_password_hash(user_data['password'], password):
            user = User(user_data)
            login_user(user, remember=form.remember.data)

                        # Log user login activity
            log_activity("User Login", f"User {username} logged in.")

            flash('Logged in successfully.', 'success')
            next_page = request.args.get('next')
            return redirect(next_page) if next_page else redirect(url_for('search'))
        else:
            flash('Invalid username or password.', 'danger')
            return redirect(url_for('login'))

    return render_template('login.html', form=form)


# Route: Search

@app.route('/', methods=['GET', 'POST'])
def search():
    if request.method == 'POST':
        query = request.form['query'].strip()
        if not query:
            flash('Please enter a search query.', 'warning')
            return redirect(url_for('search'))

                    # Log user login activity
        log_activity("User Search", f"User {current_user.contact} performed a search.")
        # Determine the collections and generate MongoDB queries (existing functionality)
        collections = determine_collections(query)

        print(f"Determined collections: {collections} for query: {query}")  # Debugging statement


        # Create MongoDB queries using GPT-4o-mini
        modified_search = perform_search_operations(query, collections)


        session['search_query'] = query
        session['search_results'] = modified_search

        #lets have a look at the final results
        print('final results', modified_search)


        if not modified_search:
            flash('No results found for your query.', 'info')
            return render_template('search.html', results=[], query=query)

        # Convert search results to a list of dictionaries for rendering
        
        return render_template('search.html', results=modified_search, query=query)

    # If the request method is GET, check if user is returning from review page
    log_activity("User Search", f"User {current_user.contact} visited the search page.")
    from_review = request.args.get('from_review')
    print('coming from the review page', from_review)  # Print the value for debugging purposes
    print('Request URL:', request.url)  # Debugging statement to check the URL
    print('Request Args:', request.args)  # Debugging statement to print all query parameters

    if from_review and 'search_query' in session and 'search_results' in session:
        # User is returning from the review page, use session data
        saved_query = session.get('search_query')
        saved_results = session.get('search_results')
        # modified_search = pd.DataFrame(saved_results).to_dict(orient='records')
        print('Saved results:', saved_results)  # Print saved results for debugging
        log_activity("User Search", f"User {current_user.contact} returned to the search page.")

        if not isinstance(saved_results, list) or not saved_results:
            flash('There was an error loading your previous results. Please try again.', 'danger')
            return redirect(url_for('search'))

        return render_template('search.html', results=saved_results, query=saved_query)

    # Otherwise, clear session data and render an empty search page
    session.pop('search_query', None)
    session.pop('search_results', None)
    return render_template('search.html')





def create_summary_with_gpt(search_results):
    """
    Sends the application details to GPT-4o-mini to create a summary for upper management approval.
    Returns a dictionary with the summary.
    """
    prompt = f"""
    You are an assistant helping to create detailed summaries of Special Numbering Resource(SNR) Applications. 
    Your task is to analyze the provided data and create a summary that highlights the key information and reasons for approval. 
    The summary should be clear, concise, and professional, and it should be suitable for presentation to upper management. 
    Here are the details of the SNR Application: \{search_results}. 
    
    Your response must be in JSON format without any extraneous words or additional explanations.

    
    """


    # try:
    response = openai_client.chat.completions.create(
        model="gpt-4o-mini",  # Use GPT-4o-mini API for the task
        messages=[
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": prompt}
        ],
        temperature=0,  # For deterministic output
        response_format={"type": "json_object"}
    )

    # Extract the assistant's reply
    reply = response.choices[0].message.content.strip()
    print('summary', reply)  # Print the reply for debugging purposes
    # Parse the JSON output
    results = json.loads(reply)

    return results

    # except Exception as e:
    #     print(f"Error creating results with GPT: {e}")
    #     return []

@app.route('/api/search', methods=['GET'])
def search_snr():
    query = request.args.get('query')
    search_results = mongo.db.applications.aggregate([
        {"$unwind": "$snrs"},
        {"$match": {"$or": [
            {"snrs.snr": query},
            {"snrs.certificate_id": query},
            {"applicant_name": {"$regex": query, "$options": "i"}}
        ]}},
        {"$project": {
            "application_id": 1,
            "snrs.snr": 1,
            "snrs.type": 1,
            "snrs.purpose": 1,
            "snrs.status": 1,
            "snrs.certificate_id": 1,
            "status": 1,
            "applicant_name": 1
        }}
    ])
    return jsonify(list(search_results))







######ADMIN REVIEW AND APPROVAL FUNCTIONS

@app.route('/review_applications_status/<application_id>', methods=['POST'])
@admin_required
def review_applications_status(application_id):
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to view Admin dashboard with a non-admin account.")
        return jsonify({'error': 'Permission denied'}), 403

    if request.method == 'POST':
        data = request.get_json()
        new_status = data.get('status')

        if application_id and new_status:
            # Update the status in 'app' collection
            mongo.db.applications.update_one(
                {'application_id': application_id},
                {'$set': {
                    'status': new_status,
                    'updated_at': datetime.now(timezone.utc)
                }}
            )

            # Fetch the application to get related SNRs
            application = mongo.db.applications.find_one({'application_id': application_id})


            if application:
                # Update the status in the corresponding status collection for each SNR
                for snr in application.get('snrs', []):
                    collection_name = get_status_type_table(snr['type'])
                    mongo.db[collection_name].update_one(
                        {'SNR': snr['snr']},
                        {'$set': {
                            'STATUS': new_status,
                            'ASSIGNEE': application.get('applicant_name', '')
                        }}
                    )
            log_activity("Status Update", f"User {current_user.contact} updated status of application {application_id}.")
            return jsonify({'response': 'success'})
        else:
            return jsonify({'error': 'Invalid request'}), 400


# this route shows the details of a single application for the reviewer to review
@app.route('/get_application_details/<application_id>', methods=['GET'])
@login_required
def get_application_details(application_id):
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to view an application's details with a non-admin account.")
        return jsonify({'error': 'Unauthorized access'}), 403

    application = mongo.db.applications.find_one({'application_id': application_id})
    snr_details = list(mongo.db.snrs.find({'application_id': application_id}))

    print('these are the snr details',snr_details)

    if not application and snr_details:
        return jsonify({'error': 'Application not found'}), 404


    log_activity("Applications Details View", f"User {current_user.contact} viewed details of applicaton {application_id}.")
    # Render the details as HTML
    details_html = f"""
    <h4>Applicant Information</h4>
    <p><strong>Applicant Name:</strong> {application.get("applicant_name", "")}</p>
    <p><strong>Email:</strong> {application.get("email", "")}</p>
    <p><strong>Phone:</strong> {application.get("phone", "")}</p>
    <p><strong>Company:</strong> {application.get("company", "")}</p>
    <p><strong>Application Date:</strong> {application.get("application_date", "")}</p>
    
    <h4>Numbers and Purposes</h4>
    {''.join(f'''
    <div class="card mt-3">
        <div class="card-body">
            <h5><strong>SNR:</strong> {snr_details.get("snr", "")}</h5>
            <p><strong>Type:</strong> {snr_details.get("type", "")}</p>
            <p><strong>Purpose:</strong> {snr_details.get("purpose", "")}</p>
            <p><strong>Status:</strong> {snr_details.get("status", "")}</p>
            <p><strong>Certificate ID:</strong> {snr_details.get("certificate_id", "")}</p>
        </div>
    </div>
    ''' for snr_details in list(snr_details))}
    """
    
    footer_html = f"""
    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
    {'<a href="' + url_for('generate_certificate_form', application_id=application_id) + '" class="btn btn-primary">Generate Certificate</a>' if application.get("status") == 'Approved' else ''}
    <a href="{url_for('edit_application', application_id=application_id)}" class="btn btn-warning">Edit Application</a>
    """
    print(snr_details)

    return jsonify({'html': details_html, 'footer_html': footer_html})


# Optimized get_applications route
@app.route('/get_applications', methods=['GET'])
@login_required
@cache.cached(timeout=60, query_string=True)
def get_applications():
    if not current_user.is_admin:
        return jsonify({'error': 'Unauthorized'}), 403

    # Parse parameters
    draw = int(request.args.get('draw', 1))
    start = int(request.args.get('start', 0))
    length = int(request.args.get('length', 10))
    search_value = request.args.get('search[value]', '').strip()
    status = request.args.get('status')
    date_from = request.args.get('date_from')
    date_to = request.args.get('date_to')

    # Build base query
    query_filter = {}

    # Add text search
    if search_value:
        query_filter['$text'] = {'$search': search_value}

    # Add status filter (only if not "All")
    if status and status != "All":
        query_filter['status'] = status

    # Handle date filters
    if date_from or date_to:
        date_filter = {}
        if date_from:
            date_filter['$gte'] = date_from
        if date_to:
            date_filter['$lte'] = date_to
        query_filter['application_date'] = date_filter

    print('Final query filter:', query_filter)  # Debugging


    # Get counts
    records_total = mongo.db.applications.estimated_document_count()
    records_filtered = mongo.db.applications.count_documents(query_filter)

    # Get paginated results
    applications = mongo.db.applications.find(
        query_filter,
        {'_id': 0, 'application_id': 1, 'applicant_name': 1, 'status': 1, 'application_date': 1}
    ).sort('application_date', DESCENDING).skip(start).limit(length)


    # Format data
    data = []
    for app in applications:
        data.append([
            app['application_id'],
            app['applicant_name'],
            app['status'],
            app['application_date'],
            f'<button class="btn btn-sm btn-info details-button" data-id="{app["application_id"]}">View</button>'
        ])

    return jsonify({
        "draw": draw,
        "recordsTotal": records_total,
        "recordsFiltered": records_filtered,
        "data": data
    })




# Route to review applications with a join between 'applications' and 'snrs'
@app.route('/review_applications', methods=['GET', 'POST'])
@admin_required
def review_applications():
    if not current_user.is_admin:
        log_activity(current_user, "Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to view Admin dashboard with a non-admin account.")
        flash("You do not have permission to access this page.", "danger")
        return redirect(url_for('dashboard'))

    # query_filter = {}
    if request.method == 'POST':
        # get today's date
       
        date_today = datetime.strftime(datetime.now().date(), "%Y-%m-%d")


        
        # check if there is an index created today. If not, create one.
        index_check = mongo.db.index_check.find_one({'created_date':date_today})

        if not index_check:
            # Create indexes if they don't exist (run once during setup)
            mongo.db.applications.create_index([("application_id", ASCENDING)])
            mongo.db.applications.create_index([("status", ASCENDING)])
            mongo.db.applications.create_index([("application_date", DESCENDING)])
            mongo.db.applications.create_index([("applicant_name", "text")])
            try:
                index_counter = index_check['index_counter']
                mongo.db.index_check.insert_one({"index_counter": index_counter+1, "created_at": date_today})
            except:
                mongo.db.index_check.insert_one({"index_counter": 1, "created_at": date_today})            


    log_activity("Base Admin Dashboard", f"User {current_user.contact} viewed Base Admin Dashboard.")
    # return render_template('admin_review_applications.html', applications=applications)
    return render_template('admin_review_applications.html')



def get_status_type_table(type):
    """Helper function to determine the appropriate collection name."""
    if type == 'shortcode':
        return 'snr_status'
    elif type == 'tollfree':
        return 'tf_status'
    elif type == 'premium-rate':
        return 'pr_status'
    else:
        return None




@app.route('/update_application/<application_id>', methods=['POST'])
@login_required
def update_application(application_id):
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to update an application with a non-admin account.")
        flash("You do not have permission to perform this action.", "danger")
        return redirect(url_for('dashboard'))
    
    status = request.form.get('status')
    remarks = request.form.get('remarks')

    try:
        mongo.db.applications.update_one(
            {'application_id': application_id},
            {'$set': {
                'status': status,
                'remarks': remarks,
                'updated_at': datetime.now(timezone.utc)
            }}
        )
        log_activity("Application Update", f"User {current_user.contact} successfully updated application {application_id}.")
        flash('Application updated successfully.', 'success')
    except Exception as e:
        flash(f'An error occurred while updating the application: {e}', 'danger')
    
    return redirect(url_for('review_applications'))



def Check_SNR_EXPIRY(snr_number):
    """
    Revised function to check SNR expiry and certificate history using new DB collections.
    """
    try:
        # 1. Find the latest application for the SNR from 'applications' collection
        latest_application = mongo.db.applications.find_one(
            {'snrs.snr': snr_number}, # Match SNR within the snrs array
            sort=[('certificate_expiry_date', DESCENDING)] # Sort by expiry date to get latest
        )

        if not latest_application:
            print(f"No previous application found for SNR: {snr_number}. Treating as NEW.")
            return "NEW", "", "", "" # No prior application, treat as new

        # 2. Extract relevant data from the latest application
        expiry_date_str = latest_application.get('certificate_expiry_date')
        start_date_str = latest_application.get('certificate_start_date')
        applicant_name = latest_application.get('applicant_name')

        if not expiry_date_str or not start_date_str or not applicant_name:
            print(f"Incomplete certificate data found for SNR: {snr_number}. Treating as NEW.")
            return "NEW", "", "", applicant_name if applicant_name else "" # Incomplete data, treat as new, return applicant if available


        try:
            expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date() #Dates are stored as string in 'YYYY-MM-DD' format now
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError as e:
            print(f"Error parsing dates for SNR {snr_number}: {e}. Treating as ERROR.")
            return "ERROR", "", "", applicant_name # Date parsing error

        today = datetime.now().date()

        if today <= expiry_date:
            status = "UNAVAILABLE" # Or "REPRINT" if you want to differentiate - if current, consider unavailable/reprint
        elif (today - expiry_date).days <= 180:
            status = "RENEWAL" # within 6 months expiry
        else:
            status = "RENEWAL" # expired > 6 months (or consider "EXPIRED" if you want separate status)

        return status, datetime.strftime(start_date, '%B %d, %Y'), datetime.strftime(expiry_date, '%B %d, %Y'), applicant_name


    except Exception as e:
        print(f"Error checking SNR expiry for {snr_number}: {e}. Treating as ERROR.")
        return "ERROR", "", "", "" # General error case




# --- Date Handling Functions (outside generate_certificate_form for clarity) ---
def handle_renewal_dates(**kwargs):
    status = kwargs.get('status')
    old_cert_sdate_str = kwargs.get('old_cert_sdate_str')
    old_cert_edate_str = kwargs.get('old_cert_edate_str')

    default_start_date = datetime.now().date() #Default in case of error
    default_expiry_date = default_start_date + timedelta(days=365)

    if old_cert_sdate_str and old_cert_edate_str:
        old_cert_sdate = datetime.strptime(old_cert_sdate_str, '%B %d, %Y')
        old_cert_edate = datetime.strptime(old_cert_edate_str, '%B %d, %Y')

        if 0 < (datetime.today() - old_cert_edate).days <= 180: #Renewal within 6 months
            default_start_date = (old_cert_sdate + timedelta(days=364)).date()
            default_expiry_date = (old_cert_edate + timedelta(days=364 * 1)).date()
        elif 0 > (datetime.today() - old_cert_edate).days <= 180: # Future/Unexpired - Reprint
            old_cert_date = old_cert_sdate.strftime('%m/%d/%Y')
            future_expiry = (old_cert_edate + timedelta(days=1)).strftime('%m/%d/%Y')
            future_edate = (datetime.today() - old_cert_edate).days
            since_last_print = (datetime.today() - old_cert_sdate).days
            reprint_template = render_template("Reprint.html", delta=since_last_print, snr=kwargs.get('number'), future_edate=future_edate, future_expiry=future_expiry, old_date=old_cert_date, sid=kwargs.get('application').get('application_id'))
            return {'start_date': datetime.now().date(), 'expiry_date': default_expiry_date, 'reprint_template': reprint_template} #Using current date for start for reprint form. Adjust as needed


        else: #Expired > 6 months
            default_start_date = datetime.now().date()
            default_expiry_date = (datetime.today() + timedelta(days=364 * 1)).date()
    else:
        print("Error: Old certificate dates not found for renewal. Using current dates.")

    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}


def handle_new_certificate_dates(**kwargs):
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}


def handle_reprint_dates(**kwargs): # For "UNAVAILABLE" status - logic adapted from old code
    old_cert_sdate_str = kwargs.get('old_cert_sdate_str')
    old_cert_edate_str = kwargs.get('old_cert_edate_str')

    if old_cert_sdate_str and old_cert_edate_str:
        old_cert_sdate = datetime.strptime(old_cert_sdate_str, '%B %d, %Y')
        old_cert_edate = datetime.strptime(old_cert_edate_str, '%B %d, %Y')

        old_cert_date = old_cert_sdate.strftime('%m/%d/%Y')
        future_expiry = (old_cert_edate + timedelta(days=1)).strftime('%m/%d/%Y')
        future_edate = (datetime.today() - old_cert_edate).days
        since_last_print = (datetime.today() - old_cert_sdate).days
        reprint_template = render_template("Reprint.html", delta=since_last_print, snr=kwargs.get('number'), future_edate=future_edate, future_expiry=future_expiry, old_date=old_cert_date, sid=kwargs.get('application').get('application_id'))
        return {'start_date': datetime.now().date(), 'expiry_date': datetime.now().date() + timedelta(days=365), 'reprint_template': reprint_template} #Using current date for start in reprint form - adjust if needed. expiry just a default.
    else: # Error case if unavailable but no old dates, treat as new
        print("Error: Unavailable SNR status but no old certificate dates found. Treating as new certificate.")
        return handle_new_certificate_dates(**kwargs) # Treat as new if no old dates


def handle_error_dates(**kwargs):
    print("Error in Check_SNR_EXPIRY. Using default dates.")
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}

def handle_default_dates(**kwargs): # Fallback/Default handler
    print(f"Using default date handler for status: {kwargs.get('status')}") # Log unexpected status
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}




@app.route('/generate_certificate_form/<application_id>', methods=['GET'])
@login_required
def generate_certificate_form(application_id):
    application = mongo.db.applications.find_one({'application_id': application_id})
    if not application:
        flash('Application not found.', 'danger')
        return redirect(url_for('review_applications'))

    application_snrs = list(mongo.db.snrs.find({'application_id': application_id}))
    number = application_snrs[0]['snr'] if application_snrs else None

    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)

    if number:
        status, old_cert_sdate_str, old_cert_edate_str, applicant_name = Check_SNR_EXPIRY(number)
        print(f'{status} from {old_cert_sdate_str} - {old_cert_edate_str} for {applicant_name}')

        # --- Dispatch Table for Date Handling ---
        date_handlers = {
            "RENEWAL": handle_renewal_dates,
            "NEW": handle_new_certificate_dates, #For brand new certificates
            "UNAVAILABLE": handle_reprint_dates, #Or potentially handle_unavailable_snr, based on logic
            "ERROR": handle_error_dates, #Handle errors from Check_SNR_EXPIRY
            None: handle_new_certificate_dates #Default case if status is None or unexpected - treat as new
        }

        handler = date_handlers.get(status, handle_default_dates) # Get handler function based on status
        default_dates_info = handler(
            status=status,
            old_cert_sdate_str=old_cert_sdate_str,
            old_cert_edate_str=old_cert_edate_str,
            applicant_name=applicant_name,
            application=application,
            number=number
        )

        default_start_date = default_dates_info['start_date']
        default_expiry_date = default_dates_info['expiry_date']
        reprint_template = default_dates_info.get('reprint_template') #Optional: Check if a reprint template is needed
        if reprint_template:
            return reprint_template #Render reprint template if returned

    log_activity("Generate Certificate Form", f"User {current_user.contact} viewed certificate generation form for application {application_id}.")
    return render_template(
        'generate_certificate.html',
        application=application,
        application_snrs=application_snrs,
        default_start_date=default_start_date,
        default_expiry_date=default_expiry_date
    )




@app.route('/generate_certificate/<application_id>', methods=['POST'])
@login_required
def generate_certificate(application_id):
    application = mongo.db.applications.find_one({'application_id': application_id})
    
    if not application:
        flash('Application not found.', 'danger')
        return redirect(url_for('review_applications'))

    start_date_str = request.form.get('start_date') # Get date as string from form
    expiry_date_str = request.form.get('expiry_date') # Get date as string from form

    if not start_date_str or not expiry_date_str: # Check if either date string is None or empty
        flash('Please provide both start and expiry dates.', 'danger')
        return redirect(url_for('generate_certificate_form', application_id=application_id))

    try:
        start_date = parser.parse(start_date_str).date() # Convert string to date object
        expiry_date = parser.parse(expiry_date_str).date() # Convert string to date object
    except ValueError:
        flash('Invalid date format. Please use YYYY-MM-DD.', 'danger') # Corrected format description in flash message
        return redirect(url_for('generate_certificate_form', application_id=application_id))

    cert_id = application['application_id']
    company = application['company'] # Assuming 'company' is the correct field
    email_address = application['email'] # Assuming 'email' is the correct field
    filename = f'{company}_certificate_{cert_id}.pdf'
    filepath = os.path.join('certificates', filename)

    if not os.path.exists('certificates'):
        os.makedirs('certificates')

    # Fetch SNRs for this application
    application_snrs = list(mongo.db.snrs.find({'application_id': application_id}))

    # --- Improved Certificate PDF Generation using ReportLab Platypus ---
    doc = SimpleDocTemplate(filepath, pagesize=letter, topMargin=0.5*inch, bottomMargin=0.5*inch) # Adjust margins
    styles = getSampleStyleSheet()
    Story = []

    # --- Custom Styles ---
    title_style = styles['Heading1']
    title_style.alignment = TA_CENTER
    title_style.fontName = 'Helvetica-Bold'
    title_style.fontSize = 20
    title_style.textColor = HexColor("#003366") # Dark blue color

    header_style = styles['Heading3']
    header_style.alignment = TA_CENTER
    header_style.fontName = 'Helvetica-Bold'
    header_style.fontSize = 16
    header_style.textColor = HexColor("#003366") # Dark blue color

    normal_style = styles['Normal']
    normal_style.fontName = 'Helvetica'
    normal_style.fontSize = 10 # Further reduced font size for table fitting
    normal_style.leading = 12
    normal_style.alignment = TA_JUSTIFY # Use Justify for body text
    normal_style.textColor = black

    centered_normal_style = normal_style.clone('CenteredNormal')
    centered_normal_style.alignment = TA_CENTER

    bold_style = styles['Normal']
    bold_style.fontName = 'Helvetica-Bold'
    bold_style.fontSize = 10
    bold_style.leading = 12
    bold_style.alignment = TA_LEFT
    bold_style.textColor = black

    italic_centered_style = centered_normal_style.clone('ItalicCentered')
    italic_centered_style.fontName = italic_font_name # Use the dynamically determined italic font name
    italic_centered_style.fontSize = 9 # Further reduced font size for italic

    table_style = TableStyle([ # Style for the SNR table
        ('BACKGROUND', (0, 0), (-1, 0), HexColor("#e0e0e0")), # Header row background
        ('TEXTCOLOR', (0, 0), (-1, 0), black),
        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
        ('TOPPADDING', (0, 0), (-1, 0), 6),
        ('GRID', (0, 0), (-1, -1), 0.5, grey), # Grid lines for the table
        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'), # Normal font for data rows
        ('FONTSIZE', (0, 1), (-1, -1), 9), # Smaller font size for table data
        ('LEFTPADDING', (0, 0), (-1, -1), 5), # Padding within cells
        ('RIGHTPADDING', (0, 0), (-1, -1), 5),
        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'), # Vertical alignment in cells
    ])


    # --- Page Template with Background Image ---
    def certificate_page_template(canvas, doc):
        canvas.saveState()
        # Background Image
        bg_image_path = os.path.join(app.static_folder, 'certificate_background.jpg') # Path to your background image
        if os.path.exists(bg_image_path):
            bg_image = Image(bg_image_path, width=letter[0], height=letter[1])
            canvas.drawImage(bg_image_path, 0, 0) #Draw image at full page size

        # Header Line (optional - if you still want it over the background image)
        canvas.setStrokeColor(HexColor("#003366")) # Dark blue header line
        canvas.setLineWidth(2)
        header_y_position = letter[1] - 0.8 * inch # Position header line slightly below top margin
        canvas.line(inch, header_y_position, letter[0] - inch, header_y_position)


        # Footer - Page number if multi-page certificates are needed in future
        canvas.setFont('Helvetica', 9)
        canvas.setFillColor(grey)
        canvas.drawCentredString(letter[0]/2.0, 0.3 * inch, f"Page {doc.page}") # Simple page numbering


        canvas.restoreState()


    doc.addPageTemplates([PageTemplate(frames=[Frame(inch, inch, letter[0] - 2*inch, letter[1] - 2*inch, id=None)], onPage=certificate_page_template)]) # Apply page template


    # --- Header Section ---
    logo_path = os.path.join(app.static_folder, 'nca_logo-removebg.png')  # Place your logo in the static folder
    if os.path.exists(logo_path):
        logo = Image(logo_path, width=0.8*inch, height=0.8*inch) # Further reduced logo size
        logo.hAlign = 'CENTER'
        Story.append(logo)
        Story.append(Spacer(1, 0.03*inch)) # Further reduced space

    authority_name = Paragraph("<b>NATIONAL COMMUNICATIONS AUTHORITY</b>", header_style)
    Story.append(authority_name)
    Story.append(Spacer(1, 0.03*inch)) #Further reduced space

    republic_ghana = Paragraph("Republic of Ghana", centered_normal_style) # Republic of Ghana text
    Story.append(republic_ghana)
    Story.append(Spacer(1, 0.15*inch)) #Reduced space


    # --- Certificate Title ---
    certificate_title = Paragraph("<b>CERTIFICATE OF AUTHORITY</b>", title_style)
    Story.append(certificate_title)
    Story.append(Spacer(1, 0.15*inch)) # Reduced space


    # --- Granting Paragraph ---
    granting_text_1 = "The National Communications Authority (\"the NCA\") in accordance with the National Communications Authority Act, 2008, Act 769 (\"the Act\") hereby grants this Certificate of Authority to"
    granting_para_1 = Paragraph(granting_text_1, normal_style) # Use normal style with Justify alignment
    Story.append(granting_para_1)
    Story.append(Spacer(1, 0.15*inch)) # Reduced space


    # --- Applicant Information ---
    applicant_name_para = Paragraph(f"<font size=14><b>{company.upper()}</b></font>", centered_normal_style) # Applicant name larger and bold
    Story.append(applicant_name_para)
    Story.append(Spacer(1, 0.05*inch)) # Reduced space

    # address_text = "Barnes Road Adj. Old Parliament House, High Street, Accra, P. O. Box AC 80, Accra (GA-143-6271)" # Replace with actual address from application if available
    address_text = email_address # Replace with actual address from application if available
    address_para = Paragraph(f"<font size=9><i>(Hereinafter called the Assignment Holder)</i></font><br/><font size=9>{address_text}</font>", italic_centered_style) # Smaller italicized 'Assignment Holder' and address - further reduced font
    Story.append(address_para)
    Story.append(Spacer(1, 0.2*inch)) # Reduced space


    # --- Resources Paragraph ---
    resources_text = "Having fulfilled the requirements under The Electronic Communications Act, 2008, Act 775, National Electronic Communication Numbering Plan, the Special Numbering Resources and Administrative Framework, 2014, has been assigned the following Special Numbering Resources"
    resources_para = Paragraph(resources_text, normal_style) # Use normal style with Justify alignment
    Story.append(resources_para)
    Story.append(Spacer(1, 0.2*inch)) #Reduced space


    # --- SNR Table ---
    snr_table_data = [['Assigned SNR', 'Type of Assigned SNR', 'SNR Applications']] # Table header
    for snr_doc in application_snrs:
        snr_table_data.append([snr_doc['snr'], snr_doc['type'].upper(), snr_doc['purpose'].title()]) # Data rows

    snr_table = Table(snr_table_data)
    snr_table.setStyle(table_style) # Apply the table style
    Story.append(snr_table)
    Story.append(Spacer(1, 0.2*inch)) # Space after table


    # --- Validity Period ---
    validity_text = f"This Certificate of Authority is valid for a period of One (1) year starting from the Effective Date."
    validity_para = Paragraph(validity_text, normal_style) # Use normal style with Justify alignment
    Story.append(validity_para)
    Story.append(Spacer(1, 0.1*inch)) # Reduced space

    dates_text = f"""
    <font size=11><b>Effective Date:</b> {start_date.strftime('%B %d, %Y')}</font>    <font size=11><b>Date of Expiration:</b> {expiry_date.strftime('%B %d, %Y')}</font>
    """
    dates_para = Paragraph(dates_text, centered_normal_style) # Use centered normal style for dates
    Story.append(dates_para)
    Story.append(Spacer(1, 0.3*inch)) #Reduced space


    # --- Disclaimer ---
    disclaimer_text = "<i>This certificate shall be rendered null and void if any of the terms of the related regulatory framework is violated and the same shall be returned to the Authority upon request.</i>"
    disclaimer_para = Paragraph(disclaimer_text, italic_centered_style) # Use italic centered style
    Story.append(disclaimer_para)
    Story.append(Spacer(1, 0.3*inch)) # Further Reduced Space


    # --- Signature and Authority Details ---
    signature_line = Paragraph("_________________________", centered_normal_style) # Styling can be adjusted
    Story.append(signature_line)
    director_general_text = "<font size=11><b>Rev. Edmund Y. Fianko</b></font><br/><font size=9><i>DIRECTOR GENERAL</i></font>" # Further reduced font for signature details
    director_general_para = Paragraph(director_general_text, centered_normal_style) # Use centered normal style
    Story.append(director_general_para)
    Story.append(Spacer(1, 0.08*inch)) # Further Reduced space

    certificate_no_text = f"<font size=8>Certificate No: NCA/SNR/{cert_id[-8:]}</font>" # Extract last 8 chars of app ID for cert_id - even smaller font
    certificate_no_para = Paragraph(certificate_no_text, centered_normal_style) # Use centered normal style
    Story.append(certificate_no_para)
    Story.append(Spacer(1, 0.05*inch)) # Further Reduced Space


    # --- QR Code ---
    qr_img = generate_qr_code(cert_id) # Function to generate QR code
    if qr_img:
        qr_img.hAlign = 'RIGHT'
        Story.append(qr_img)


    doc.build(Story) # Build the PDF document using Platypus Story


    # Update application and SNRs in database (same as before)
    mongo.db.applications.update_one(
        {'application_id': application_id},
        {
            '$set': {
                'certificate_generated': True,
                'certificate_start_date': start_date_str, # Store as string for consistency with form
                'certificate_expiry_date': expiry_date_str, # Store as string for consistency with form
                'processed_by': current_user.contact
            }
        }
    )

    # Update each SNR document with certificate expiry date
    for snr_doc in application_snrs:
        mongo.db.snrs.update_one(
            {'_id': snr_doc['_id']}, # Use _id to uniquely identify SNR document
            {'$set': {'certificate_expiry_date': expiry_date_str, 'status':'certificate_generated'}}, # Store in snrs collection and update the application status to 'certificate generated'
        )

        # get the SNR type
        snr_collection = get_status_table(snr_doc['snr']) # you need to define this function get_status_table
        mongo.db[snr_collection].update_one({'SNR': snr_doc['snr']},
                                            {'$set': {'STATUS': 'ASSIGNED', 'EXPIRY': expiry_date_str, 'UPDATED_AT': datetime.now(timezone.utc)}})


    log_activity("Generated Certificate", f"User {current_user.contact} generated a certificate for application {application_id}.")
    # Redirect to the download URL
    return redirect(url_for('download_certificate', filename=filename))


def generate_qr_code(cert_id): # Pass cert_id (application_id)
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=6, # Further reduced box size for even smaller QR code
        border=4,
    )
    qr.add_data(url_for('verify_certificate', certificate_id=cert_id, _external=True)) # Embed verification URL with certificate_id
    qr.make(fit=True)

    img_byte_array = BytesIO()
    img = qr.make_image(fill_color="black", back_color="white")
    img.save(img_byte_array)
    img_byte_array.seek(0) # Reset to beginning of byte array

    return Image(img_byte_array, width=0.6*inch, height=0.6*inch) # Even smaller QR code image size


@app.route('/verify_certificate/<certificate_id>')
def verify_certificate(certificate_id):
    application = mongo.db.applications.find_one({'application_id': certificate_id}) # Find application by application_id (used as certificate_id)
    if not application:
        return jsonify({'valid': False, 'message': 'Certificate ID not found.'}), 404

    expiry_date_str = application.get('certificate_expiry_date')
    if not expiry_date_str:
        return jsonify({'valid': False, 'message': 'Expiry date not set for this certificate.'}), 400 # Or handle as invalid

    try:
        expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
    except ValueError:
        return jsonify({'valid': False, 'message': 'Invalid expiry date format in database.'}), 500 # Data issue

    is_expired = datetime.now().date() > expiry_date
    status_message = "Certificate is VALID." if not is_expired else "Certificate is EXPIRED."
    validity_status = not is_expired

    certificate_info = {
        'valid': validity_status,
        'message': status_message,
        'certificate_id': application['application_id'],
        'applicant_name': application['applicant_name'],
        'effective_date': application.get('certificate_start_date'), #Might be None
        'expiry_date': expiry_date_str #Return as string for display
    }

    return jsonify(certificate_info), 200 # Return certificate info as JSON




@app.route('/download_certificate/<filename>')
@login_required
def download_certificate(filename):
    certificates_dir = os.path.join(app.root_path, 'certificates')
    filepath = os.path.join(certificates_dir, filename)
    if not os.path.exists(filepath):
        flash('Certificate not found.', 'danger')
        return redirect(url_for('review_applications')) # Ensure review_applications route exists
    return send_file(filepath, as_attachment=True) # Import send_file from flask


@app.route('/edit_application/<application_id>', methods=['GET', 'POST'])
@login_required
def edit_application(application_id):
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to edit an application with a non-admin account.")
        flash("You do not have permission to access this page.", "danger")
        return redirect(url_for('dashboard'))

    # Fetch the application details from the database
    application = mongo.db.applications.find_one({'application_id': application_id})
    snrs = list(mongo.db.snrs.find({'application_id': application_id}))

    if not application:
        flash('Application not found.', 'danger')
        return redirect(url_for('review_applications'))

    change_log = []

    if request.method == 'POST':
        # Collect updated data from the form
        new_status = request.form.get('status')
        new_purposes = []
        for key, value in request.form.items():
            if key.startswith('purpose_'):
                new_purposes.append(value)
        new_expiry_date = request.form.get('expiry_date')
        new_applicant_name = request.form.get('applicant_name')
        new_remarks = request.form.get('remarks')

        application_expiry_date = application.get('certificate_expiry_date', 'N/A')

        # Prepare to collect changes to the application
        if new_status != application['status']:
            message = f"Application {application_id} status changed to {new_status}."
            add_notification(message, application['applicant_name'])

            change_log.append({
                'field': 'status',
                'old_value': application['status'],
                'new_value': new_status,
                'modified_by': current_user.contact,
                'modified_at': datetime.now(timezone.utc)
            })
            application['status'] = new_status

        if new_applicant_name != application['applicant_name']:
            change_log.append({
                'field': 'applicant_name',
                'old_value': application['applicant_name'],
                'new_value': new_applicant_name,
                'modified_by': current_user.contact,
                'modified_at': datetime.now(timezone.utc)
            })
            application['applicant_name'] = new_applicant_name

        if new_remarks != application['remarks']:
            change_log.append({
                'field': 'remarks',
                'old_value': application['remarks'],
                'new_value': new_remarks,
                'modified_by': current_user.contact,
                'modified_at': datetime.now(timezone.utc)
            })
            application['remarks'] = new_remarks

        if new_expiry_date and new_expiry_date != application_expiry_date:
            change_log.append({
                'field': 'certificate_expiry_date',
                'old_value': application_expiry_date,
                'new_value': new_expiry_date,
                'modified_by': current_user.contact,
                'modified_at': datetime.now(timezone.utc)
            })
            application['certificate_expiry_date'] = new_expiry_date

        # Update application collection with change_log
        mongo.db.applications.update_one(
            {'application_id': application_id},
            {
                '$set': application,
                '$push': {'change_log': {'$each': change_log}}  # Add change log to the document
            }
        )

        # Update SNRs collection for the specific application
        for idx, snr in enumerate(snrs):
            if new_purposes[idx] and new_purposes[idx] != snr.get('purpose'):
                mongo.db.snrs.update_one(
                    {'_id': snr['_id']},
                    {'$set': {'purpose': new_purposes[idx]}}
                )
                change_log.append({
                    'field': 'purpose',
                    'old_value': snr['purpose'],
                    'new_value': new_purposes[idx],
                    'modified_by': current_user.contact,
                    'modified_at': datetime.now(timezone.utc)
                })

            # Update status in the SNR collection
            if new_status and new_status != snr.get('status'):
                mongo.db.snrs.update_one(
                    {'_id': snr['_id']},
                    {'$set': {'status': new_status}}
                )
                change_log.append({
                    'field': 'status',
                    'old_value': snr['status'],
                    'new_value': new_status,
                    'modified_by': current_user.contact,
                    'modified_at': datetime.now(timezone.utc)
                })

            # Update expiry date in the SNR collection
            if new_expiry_date and new_expiry_date != snr.get('certificate_expiry_date', 'N/A'):
                mongo.db.snrs.update_one(
                    {'_id': snr['_id']},
                    {'$set': {'certificate_expiry_date': new_expiry_date}}
                )
                change_log.append({
                    'field': 'certificate_expiry_date',
                    'old_value': snr.get('certificate_expiry_date', 'N/A'),
                    'new_value': new_expiry_date,
                    'modified_by': current_user.contact,
                    'modified_at': datetime.now(timezone.utc)
                })

        # Save the changes to the change_log
        if change_log:
            mongo.db.applications.update_one(
                {'application_id': application_id},
                {
                    '$push': {'change_log': {'$each': change_log}}  # Add change log to the document
                }
            )

        # Log the activity and flash a success message
        flash('Application details updated successfully.', 'success')
        log_activity("Admin Application Editing", f"User {current_user.contact} successfully edited details of application {application_id}.")
        return redirect(url_for('review_applications'))

    return render_template('edit_application.html', application=application, snrs=snrs, datetime=datetime, timedelta=timedelta)



@app.context_processor
def inject_notifications():
    if current_user.is_authenticated:
        # Fetch unread notifications for the current user
        unread_notifications = list(mongo.db.notifications.find({
            'recipient': current_user.contact,
            'read': False
        }).sort('timestamp', -1).limit(5))
        
        unread_notifications_count = len(unread_notifications)
        
        return {
            'notifications': unread_notifications,
            'unread_notifications_count': unread_notifications_count
        }
    return {
        'notifications': [],
        'unread_notifications_count': 0
    }




def add_notification(message, recipient):
    notification = {
        'message': message,
        'recipient': recipient,
        'read': False,
        'timestamp': datetime.now(timezone.utc)
    }
    mongo.db.notifications.insert_one(notification)


# View notifications page
@app.route('/notifications', methods=['GET', 'POST'])
@login_required
def view_notifications():
    if request.method == 'POST':
        # Mark selected notifications as read
        notification_ids = request.form.getlist('notification_ids')
        for notification_id in notification_ids:
            mongo.db.notifications.update_one(
                {'_id': ObjectId(notification_id)},
                {'$set': {'read': True}}
            )
        flash('Notifications marked as read.', 'success')
        return redirect(url_for('view_notifications'))

    # Fetch all notifications for the current user
    notifications = list(mongo.db.notifications.find({
        'recipient': current_user.contact
    }).sort('timestamp', -1))

    return render_template('notifications.html', notifications=notifications)


# For viewing a single notification
@app.route('/notification/<notification_id>', methods=['GET'])
@login_required
def view_notification(notification_id):
    # Fetch the notification to validate its existence and determine next steps
    notification = mongo.db.notifications.find_one({'_id': ObjectId(notification_id)})

    if not notification:
        flash('Notification not found.', 'danger')
        return redirect(url_for('view_notifications'))

    # Ensure that the notification belongs to the current user
    if notification['recipient'] != current_user.contact:
        flash('You are not authorized to view this notification.', 'danger')
        return redirect(url_for('view_notifications'))

    # Mark the notification as read
    mongo.db.notifications.update_one(
        {'_id': ObjectId(notification_id)},
        {'$set': {'read': True}}
    )

    # Redirect to the appropriate application or dashboard
    if notification and "Application" in notification['message']:
        if current_user.is_admin:
            # Admin user - Redirect to the application review page
            return redirect(url_for('review_applications'))
        else:
            # Non-admin user (applicant) - Redirect to the application status page
            application_id = notification['message'].split()[1]  # Extract application ID from the message
            return redirect(url_for('view_application_status', application_id=application_id))
    
    # Default redirection to the user's dashboard if no specific handling is required
    return redirect(url_for('dashboard'))

@app.route('/application_status/<application_id>', methods=['GET'])
@login_required
def view_application_status(application_id):
    # Fetch the application details for the user
    application = mongo.db.applications.find_one({
        'application_id': application_id,
        'user_id': current_user.id  # Ensure user only sees their own applications
    })
    
    if not application:
        flash('You are not authorized to view this application or it does not exist.', 'danger')
        return redirect(url_for('dashboard'))

    return render_template('applications_status.html', application=application)






# REPORT GENERATION
@app.route('/generate_report', methods=['GET'])
@admin_required
def generate_report():
    log_activity("Report Generation", f"User {current_user.contact} viewed Admin Report generation page.")
    return render_template('reports.html')


def determine_reports_collections(query):
    """
    Determines the MongoDB collections to search based on the user's query.
    Returns a list of collection names. 
    """
    query = query.lower()
    collections = ['applications']  # Default collections

    # Determine specific status tables if a number type is referenced
    if re.search(r'\bshortcode\b|\b3 digit\b|\b4 digit\b|\b5 digit\b|\b6 digit\b|^[0-9]{3,6}$', query):
        collections.append('snr_status')

    if re.search(r'\btoll-?free\b|\b0800\b|^0800[0-9]{6}$', query):
        collections.append('tf_status')

    if re.search(r'\bpremium-?rate\b|\b0900\b|^0900[0-9]{6}$', query):
        collections.append('pr_status')

    return list(set(collections))


def create_report_mongodb_queries_with_gpt(query, collections):
    """
    Sends the user query and collections to GPT-4o-mini to generate MongoDB queries.
    Returns a list of MongoDB query dictionaries.
    """
    prompt = f"""
    You are an assistant helping to create MongoDB queries based on the user's search intent.
    Below are the collections and their respective fields that you can use to generate the MongoDB queries.

    Collections and their available fields:
    - 'snrs': {{
        "_id": ObjectId,
        "snr": string,          # Special Numbering Resource (e.g., "8001")
        "type": string,         # Type of number (e.g., "shortcode", "tollfree", "premium-rate")
        "status": string,       # Status of the SNR (e.g., "Approved", "Available", "Assigned", "Pending Review", "Reserved", "Certifcate_printable" )
        "purpose": string,      # Purpose for which the SNR is requested
        "certificate_id": string,
        "expiry_date": date,    # Expiry date of the number if applicable
 
    }}
    
    - 'applications': {{
        "_id": ObjectId,
        "application_id": string,
        "applicant_name": string,
        "email": string,
        "contact": string,
        "phone": string,
        "company": string,
        "application_date": date,  # Date of the application submission
        "status": string,          # Status of the application (e.g., "Pending Review", "Approved", "Rejected")
        "snrs": array,             # List of linked SNRs to the application
        "remarks": string,
        "created_at": date,
        "updated_at": date
    }}
    
    - 'snr_status': {{
        "SNR": string,            # Shortcode Special Numbering Resource
        "STATUS": string,         # Status of the SNR ("AVAILABLE", "EXPIRED", "ASSIGNED", "RESERVED")
        "EXPIRY": date,           # Expiry date if applicable
        "ASSIGNEE": string        # Name of the assignee if assigned
    }}

    - 'tf_status': {{
        "SNR": string,            # Toll-free Special Numbering Resource
        "STATUS": string,         # Status of the SNR ("AVAILABLE", "EXPIRED", "ASSIGNED", "RESERVED")
        "EXPIRY": date,
        "ASSIGNEE": string
    }}

    - 'pr_status': {{
        "SNR": string,            # Premium-rate Special Numbering Resource
        "STATUS": string,         # Status of the SNR ("AVAILABLE", "EXPIRED", "ASSIGNED", "RESERVED")
        "EXPIRY": date,
        "ASSIGNEE": string
    }}

    Important Notes:
    - Only use the fields described above when generating MongoDB queries. Do not add or use any fields that are not defined in the collections.
    - The `snrs` collection contains limited information about processed numbers (shortcodes, toll-free, premium-rate).
    - The `applications` collection contains details for applications linked to the numbers in the snrs collection. It includes the application status, and other relevant information.
    - Use the appropriate collections based on the user query.
    - Never use both `snrs` and `applications` collections in the same list of collections. They related and either will be linked to the other using a mongodb aggregation pipeline by a different function.
    - If you have to choose between `snrs` and `applications`, prefer `applications` as it contains more comprehensive information about the numbers and their associated applications. However, if the user query specifically mentions a number (shortcode, toll-free, premium-rate), use the `snrs` collection to search for that number and then use the `applications` collection to retrieve additional information about the number's associated application, if any. This can be done using a mongodb aggregation pipeline by a different function.


    User Query: "{query}"
    Collections to Query: {collections}


    Provide the report type and MongoDB queries for each relevant collection in the following JSON format:
    {{
    "report_type": # Type of report (e.g., "Monthly Report", "Quarterly Report", "Yearly Report", "Custom Report") based on the user query. If not clear, use "Custom Report".
        "queries": {{
            "snrs": <MongoDB query for snrs>,
            "applications": <MongoDB query for applications>,
            "snr_status": <MongoDB query for snr_status>,
            "tf_status": <MongoDB query for tf_status>,
            "pr_status": <MongoDB query for pr_status>
        }},
        "limit": <Maximum number of results to return based on the query or 10 if not specified in the query>,
        
    }}


    Replace `<MongoDB query>` with actual query objects based on the fields provided.
    If a collection is not relevant to the query, set its value to `null`.

    Here's an example of how the response should look like for the query "Generate the report for all SNRs processed for the whole month of October 2024":
    {{
        "report_type": "Monthly Report",
        "queries": {{
            "snrs": null, # (since this table has no date component to filter by month. A join can be made with applications table to filter by application_date)refer to the applications table instead
            "applications": {{application_date: {{'$gte': '2024-10-01'), '$lt': '2024-11-01'}},
            "snr_status": None,
            "tf_status": None,
            "pr_status": None,
        }},
        "limit": null
    }}

    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )

        # Extract the assistant's reply
        reply = response.choices[0].message.content.strip()

        query_data = json.loads(reply)

        print('query_data', query_data)  # Debugging line to print the query data

        return query_data

    except Exception as e:
        print(f"Error creating MongoDB queries with GPT: {e}")
        return {}

def preprocess_snr(snr):
    # Remove spaces and hyphens
    snr = snr.replace(" ", "").replace("-", "")
    
    # Ensure it's a valid numeric string (after stripping any non-numeric chars)
    if snr.isdigit():
        return snr
    else:
        return None  # Return None if the input is not numeric

def snr_length_finder(snr):
    snr = preprocess_snr(snr)  # Preprocess the SNR

    if snr is None:
        return "Undefined"  # Return "Undefined" if preprocessing returns None

    # If the snr starts with "0800", it's Tollfree
    if len(snr) >= 9 and (snr.startswith("0800") or snr.startswith("800")):
        ans = "Tollfree"
    elif len(snr) >= 9 and 900000000 <= int(snr) <= 900999999:
        ans = "Premium"
    elif len(snr) == 3 and 100 <= int(snr) <= 999:
        ans = "3 Digit"
    elif len(snr) == 4 and 1000 <= int(snr) <= 9999:
        ans = "4 Digit"
    elif len(snr) == 5 and 10000 <= int(snr) <= 99999:
        ans = "5 Digit"
    elif len(snr) == 6 and 100000 <= int(snr) <= 999999:
        ans = "6 Digit"
    else:
        ans = "Undefined"
    
    return ans



pricing = {"3 Digit": 2100,
    "4 Digit": 500,
    "5 Digit":300,
    "6 Digit":250,
    "Tollfree":40,
    "Premium":100}



def convert_object_types(result):
    """
    Recursively convert ObjectId and datetime types to strings in a nested dictionary.
    """
    if isinstance(result, dict):
        # Iterate through the dictionary and process each key-value pair
        for key, value in result.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, dict):
                # Recursively apply to nested dictionaries
                convert_object_types(value)
            elif isinstance(value, list):
                # Recursively apply to each item in the list
                for item in value:
                    convert_object_types(item)
    elif isinstance(result, list):
        # If result itself is a list, apply conversion to each item
        for item in result:
            convert_object_types(item)


def flatten_document(doc):
    flattened = {}

    # Handle the top-level fields
    for key, value in doc.items():
        if isinstance(value, dict):
            if '$oid' in value:
                flattened[key] = value['$oid']  # Extract ObjectId
            elif '$date' in value:
                flattened[key] = value['$date']  # Extract Date
            else:
                # If it's another nested dict, prefix the keys
                for sub_key, sub_value in value.items():
                    flattened[f"{key}_{sub_key}"] = sub_value
        else:
            flattened[key] = value

    # Handle the nested snr_details separately
    if 'snr_details' in doc:
        for key, value in doc['snr_details'].items():
            if isinstance(value, dict) and '$oid' in value:
                flattened[f"snr_details_{key}"] = value['$oid']  # Extract ObjectId
            else:
                flattened[f"snr_details_{key}"] = value

    return flattened



# NLP
@app.route('/generate_natural_report', methods=['POST'])
@login_required
def generate_natural_report():
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to generate anatural language report.")
        return jsonify({'error': 'Unauthorized access'}), 403

    # try:
    user_input = request.json.get('description', '')
    if not user_input:
        return jsonify({'error': 'Please provide a valid report description'}), 400

    # Use GPT-4o-mini to analyze user input and determine relevant collections
    collections = determine_reports_collections(user_input)
    print('collections', collections)  # Debugging output
    
    mongodb_queries_full = create_report_mongodb_queries_with_gpt(user_input, collections)

    print('mongodb_queries_full', mongodb_queries_full)  # Debugging output
    report_type = mongodb_queries_full.get("report_type", 'Custom Report')
    mongodb_queries = mongodb_queries_full.get('queries', {})
    limits = mongodb_queries_full.get('limit')

    # Determine if join is needed based on collections involved
    use_applications = 'applications' in mongodb_queries
    use_snrs = 'snrs' in mongodb_queries

    search_results = []

    if use_applications and use_snrs:
        # When both are needed, default to using pipeline1
        applications_query = mongodb_queries.get('applications', {})
        
        aggregation_pipeline = [
            {
                "$match": applications_query
            },
            {
                "$lookup": {
                    "from": 'snrs',
                    "localField": 'application_id',
                    "foreignField": 'application_id',
                    "as": 'snr_details'
                }
            },
            { "$unwind": { "path": '$snr_details' } }
        ]

        # Add limit if applicable
        if limits and limits > 0:
            aggregation_pipeline.append({"$limit": limits})

        # Perform aggregation on applications collection
        print('performing aggregation on applications collection')
        search_results = list(mongo.db.applications.aggregate(aggregation_pipeline))

    elif use_applications:
        # Only applications collection is available, use pipeline1
        applications_query = mongodb_queries.get('applications', {})
        
        aggregation_pipeline = [
            {
                "$match": applications_query
            },
            {
                "$lookup": {
                    "from": 'snrs',
                    "localField": 'application_id',
                    "foreignField": 'application_id',
                    "as": 'snr_details'
                }
            },
            { "$unwind": { "path": '$snr_details' } }
        ]

        # Add limit if applicable
        if limits and limits > 0:
            aggregation_pipeline.append({"$limit": limits})

        # Perform aggregation on applications collection
        print('performing aggregation on applications collection')
        search_results = list(mongo.db.applications.aggregate(aggregation_pipeline))

    elif use_snrs:
        # Only snrs collection is available, use pipeline2
        snrs_query = mongodb_queries.get('snrs', {})
        
        aggregation_pipeline = [
            {
                "$match": snrs_query
            },
            {
                "$lookup": {
                    "from": 'applications',
                    "localField": 'application_id',
                    "foreignField": 'application_id',
                    "as": 'snr_details'
                }
            },
            { "$unwind": { "path": '$snr_details' } }
        ]

        # Add limit if applicable
        if limits and limits > 0:
            aggregation_pipeline.append({"$limit": limits})

        # Perform aggregation on snrs collection
        print('performing aggregation on snrs collection')
        search_results = list(mongo.db.snrs.aggregate(aggregation_pipeline))

    else:
        # Perform individual collection queries if neither applications nor snrs requires join
        print('Perform individual collection queries if neither applications nor snrs requires join')
        for collection_name, mongo_query in mongodb_queries.items():
            if mongo_query:
                collection = mongo.db[collection_name]
                if limits is None or limits == 'null':
                    search_results += list(collection.find(mongo_query))
                else:
                    search_results += list(collection.find(mongo_query).limit(limits))

    # Format the search results
    flattened_results = [ flatten_document(doc) for doc in search_results ] # Flatten the documents

    for result in flattened_results:
        convert_object_types(result)

    print('search results flattened: ', flattened_results)  # Debugging output
    
    

    # Convert results to a DataFrame
    df = pd.DataFrame(flattened_results)

    if df.empty:
        return jsonify({'error': 'No data found for the given criteria'}), 404


    # Add additional calculated columns
    df['category'] = df['snr_details_snr'].apply(lambda x: snr_length_finder(x))
    df["fees"] = df['category'].apply(lambda x: pricing.get(x))


    print('df columns and 5 rows: ', df.columns, df.head(5))  # Debugging output

# Generate summary with GPT-4o-mini
    search_results_summary = generate_report_prose_with_gpt(df, report_type)

    # Generate column selection for graph dynamically using GPT-4o-mini
    prompt = f"""
    Given the following data columns: {list(df.columns)},
    determine which columns should be used for plotting a meaningful graph.
    Suggest which column to use for x-axis and which to use for y-axis.
    The response must be in JSON format as follows:
    {{
        "x_axis": "<suggested_column_for_x_axis>",
        "y_axis": "<suggested_column_for_y_axis>",
        "chart_type": "<suggested_chart_type>"  # Options: "line", "bar", "scatter", etc.
    }}
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )
        chart_config = json.loads(response.choices[0].message.content.strip())

        # Extract suggested configurations
        x_axis = chart_config.get("x_axis", "snr")  # Default to 'snr'
        y_axis = chart_config.get("y_axis", "fees")  # Default to 'fees'
        chart_type = chart_config.get("chart_type", "line")

        # Generate the chart based on the suggested configuration
        if chart_type == "line":
            fig = px.line(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
        elif chart_type == "bar":
            fig = px.bar(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
        elif chart_type == "scatter":
            fig = px.scatter(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
        else:
            # Fallback to line chart if an unsupported type is suggested
            fig = px.line(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')

        graph_json = fig.to_json()

    except Exception as e:
        print(f"Error with GPT chart suggestion: {e}")
        return jsonify({'error': 'Error generating chart based on GPT suggestion.'}), 500

    log_activity("Report Generated", f"User {current_user.contact} generated a Natural Language Report.")
    # Send JSON response with summary and graph
    return jsonify({
        "summary": search_results_summary,
        "visualization": json.loads(graph_json)
    })

    # except Exception as e:
    #     print(f"Error generating natural report: {e}")
    #     return jsonify({'error': str(e)}), 500



def convert_object_ids_and_timestamps_to_str(df):
    """Enhanced converter that handles nested structures"""
    def convert_element(x):
        if isinstance(x, (datetime, pd.Timestamp)):
            return x.isoformat()
        if isinstance(x, ObjectId):
            return str(x)
        if isinstance(x, list):
            return [convert_element(e) for e in x]
        if isinstance(x, dict):
            return {k: convert_element(v) for k, v in x.items()}
        return x

    for col in df.columns:
        df[col] = df[col].apply(convert_element)
    
    return df

@app.route('/generate_static_report', methods=['POST'])
@login_required
def generate_static_report():
    if not current_user.is_admin:
        log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to generate a static report with a non-admin account.")
        return jsonify({'error': 'Unauthorized access'}), 403

    # Parse the structured form inputs from the request
    form_data = request.json
    report_type = form_data.get('report_type', 'monthly')
    number_type = form_data.get('number_type', 'applications')
    date_range = form_data.get('date_range', '').split(' to ')

    if len(date_range) != 2:
        return jsonify({'error': 'Invalid date range provided'}), 400

    start_date_str = date_range[0].strip()
    end_date_str = date_range[1].strip()

    # Fetch applications from MongoDB based on date range
    applications_cursor = mongo.db.applications.find(
        {
            "application_date": {"$gte": start_date_str, "$lte": end_date_str}
        }
    )
    applications_list = list(applications_cursor)
    if not applications_list:
        return jsonify({'error': 'No application data found for the given criteria'}), 404
    applications_df = pd.DataFrame(applications_list)


    # Fetch all SNRs from MongoDB
    snrs_cursor = mongo.db.snrs.find()
    snrs_list = list(snrs_cursor)
    snrs_df = pd.DataFrame(snrs_list)
    if snrs_df.empty:
        return jsonify({'error': 'No SNR data found in the database'}), 404


    # Merge DataFrames using application_id as the key
    merged_df = pd.merge(applications_df, snrs_df, on='application_id', suffixes=('_app', '_snr'), how='left')
    if merged_df.empty:
        return jsonify({'error': 'No data found after merging applications and SNRs'}), 404


    df = merged_df.copy() # Use a copy to avoid modifying original merged_df

    # Convert ObjectId columns to strings AFTER DataFrame creation and merge
    df = convert_object_ids_and_timestamps_to_str(df)


    print('df columns after merge: ', df.columns)
    df['category'] = df['snr'].apply(lambda x: snr_length_finder(x))


    # Apply filters based on number type (same as before)
    number_type_filters = {
        'applications': lambda df: df,
        'snr_status': lambda df: df[df['snr_number_type'].isin(["3 Digit", "4 Digit", "5 Digit", "6 Digit"])],
        'tf_status': lambda df: df[df['snr_number_type'] == "Tollfree"],
        'pr_status': lambda df: df[df['snr_number_type'] == "Premium"],
    }

    df = number_type_filters.get(number_type, lambda df: df)(df)

    # Add additional calculated columns if needed (same as before)
    df["fees"] = df['category'].apply(lambda x: pricing.get(x, 0))


    # Generate chart configuration using GPT-4o-mini (same as before)
    prompt = f"""
Given the following data columns from a report on telecom number applications: {list(df.columns)}.
The data includes information about applications for different types of telecom numbers (Shortcodes, Toll-free, Premium-rate).
It also includes 'category' (number type), 'fees' (revenue), 'application_date', and 'status_app' (application status).

Based on these columns, suggest insightful graph visualizations that would be valuable for a business report.
Specifically, consider graphs that can show:
- Monthly revenue trends per number type.
- Distribution of application requests or assignments across different number types for the given period.

Determine which columns should be used for plotting these meaningful graphs.
Suggest which column to use for x-axis and which to use for y-axis for each graph idea.
Also, suggest the most appropriate chart type for each visualization (e.g., "line", "bar", "pie").

Your response must be in JSON format as a list of graph configurations. Each configuration should have the following structure:
{{
    "graph_title": "<descriptive_title_of_the_graph>",
    "x_axis": "<suggested_column_for_x_axis>",
    "y_axis": "<suggested_column_for_y_axis>",
    "chart_type": "<suggested_chart_type>",
    "description": "<brief_description_of_what_the_graph visualizes and its insights>"
}}

Provide 2-3 graph suggestions that are most relevant to understanding the data, focusing on revenue and application distribution by number type.
"""


    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )
        chart_configs = json.loads(response.choices[0].message.content.strip())

        visualizations = [] # List to hold all graph JSONs and descriptions

        if isinstance(chart_configs, list): # Ensure it's a list of configs
            for config in chart_configs:
                x_axis = config.get("x_axis")
                y_axis = config.get("y_axis")
                chart_type = config.get("chart_type", "line") # Default to line if missing
                graph_title = config.get("graph_title", f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}') # Default title
                graph_description = config.get("description", "A chart visualization.") # Default description


                # Generate the chart based on the suggested configuration
                if chart_type == "line":
                    fig = px.line(df, x=x_axis, y=y_axis, title=graph_title)
                elif chart_type == "bar":
                    fig = px.bar(df, x=x_axis, y=y_axis, title=graph_title)
                elif chart_type == "pie": # Added pie chart option
                    fig = px.pie(df, names=x_axis, values=y_axis, title=graph_title) # Pie chart needs names and values
                elif chart_type == "scatter":
                    fig = px.scatter(df, x=x_axis, y=y_axis, title=graph_title)
                else:
                    fig = px.line(df, x=x_axis, y=y_axis, title=graph_title) # Fallback

                graph_json = fig.to_json()
                visualizations.append({"visualization": json.loads(graph_json), "description": graph_description, "title": graph_title}) # Store JSON, description, and title

        else: # Handle case if GPT returns a single config (or error) - fallback to original logic for single chart.
            x_axis = chart_configs.get("x_axis", "snr")
            y_axis = chart_configs.get("y_axis", "fees")
            chart_type = chart_configs.get("chart_type", "line")
            fig = px.line(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
            graph_json = fig.to_json()
            visualizations = [{"visualization": json.loads(graph_json), "description": "A default chart visualization.", "title": f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}'}] # Still put in list format


    except Exception as e:
        print(f"Error with GPT chart suggestion: {e}")
        return jsonify({'error': 'Error generating chart based on GPT suggestion.'}), 500


    # Generate the summary using GPT (same as before)
    # Pass the DataFrame AFTER ObjectId conversion
    summary = generate_report_prose_with_gpt(
    df.copy().astype(str),  # Force all remaining elements to strings
    report_type
)

    # Select relevant columns from the DataFrame (adjust column names to reflect merged DataFrame)
    reduced_df = df[['snr', 'status_app', 'category', 'fees']].copy() # Create a copy to avoid modifying original df


    reduced_df = reduced_df.rename(columns={'status_app': 'status'}) # rename column for consistency

    #group by category and sum (same as before)
    reduced_df_summary = reduced_df.groupby('category').agg(
                                                total_count=('category', 'size'),  # Count of entries per category
                                                total_fees=('fees', 'sum')        # Sum of fees per category
                                                    ).reset_index()

    # Generate JSON format for reduced DataFrame (same as before)
    reduced_data_summary = reduced_df_summary.to_dict(orient='records')

    # Generate JSON format for reduced DataFrame (same as before)
    reduced_data = reduced_df.to_dict(orient='records')

    # Generate Plotly Table from the reduced DataFrame (same as before)
    fig = go.Figure(data=[go.Table(
        header=dict(values=list(reduced_df.columns),
                    fill_color='paleturquoise',
                    align='left'),
        cells=dict(values=[reduced_df[col] for col in reduced_df.columns],
                   fill_color='lavender',
                   align='left'))
    ])

    # Convert figure to JSON for visualization (same as before)
    table_json = fig.to_json()


    # Generate Plotly Table from the Summary of DataFrame (same as before)
    fig2 = go.Figure(data=[go.Table(
        header=dict(values=list(reduced_df_summary.columns),
                    fill_color='paleturquoise',
                    align='left'),
        cells=dict(values=[reduced_df_summary[col] for col in reduced_df_summary.columns],
                   fill_color='lavender',
                   align='left'))
    ])

    # Convert figure to JSON for visualization (same as before)
    table_json2 = fig2.to_json()

    log_activity("Report Generated", f"User {current_user.contact} generated a Static Language Report.")
    # Send JSON response containing the summary and visualization (same as before)
    # return jsonify({
    #     "summary": summary,
    #     "visualization": json.loads(graph_json),
    #     "dataframe_visualization": json.loads(table_json),
    #     "dataframe_visualization2": json.loads(table_json2),
    #     "reduced_dataframe": reduced_data,
    #     "reduced_dataframe_summary": reduced_data_summary
    # })


    return jsonify({
            "summary": summary,
            "visualizations": visualizations, # Changed from "visualization" to "visualizations" - now a list
            "dataframe_visualization": json.loads(table_json),
            "dataframe_visualization2": json.loads(table_json2),
            "reduced_dataframe": reduced_data,
            "reduced_dataframe_summary": reduced_data_summary
        })

def log_activity(action, details=""):
    activity = {
        "user_id": getattr(current_user, 'id', 'Guest'),
        "email": getattr(current_user, 'email', 'Guest'),
        "action": action,
        "details": details,
        "timestamp": datetime.now(timezone.utc)
    }
    mongo.db.activity_log.insert_one(activity)


@app.route('/activity_log_summary', methods=['GET'])
@login_required
@admin_required
def activity_log_summary():
    log_activity("Activity Log Summary", f"User {current_user.contact} viewed Activity Log Summary.")
    # Fetch and group activities by user and action over time
    aggregation_pipeline = [
        {"$group": {
            "_id": {"user_id": "$user_id", "action": "$action"},
            "total": {"$sum": 1},
            "first_action": {"$min": "$timestamp"},
            "last_action": {"$max": "$timestamp"}
        }},
        {"$sort": {"last_action": DESCENDING}}
    ]

    activity_summary = list(mongo.db.activity_log.aggregate(aggregation_pipeline))

    # Prepare data for visualization
    activities = []
    for activity in activity_summary:
        activities.append({
            "user_id": activity["_id"]["user_id"],
            "action": activity["_id"]["action"],
            "total": activity["total"],
            "first_action": activity["first_action"].strftime('%Y-%m-%d %H:%M:%S'),
            "last_action": activity["last_action"].strftime('%Y-%m-%d %H:%M:%S')
        })

    return jsonify(activities)






# /////////OPERATOR-SIDE OPERATIONS/////////////

@app.route('/api/snrs/approved', methods=['GET'])
def get_approved_snrs():
    """
    Get all approved SNRs that are ready for implementation by operators.
    Uses the existing aggregation function and filters for approved status.
    """
    try:
        # Get aggregated data using the existing function
        aggregated_data = aggregate_applications_and_snrs()
        
        # Filter for approved SNRs that don't have implementation status yet
        approved_snrs = []
        for record in aggregated_data:
            if record.get('snr_data', {}).get('status') == 'Approved' or record.get('snr_data', {}).get('status') == 'certificate_generated':
                # Create a simplified record with essential fields
                approved_snr = {
                    "application_id": record.get('application_id'),
                    "applicant_name": record.get('applicant_name'),
                    "company": record.get('company'),
                    "application_date": record.get('application_date'),
                    "snr": record.get('snr_data', {}).get('snr'),
                    "type": record.get('snr_data', {}).get('type'),
                    "purpose": record.get('snr_data', {}).get('purpose'),
                    "status": record.get('snr_data', {}).get('status'),
                    "certificate_id": record.get('snr_data', {}).get('certificate_id'),
                    "implementation_status": record.get('snr_data', {}).get('implementation_status', None)
                }
                approved_snrs.append(approved_snr)
        
        log_activity("API Request", f"Operator API requested list of approved SNRs")
        return jsonify(approved_snrs)
    except Exception as e:
        log_activity("API Error", f"Error in get_approved_snrs: {str(e)}")
        return jsonify({"error": f"Error retrieving approved SNRs: {str(e)}"}), 500

@app.route('/api/snrs/<certificate_id>/claim', methods=['POST'])
def claim_snr_implementation(certificate_id):
    """
    Claim a specific SNR for implementation by an operator.
    Updates the SNR record with implementation information.
    """
    data = request.json
    operator_name = data.get('operator')
    
    if not operator_name:
        return jsonify({"error": "Operator name is required"}), 400
    
        # Use the snr_tracker here
    result = snr_tracker.update_snr_status(
        certificate_id=certificate_id,
        new_status="active",
        operator_name=operator_name
    )
    if result["success"]:
        log_activity("SNR Claim", f"Operator {operator_name} claimed SNR {result['snr']}")
        return jsonify(result), 200(result)
    else:
        return jsonify(result), 400



@app.route('/api/snrs/<certificate_id>/confirm', methods=['PATCH'])
def confirm_snr_implementation(certificate_id):
    """
    Confirm that an SNR has been fully implemented in the operator's network.
    Updates status in SNR and operational records.
    """
    data = request.json
    operator_name = data.get('operator')
    
    if not operator_name:
        return jsonify({"error": "Operator name is required"}), 400
    
    # Start a session for transaction
    with mongo.cx.start_session() as session:
        with session.start_transaction():
            # 1. Find the SNR by certificate_id
            snr_record = mongo.db.snrs.find_one(
                {"certificate_id": certificate_id},
                {"_id": 0, "snr": 1, "implementing_operator": 1},
                session=session
            )
            
            if not snr_record:
                return jsonify({"error": "Certificate ID not found"}), 404
            
            snr_number = snr_record.get("snr")
            
            # Verify the operator is the one who claimed it
            if snr_record.get("implementing_operator") and snr_record.get("implementing_operator") != operator_name:
                return jsonify({
                    "error": f"This SNR was claimed by {snr_record.get('implementing_operator')}, not {operator_name}"
                }), 403
            
            # 2. Update the SNR record
            result = mongo.db.snrs.update_one(
                {"certificate_id": certificate_id},
                {"$set": {
                    "implementation_status": "implemented",
                    "implementation_completed": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }},
                session=session
            )
            
            if result.modified_count == 0:
                return jsonify({"error": "Failed to update SNR status"}), 500
            
            # 3. Update SNR operational record
            mongo.db.snr_operations.update_one(
                {"certificate_id": certificate_id},
                {"$set": {
                    "implementation_status": "active",
                    "activation_date": datetime.now(timezone.utc),
                    "last_updated": datetime.now(timezone.utc)
                }},
                session=session
            )
            
            # 4. Update the current audit record for this operator
            current_year = datetime.now().year
            current_quarter = f"Q{(datetime.now().month-1)//3+1}"
            
            # Try to find an existing audit record
            audit_record = mongo.db.audits.find_one(
                {
                    "operator": operator_name,
                    "year": current_year,
                    "quarter": current_quarter
                },
                session=session
            )
            
            if audit_record:
                # If we have active_snrs field, update it
                if "active_snrs" in audit_record:
                    # Check if this SNR is already in active_snrs
                    snr_exists = False
                    for snr in audit_record.get("active_snrs", []):
                        if snr.get("snr") == snr_number:
                            snr_exists = True
                            break
                    
                    if not snr_exists:
                        # Get purpose from SNR record
                        snr_details = mongo.db.snrs.find_one(
                            {"snr": snr_number},
                            {"_id": 0, "purpose": 1, "type": 1},
                            session=session
                        )
                        
                        # Add to active_snrs array and increment count
                        mongo.db.audits.update_one(
                            {"_id": audit_record["_id"]},
                            {"$push": {
                                "active_snrs": {
                                    "snr": snr_number,
                                    "status": "active",
                                    "activated_date": datetime.now(timezone.utc),
                                    "purpose": snr_details.get("purpose", "") if snr_details else "",
                                    "certificate_id": certificate_id,
                                    "type": snr_details.get("type", "")
                                }
                            },
                            "$inc": {"active_snrs_count": 1}},
                            session=session
                        )
            
            # 5. Log the activity
            activity_data = {
                "user_id": "API",
                "action": "SNR Implementation",
                "details": f"Operator {operator_name} confirmed implementation of SNR {snr_number}",
                "timestamp": datetime.now(timezone.utc),
                "snr": snr_number,
                "operator": operator_name,
                "certificate_id": certificate_id
            }
            
            mongo.db.activity_log.insert_one(activity_data, session=session)
    
    # Notify regulators and other stakeholders
    notify_operators({
        "event": "snr_implemented",
        "snr": snr_number,
        "operator": operator_name,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })
    
    return jsonify({
        "message": "SNR implementation confirmed successfully.",
        "snr": snr_number,
        "operator": operator_name,
        "status": "Implemented"
    }), 200

@app.route('/api/snrs/<certificate_id>/deactivate', methods=['POST'])
def deactivate_snr(certificate_id):
    """
    Mark an SNR as deactivated/revoked in the operator's network.
    Updates both SNR record and audit data.
    """
    data = request.json
    operator_name = data.get('operator')
    reason = data.get('reason', 'Not specified')
    
    if not operator_name:
        return jsonify({"error": "Operator name is required"}), 400
    
    # Start a session for transaction
    with mongo.cx.start_session() as session:
        with session.start_transaction():
            # 1. Find the SNR by certificate_id
            snr_record = mongo.db.snrs.find_one(
                {"certificate_id": certificate_id},
                {"_id": 0, "snr": 1, "implementing_operator": 1, "type": 1},
                session=session
            )
            
            if not snr_record:
                return jsonify({"error": "Certificate ID not found"}), 404
            
            snr_number = snr_record.get("snr")
            snr_type = snr_record.get("type")
            
            # Verify the operator is the one who implemented it (if set)
            if snr_record.get("implementing_operator") and snr_record.get("implementing_operator") != operator_name:
                return jsonify({
                    "error": f"This SNR was implemented by {snr_record.get('implementing_operator')}, not {operator_name}"
                }), 403
            
            # 2. Update the SNR record
            result = mongo.db.snrs.update_one(
                {"certificate_id": certificate_id},
                {"$set": {
                    "implementation_status": "deactivated",
                    "deactivation_date": datetime.now(timezone.utc),
                    "deactivation_reason": reason,
                    "updated_at": datetime.now(timezone.utc)
                }},
                session=session
            )
            
            if result.modified_count == 0:
                return jsonify({"error": "Failed to update SNR status"}), 500
            
            # 3. Update SNR operational record
            mongo.db.snr_operations.update_one(
                {"certificate_id": certificate_id},
                {"$set": {
                    "implementation_status": "deactivated",
                    "deactivation_date": datetime.now(timezone.utc),
                    "deactivation_reason": reason,
                    "last_updated": datetime.now(timezone.utc)
                }},
                session=session
            )
            
            # 4. Update the current audit record for this operator
            current_year = datetime.now().year
            current_quarter = f"Q{(datetime.now().month-1)//3+1}"
            
            # Find the audit record
            audit_record = mongo.db.audits.find_one(
                {
                    "operator": operator_name,
                    "year": current_year,
                    "quarter": current_quarter
                },
                session=session
            )
            
            if audit_record and "active_snrs" in audit_record:
                # First, find if the SNR is in active_snrs
                snr_index = None
                for i, snr in enumerate(audit_record.get("active_snrs", [])):
                    if snr.get("snr") == snr_number:
                        snr_index = i
                        break
                
                if snr_index is not None:
                    # Get the SNR record from active_snrs
                    active_snr = audit_record["active_snrs"][snr_index]
                    
                    # Create a deactivated record
                    deactivated_record = {
                        "snr": active_snr["snr"],
                        "status": "deactivated",
                        "deactivation_date": datetime.now(timezone.utc),
                        "reason": reason,
                        "purpose": active_snr.get("purpose", ""),
                        "certificate_id": certificate_id,
                        "type": active_snr.get("type", snr_type)
                    }
                    
                    # Update the audit record
                    mongo.db.audits.update_one(
                        {"_id": audit_record["_id"]},
                        {
                            "$pull": {"active_snrs": {"snr": snr_number}},
                            "$push": {"deactivated_snrs": deactivated_record},
                            "$inc": {
                                "active_snrs_count": -1,
                                "deactivated_snrs_count": 1
                            }
                        },
                        session=session
                    )
            
            # 5. Log the activity
            activity_data = {
                "user_id": "API",
                "action": "SNR Deactivation",
                "details": f"Operator {operator_name} deactivated SNR {snr_number}. Reason: {reason}",
                "timestamp": datetime.now(timezone.utc),
                "snr": snr_number,
                "operator": operator_name,
                "certificate_id": certificate_id,
                "reason": reason
            }
            
            mongo.db.activity_log.insert_one(activity_data, session=session)
    
    # Notify regulators of the deactivation
    notify_operators({
        "event": "snr_deactivated",
        "snr": snr_number,
        "operator": operator_name,
        "reason": reason,
        "timestamp": datetime.now(timezone.utc).isoformat()
    })
    
    return jsonify({
        "message": "SNR deactivated successfully.",
        "snr": snr_number,
        "operator": operator_name,
        "status": "Deactivated"
    }), 200

@app.route('/api/operator/snrs', methods=['GET'])
def get_operator_snrs():
    """
    Get all SNRs associated with a specific operator.
    Supports filtering by status and type.
    """
    operator = request.args.get('operator')
    status = request.args.get('status')  # active, implementing, deactivated
    snr_type = request.args.get('type')  # shortcode, tollfree, premium
    
    if not operator:
        return jsonify({"error": "Operator parameter is required"}), 400
    
    # Build query
    query = {"assigned_operator": operator}
    if status:
        query["implementation_status"] = status
    if snr_type:
        query["type"] = snr_type
    
    # Get SNRs from operational records
    snrs = list(mongo.db.snr_operations.find(
        query,
        {
            "_id": 0,
            "snr": 1,
            "type": 1,
            "implementation_status": 1,
            "claim_date": 1,
            "activation_date": 1,
            "deactivation_date": 1,
            "certificate_id": 1,
            "application_id": 1
        }
    ).sort("last_updated", -1))
    
    # Join with application and SNR data for additional details
    enhanced_snrs = []
    for snr in snrs:
        # First get the SNR record for purpose
        snr_detail = mongo.db.snrs.find_one(
            {"snr": snr["snr"]},
            {"_id": 0, "purpose": 1}
        )
        
        # Then get the application record for company info
        app = mongo.db.applications.find_one(
            {"application_id": snr["application_id"]},
            {"_id": 0, "applicant_name": 1, "company": 1}
        )
        
        if snr_detail:
            snr["purpose"] = snr_detail.get("purpose", "")
        
        if app:
            snr["applicant"] = app.get("applicant_name", "")
            snr["company"] = app.get("company", "")
        
        enhanced_snrs.append(snr)
    
    return jsonify(enhanced_snrs)

@app.route('/api/operators/audit/summary', methods=['GET'])
def get_operator_audit_summary():
    """
    Get a summary of SNR audit data for all operators or a specific operator.
    """
    operator = request.args.get('operator')
    year = request.args.get('year', datetime.now().year)
    quarter = request.args.get('quarter')
    
    # Build match criteria
    match = {}
    if operator:
        match["operator"] = operator
    if year:
        match["year"] = int(year)
    if quarter:
        match["quarter"] = quarter
    
    # Aggregation pipeline for summary statistics
    pipeline = [
        {"$match": match},
        {"$group": {
            "_id": "$operator",
            "total_active_snrs": {"$sum": "$active_snrs_count"},
            "total_deactivated_snrs": {"$sum": "$deactivated_snrs_count"},
            "avg_utilization_rate": {"$avg": "$processed.utilization_rate"},
            "total_numbers": {"$sum": "$tan"},
            "quarters_reported": {"$addToSet": "$quarter"}
        }},
        {"$project": {
            "operator": "$_id",
            "_id": 0,
            "total_active_snrs": 1,
            "total_deactivated_snrs": 1,
            "avg_utilization_rate": 1,
            "total_numbers": 1,
            "quarters_reported": 1,
            "quarters_count": {"$size": "$quarters_reported"}
        }},
        {"$sort": {"total_active_snrs": -1}}
    ]
    
    summary = list(mongo.db.audits.aggregate(pipeline))
    return jsonify(summary)

def update_audit_snr_counts(operator_name=None):
    """
    Update the SNR counts in audit records based on operational data.
    Can be run for a specific operator or all operators.
    
    This function can be called periodically to ensure audit data 
    matches operational reality.
    """
    current_year = datetime.now().year
    current_quarter = f"Q{(datetime.now().month-1)//3+1}"
    
    # Build query for audits
    audit_query = {
        "year": current_year,
        "quarter": current_quarter
    }
    
    if operator_name:
        audit_query["operator"] = operator_name
    
    # Get all relevant audit records
    audit_records = mongo.db.audits.find(audit_query)
    
    updates = []
    for audit in audit_records:
        operator = audit.get("operator")
        
        # Get active SNRs for this operator from operational records
        active_snrs = list(mongo.db.snr_operations.find(
            {
                "assigned_operator": operator,
                "implementation_status": "active"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "certificate_id": 1,
                "application_id": 1,
                "activation_date": 1
            }
        ))
        
        # Get deactivated SNRs
        deactivated_snrs = list(mongo.db.snr_operations.find(
            {
                "assigned_operator": operator,
                "implementation_status": "deactivated"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "certificate_id": 1,
                "application_id": 1,
                "deactivation_date": 1,
                "deactivation_reason": 1
            }
        ))
        
        # Prepare SNR records for audit
        active_snr_records = []
        for snr in active_snrs:
            # Get SNR details
            snr_detail = mongo.db.snrs.find_one(
                {"snr": snr["snr"]},
                {"_id": 0, "purpose": 1}
            )
            
            active_snr_records.append({
                "snr": snr["snr"],
                "status": "active",
                "activated_date": snr.get("activation_date", datetime.now(timezone.utc)),
                "purpose": snr_detail.get("purpose", "") if snr_detail else "",
                "certificate_id": snr.get("certificate_id", ""),
                "type": snr.get("type", "")
            })
        
        deactivated_snr_records = []
        for snr in deactivated_snrs:
            # Get SNR details
            snr_detail = mongo.db.snrs.find_one(
                {"snr": snr["snr"]},
                {"_id": 0, "purpose": 1}
            )
            
            deactivated_snr_records.append({
                "snr": snr["snr"],
                "status": "deactivated",
                "deactivation_date": snr.get("deactivation_date", datetime.now(timezone.utc)),
                "reason": snr.get("deactivation_reason", "Not specified"),
                "purpose": snr_detail.get("purpose", "") if snr_detail else "",
                "certificate_id": snr.get("certificate_id", ""),
                "type": snr.get("type", "")
            })
        
        # Update the audit record
        mongo.db.audits.update_one(
            {"_id": audit["_id"]},
            {"$set": {
                "active_snrs": active_snr_records,
                "deactivated_snrs": deactivated_snr_records,
                "active_snrs_count": len(active_snr_records),
                "deactivated_snrs_count": len(deactivated_snr_records),
                "last_synchronized": datetime.now(timezone.utc)
            }}
        )
        
        updates.append({
            "operator": operator,
            "active_count": len(active_snr_records),
            "deactivated_count": len(deactivated_snr_records)
        })
    
    return updates

@app.route('/webhook/register', methods=['POST'])
def register_webhook():
    """
    Register a webhook URL for notifications about SNR status changes.
    """
    data = request.json
    operator_name = data.get('operator')
    webhook_url = data.get('webhook_url')
    events = data.get('events', ['snr_approved', 'snr_claimed', 'snr_implemented', 'snr_deactivated'])
    
    if not operator_name or not webhook_url:
        return jsonify({"error": "Operator name and webhook URL are required"}), 400
    
    # Validate URL format
    try:
        result = requests.head(webhook_url, timeout=5)
    except requests.RequestException:
        return jsonify({"error": "Invalid webhook URL or endpoint not reachable"}), 400
    
    # Store the webhook configuration
    webhook_config = {
        "operator": operator_name,
        "webhook_url": webhook_url,
        "events": events,
        "created_at": datetime.now(timezone.utc),
        "last_updated": datetime.now(timezone.utc),
        "active": True
    }
    
    mongo.db.webhooks.update_one(
        {"operator": operator_name},
        {"$set": webhook_config},
        upsert=True
    )
    
    log_activity("Webhook Registration", f"Operator {operator_name} registered webhook for events: {', '.join(events)}")
    
    return jsonify({
        "message": "Webhook registered successfully.",
        "operator": operator_name,
        "events": events
    }), 200

def notify_operators(event_data):
    """
    Send notifications to registered webhooks based on event type.
    Enhanced with better error handling and selective notifications.
    """
    event_type = event_data.get('event')
    
    # Get webhooks subscribed to this event
    webhooks = mongo.db.webhooks.find({
        "active": True,
        "events": event_type
    })
    
    # Track successful and failed notifications
    results = {
        "successful": [],
        "failed": []
    }
    
    for webhook in webhooks:
        operator = webhook.get('operator')
        url = webhook.get('webhook_url')
        
        try:
            # Add timestamp and webhook ID to the payload
            notification_data = {
                **event_data,
                "notification_id": str(ObjectId()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Send the notification with timeout
            response = requests.post(
                url, 
                json=notification_data,
                timeout=5,
                headers={"Content-Type": "application/json"}
            )
            
            # Log notification attempt
            log_entry = {
                "webhook_id": str(webhook.get('_id')),
                "operator": operator,
                "event": event_type,
                "notification_data": notification_data,
                "status_code": response.status_code,
                "timestamp": datetime.now(timezone.utc),
                "successful": 200 <= response.status_code < 300
            }
            
            mongo.db.webhook_logs.insert_one(log_entry)
            
            # Track result
            if 200 <= response.status_code < 300:
                results["successful"].append(operator)
            else:
                results["failed"].append({
                    "operator": operator,
                    "status_code": response.status_code,
                    "reason": response.text[:100]  # Truncate long error messages
                })
                
        except requests.RequestException as e:
            # Log the failure
            log_entry = {
                "webhook_id": str(webhook.get('_id')),
                "operator": operator,
                "event": event_type,
                "notification_data": event_data,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc),
                "successful": False
            }
            
            mongo.db.webhook_logs.insert_one(log_entry)
            
            # Track failure
            results["failed"].append({
                "operator": operator,
                "error": str(e)
            })
    
    return results

# API endpoint to run the SNR audit update
@app.route('/api/snrs/audit/update', methods=['POST'])
@login_required
@admin_required
def run_snr_audit_update():
    """
    API endpoint to manually trigger SNR audit update.
    """
    data = request.json
    operator_name = data.get('operator')
    
    try:
        updates = update_audit_snr_counts(operator_name)
        log_activity("SNR Audit Update", f"User {current_user.contact} triggered SNR audit update for {operator_name or 'all operators'}")
        return jsonify({
            "message": f"Updated SNR audit counts for {len(updates)} operators",
            "updates": updates
        }), 200
    except Exception as e:
        log_activity("SNR Audit Update Error", f"Error updating SNR audit counts: {str(e)}")
        return jsonify({"error": f"Error updating SNR audit counts: {str(e)}"}), 500

# /////////END OF OPERATOR-SIDE OPERATIONS/////////////




#//////////CODE FOR HANDLING USER PAGES /////////////

# Route: Apply for a Single Number

@app.route('/apply/<snr>', methods=['GET', 'POST'])
@login_required
def apply(snr):
    if request.method == 'POST':
        purpose = request.form.get('purpose', '').strip()
        if not purpose:
            flash('Purpose is required.', 'danger')
            return redirect(url_for('apply', snr=snr))
        
        # Store in session for confirmation
        session['pending_application'] = {
            'snr': snr,
            'purpose': purpose,
            'application_id': generate_ids()[0]  # Use your ID generator
        }
        
        return redirect(url_for('confirm_apply'))
    
    return render_template('apply.html', snr=snr)


def generate_ids():
    """
    Generate unique application and certificate IDs.
    """
    application_id = f"APP{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
    certificate_id = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
    return application_id, certificate_id


@app.route('/confirm_apply', methods=['GET', 'POST'])
@login_required
def confirm_apply():
    # Retrieve pending application from Flask's session
    pending_app = session.get('pending_application')
    
    if not pending_app:
        flash('No pending application to confirm.', 'danger')
        return redirect(url_for('search'))

    # For GET requests, render the review page with the new template
    if request.method == 'GET':
        # Ensure that the stored SNR is a list.
        # (If not, convert it to one.)
        if not isinstance(pending_app.get('snr'), list):
            pending_app['snr'] = [pending_app['snr']]
            session['pending_application'] = pending_app

        # Generate a certificate ID if not already present.
        if 'certificate_id' not in pending_app:
            # generate_ids() returns (application_id, certificate_id)
            _, certificate_id = generate_ids()
            pending_app['certificate_id'] = certificate_id
            session['pending_application'] = pending_app

        # Determine SNR type based on the first number in the list.
        first_snr = pending_app['snr'][0] if pending_app['snr'] else ""
        if len(first_snr) <= 6:
            snr_type = "shortcode"
        elif first_snr.startswith("0800"):
            snr_type = "tollfree"
        else:
            snr_type = "premium"

        # Prepare snr_info as a list with a single dictionary.
        snr_info = [{
            'snr': pending_app['snr'],  # a list of numbers
            'type': snr_type,
            'purpose': pending_app['purpose'],
            'status': "Pending Review",
            'certificate_id': pending_app['certificate_id']
        }]

        return render_template(
            'review_single_application.html',
            applicant_name=current_user.contact,
            email=current_user.email,
            phone=current_user.phone,
            company=current_user.company,
            application_date=datetime.now(timezone.utc).strftime('%Y-%m-%d'),
            snr_info=snr_info,
            application_id=pending_app['application_id']
        )

    # For POST requests, process the confirmation and persist to the database.
    if request.method == 'POST':
        try:
            # Use the IDs stored in the session.
            application_id = pending_app['application_id']
            certificate_id = pending_app['certificate_id']

            # Create application document (metadata)
            application_doc = {
                "application_id": application_id,
                "user_id": current_user.id,
                "applicant_name": current_user.contact,
                "email": current_user.email,
                "phone": current_user.phone,
                "company": current_user.company,
                "application_date": datetime.now(timezone.utc).strftime('%Y-%m-%d'),
                "status": "Pending Review",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Determine SNR type based on the first number
            first_snr = pending_app['snr'][0] if pending_app['snr'] else ""
            if len(first_snr) <= 6:
                snr_type = "shortcode"
            elif first_snr.startswith("0800"):
                snr_type = "tollfree"
            else:
                snr_type = "premium"

            # Create SNR document (specific details)
            snr_doc = {
                "application_id": application_id,
                "snr": pending_app['snr'],  # now a list of numbers
                "type": snr_type,
                "purpose": pending_app['purpose'],
                "status": "Pending Review",
                "certificate_id": certificate_id,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Use transaction for atomic operation (rename MongoDB session variable)
            with mongo.cx.start_session() as mongo_session:
                with mongo_session.start_transaction():
                    # Get the default database instance
                    db = mongo.cx.get_default_database()
                    
                    # Insert application metadata
                    db.applications.insert_one(application_doc, session=mongo_session)
                    
                    # Insert SNR details
                    db.snrs.insert_one(snr_doc, session=mongo_session)
                    
                    # Update status collection
                    status_collection = get_status_table(pending_app['snr'][0])
                    db[status_collection].update_one(
                        {'SNR': pending_app['snr'][0]},
                        {'$set': {
                            'STATUS': 'PENDING REVIEW',
                            'ASSIGNEE': current_user.company,
                            'EXPIRY': '',  # Clear previous expiry if exists
                            'UPDATED_AT': datetime.now(timezone.utc)
                        }},
                        upsert=True,
                        session=mongo_session
                    )




            # Clear pending application from Flask's session
            session.pop('pending_application', None)

            # Log activity
            log_activity("Application Submitted", 
                         f"User {current_user.contact} confirmed application for {pending_app['snr'][0]}")

            flash('Application submitted successfully!', 'success')
            return redirect(url_for('application_details', application_id=application_id))

        except Exception as e:
            flash(f'Error submitting application: {str(e)}', 'danger')
            return redirect(url_for('apply', snr=pending_app['snr'][0]))



def calculate_fee(snr):
    """Calculate application fee based on number type"""
    if len(snr) == 3:
        return 2100  # 3-digit shortcode fee
    elif len(snr) == 4:
        return 500   # 4-digit shortcode fee
    elif snr.startswith('0800'):
        return 40    # Toll-free fee
    elif snr.startswith('0900'):
        return 100   # Premium-rate fee
    return 250       # Default fee for other numbers

def get_status_table(snr):
    """Determine which status collection to update"""
    if snr.startswith('0800'):
        return 'tf_status'
    elif snr.startswith('0900'):
        return 'pr_status'
    else:
        return 'snr_status'


@app.route('/apply_multiple/preview', methods=['POST'])
@login_required
def apply_multiple_preview():
    if current_user.is_admin:
        log_activity("Unapproved Application Bypass Attempt", f"User {current_user.contact} attempted applying as an Admin.")
        flash('You cannot apply using an Admin account. Please use the right Applicant account.', 'danger')

        return redirect(url_for('search'))
    # Retrieve data from the hidden inputs
    purposes_json = request.form.get('purposes')

    if not purposes_json:
        flash('Please provide a purpose for the selected numbers.', 'warning')
        return redirect(url_for('search'))
    

    # Parse purposes JSON
    try:
        purposes = json.loads(purposes_json)

    except json.JSONDecodeError as e:
        flash(f'Error parsing purposes: {e}', 'danger')
        return redirect(url_for('search'))


    # Validate purposes and SNRs
    for purpose_group in purposes:
        purpose_text = purpose_group.get('purpose', '').strip()
        snrs_list = purpose_group.get('snrs', [])
        print(f"Purpose: {purpose_text}, SNRs: {snrs_list}")  # Debugging line

        if not purpose_text:
            flash('Each purpose must have a valid description.', 'warning')
            return redirect(url_for('search', from_review=1))
        if not snrs_list:
            flash('Each purpose must have at least one associated number.', 'warning')
            return redirect(url_for('search', from_review=1))



    # Generate unique application ID for preview
    application_id = f"APP{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"



    # Prepare list of SNRs for the application
    snrs = []
    for purpose_group in purposes:
        purpose_text = purpose_group['purpose']
        snrs_list = purpose_group['snrs']
        
        for snr in snrs_list:
            certificate_id = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
            snrs.append({
                "snr": snr,
                "type": "shortcode" if len(snr) <= 6 else ("tollfree" if snr.startswith('0800') else "premium"),
                "purpose": purpose_text,
                "status": "Pending Review",
                "certificate_id": certificate_id
            })



    # Save the search results and the application details in the session to allow going back
    session['application_id'] = application_id
    session['snrs'] = snrs


    log_activity("Final Application Preview", f"User {current_user.contact} reviewed their application for multiple snrs.")

    # Store COMPLETE application data in session
    session['pending_application'] = {
        'application_id': application_id,
        'snrs': snrs,
        'purposes': purposes  # Store original purposes structure
    }

    # Render review page with the gathered details
    return render_template(
        'review_multiple_application.html',
        application_id=application_id,
        applicant_name=current_user.contact,
        email=current_user.email,
        phone=current_user.phone,
        contact=current_user.contact,
        company=current_user.company,
        application_date=datetime.now(timezone.utc).strftime('%Y-%m-%d'),
        snrs=snrs
    )


@app.route('/apply_multiple/confirm', methods=['POST'])
@login_required
def confirm_apply_multiple():
    # try:
    application_id = request.form['application_id']
    pending_app = session.get('pending_application')
    
    # Validate session data
    if not pending_app or pending_app['application_id'] != application_id:
        flash('Invalid or expired application session', 'danger')
        return redirect(url_for('search'))
        
    # Use session-stored data instead of form data
    purposes = pending_app['purposes']
    # snrs = pending_app['snrs']


    certificate_id_base = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}"

    # Parse and validate input data
    # purposes = json.loads(request.form['purposes'])
    if not isinstance(purposes, list) or len(purposes) == 0:
        flash('Invalid request format', 'danger')
        return redirect(url_for('search'))

    # Create main application document
    application_doc = {
        "application_id": application_id,
        "user_id": current_user.id,
        "applicant_name": current_user.contact,
        "email": current_user.email,
        "phone": current_user.phone,
        "company": current_user.company,
        "application_date": datetime.now(timezone.utc).strftime('%Y-%m-%d'),
        "status": "Pending Review",
        "remarks": f"{current_user.company} requested {len(purposes)} number groups",
        # "snrs": [],  # Will contain embedded SNR data
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }

    # Prepare SNR documents
    snr_docs = []
    for idx, purpose_group in enumerate(purposes, 1):
        for snr in purpose_group['snrs']:
            # Generate unique certificate ID for each SNR
            certificate_id = f"{certificate_id_base}-{idx}-{snr}"
            
            snr_doc = {
                "application_id": application_id,
                "snr": snr,
                "type": "shortcode" if len(snr) <= 6 else 
                        "tollfree" if snr.startswith('0800') else 
                        "premium",
                "purpose": purpose_group['purpose'],
                "status": "Pending Review",
                "certificate_id": certificate_id,
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }
            
            # Add to both embedded and separate collections
            # application_doc['snrs'].append(snr_doc)
            snr_docs.append(snr_doc)

    # Database operations using transaction
    with mongo.cx.start_session() as mongo_session:
        with mongo_session.start_transaction():
            # Get the default database instance
            db = mongo.cx.get_default_database()

            # Insert application metadata
            db.applications.insert_one(application_doc, session=mongo_session)
            
            # Insert all SNR documents
            if snr_docs:
                db.snrs.insert_many(snr_docs, session=mongo_session)
            
            # Update status tables
            for snr_doc in snr_docs:
                status_table = get_status_table(snr_doc['snr'])
                db[status_table].update_one(
                    {'SNR': snr_doc['snr']},
                    {'$set': {
                        'STATUS': 'PENDING REVIEW',
                        'ASSIGNEE': current_user.company,
                        'EXPIRY': '',  # Clear previous expiry if exists
                        'UPDATED_AT': datetime.now(timezone.utc)
                    }},
                    upsert=True,
                    session=mongo_session
                )

            # Log activity for each SNR applied for in the group application

    log_activity("Multiple Application Submitted",
                f"User {current_user.contact} applied for {len(snr_docs)} numbers")

    # Clear session data after successful submission
    session.pop('pending_application', None)

    flash('Application submitted successfully!', 'success')
    return redirect(url_for('application_details', application_id=application_id))


        
    # except Exception as e:
    #     # Handle errors and maintain session data for recovery
    #     flash(f'Application failed: {str(e)}', 'danger')
    #     return redirect(url_for('search'))






# @app.route('/dashboard')
# @login_required
# def dashboard():
#     log_activity("User Applications Dashboard", f"User {current_user.contact} viewed their applications dashboard.")
#     # Fetch user applications
#     applications = mongo.db.applications.find({'user_id': str(current_user.id)})
#     return render_template('dashboard.html', applications=applications)

@app.route('/dashboard')
@login_required
def dashboard():
    log_activity("User Applications Dashboard", f"User {current_user.contact} viewed their applications dashboard.")
    
    # Get filter parameters
    search_query = request.args.get('search', '')
    sort_field = request.args.get('sort', 'application_date')
    sort_order = int(request.args.get('order', -1))  # -1 for descending
    
    # Base pipeline
    pipeline = [
        {"$match": {"user_id": str(current_user.id)}},
        {"$lookup": {
            "from": "snrs",
            "localField": "application_id",
            "foreignField": "application_id",
            "as": "snr_details"
        }}
    ]

    # Add search filter
    if search_query:
        pipeline.insert(1, {"$match": {
            "$or": [
                {"application_id": {"$regex": f".*{search_query}.*", "$options": "i"}},
                {"snr_details.snr": {"$regex": f".*{search_query}.*", "$options": "i"}},
                {"snr_details.purpose": {"$regex": f".*{search_query}.*", "$options": "i"}},
                {"snr_details.status": {"$regex": f".*{search_query}.*", "$options": "i"}}
            ]
        }})

    # Add sorting
    pipeline.append({"$sort": {sort_field: sort_order}})

    # Add aggregation stages
    pipeline.extend([
        {"$addFields": {
            "total_numbers": {"$size": "$snr_details"},
            "status_summary": {
                "$arrayToObject": {
                    "$reduce": {
                        "input": "$snr_details.status",
                        "initialValue": [],
                        "in": {
                            "$concatArrays": [
                                "$$value",
                                [{
                                    "k": "$$this",
                                    "v": {"$sum": [{"$cond": [{"$eq": ["$$this", "$$value.k"]}, 1, 0]}]}
                                }]
                            ]
                        }
                    }
                }
            }
        }},
        {"$project": {
            "application_id": 1,
            "application_date": 1,
            "total_numbers": 1,
            "status_summary": 1,
            "snr_details": 1
        }}
    ])

    applications = list(mongo.db.applications.aggregate(pipeline))
    
    # Process data
    for app in applications:
        app['_id'] = str(app['_id'])
        #convert the application_date which is a datetime.datetime object to a datetime object and back to a string in the desired format

    


        app['application_date'] = app['application_date']
        app['purposes'] = {}
        
        # Group by purpose
        for snr in app.get('snr_details', []):
            purpose = snr.get('purpose', 'Unknown')
            if purpose not in app['purposes']:
                app['purposes'][purpose] = {'count': 0, 'statuses': set()}
            app['purposes'][purpose]['count'] += 1
            app['purposes'][purpose]['statuses'].add(snr.get('status', 'Unknown'))
        
        # Convert sets to lists
        for purpose in app['purposes'].values():
            purpose['statuses'] = list(purpose['statuses'])

    return render_template('dashboard.html', 
                         applications=applications,
                         current_sort=sort_field,
                         current_order=sort_order,
                         search_query=search_query)



@app.route('/application_details/<application_id>', methods=['GET'])
@login_required
def application_details(application_id):
    log_activity("User Application Details", f"User {current_user.contact} viewed details of application {application_id}.")

    # Fetch application with all SNR details
    application = mongo.db.applications.find_one({"application_id": application_id})
    if not application:
        flash('Application not found.', 'warning')
        return redirect(url_for('dashboard'))
    
    # Convert ObjectId and dates
    application['_id'] = str(application['_id'])
    application['created_at'] = application['created_at'].strftime('%Y-%m-%d %H:%M:%S')
    application['updated_at'] = application['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
    
    # Fetch all related SNRs
    snrs = list(mongo.db.snrs.find({"application_id": application_id}))
    for snr in snrs:
        snr['_id'] = str(snr['_id'])
        snr['created_at'] = snr['created_at'].strftime('%Y-%m-%d %H:%M:%S')
        snr['updated_at'] = snr['updated_at'].strftime('%Y-%m-%d %H:%M:%S')
    
    application['snr_details'] = snrs

    return render_template('application_details.html', application=application)






# Addition

# @app.route('/update_account', methods=['GET', 'POST'])
# @login_required
# def update_account():
#     if request.method == 'POST':
#         # Update user details
#         email = request.form['email']
#         phone = request.form['phone']
#         # Validate and update in MongoDB
#         mongo.db.users.update_one(
#             {'_id': ObjectId(current_user.id)},
#             {'$set': {'email': email, 'phone': phone}}
#         )
#         flash('Account updated successfully.')
#         return redirect(url_for('update_account'))

#     # Fetch current user data
#     user_data = mongo.db.users.find_one({'_id': ObjectId(current_user.id)})

#     return render_template('update_account.html', user_data=user_data)




@app.route('/upload_csv', methods=['POST'])
@login_required
def upload_csv():
    if 'file' not in request.files:
        flash('No file part', 'danger')
        return redirect(url_for('dashboard'))

    file = request.files['file']
    
    try:
        # Read and process CSV/Excel file
        if file.filename.endswith('.csv'):
            df = pd.read_csv(file)
        else:
            df = pd.read_excel(file)

        # Create list to store enhanced results
        processed_results = []

        #convert all column names to lowercase and strip whitespace from ends of column names
        df.columns = [col.lower().strip() for col in df.columns]
        
        # Process each row in the CSV
        for _, row in df.iterrows():
            raw_number = str(row['number'])
            purpose = row.get('purpose', 'No purpose provided')
            
            # Clean and validate number
            clean_number = preprocess_snr(raw_number)
            if not clean_number:
                continue  # Skip invalid numbers
            print(f"Processing number: {clean_number}")

            # Create individual query for each number
            query = f"Check availability for: {clean_number}"
            collections = determine_collections(clean_number)
            print(f'collections for {clean_number}', collections)
            
            # Perform search operations for this number
            number_results = perform_search_operations(query, collections)
            print("Search results structure:", number_results)


            # Find matching result (use first match)
            main_result = next(
                (r['results'] for r in number_results 
                if str(r['results'].get('snr')) == clean_number),
                None
            )


    
            
            # Create enhanced result entry
            if main_result:
                print("Matched result:", main_result)
                # Extract availability from GPT-formatted structure
                availability = main_result.get('availability', 'Unknown')
                
                processed_result = {
                    'snr': raw_number,
                    'purpose': purpose,
                    'availability': availability,  # Use actual availability status
                    'source': 'csv',
                    'description': main_result.get('description', '')
                }
            else:
                print("No matching result found")
                processed_result = {
                    'snr': raw_number,
                    'purpose': purpose,
                    'availability': 'Unavailable',
                    'source': 'csv',
                    'description': f'Number {raw_number} not found in registry'
                }
            
            processed_results.append(processed_result)
        print('all results', processed_results)
        return render_template('search.html',
                             results=processed_results,
                             query=f"CSV Analysis - {len(df)} numbers processed")

    except Exception as e:
        flash(f'Error processing file: {str(e)}', 'danger')
        return redirect(url_for('/'))


@app.route('/process_form', methods=['POST'])
def process_form():
    if 'file' not in request.files:
        flash('No file uploaded', 'danger')
        return redirect(url_for('search'))

    file = request.files['file']
    
    if file.filename == '':
        flash('No selected file', 'danger')
        return redirect(url_for('search'))

    if not allowed_file(file.filename):
        flash('Invalid file type. Allowed: PNG, JPG, PDF', 'danger')
        return redirect(url_for('search'))

    # try:
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
        file.save(tmp_file.name)
        temp_path = tmp_file.name

    # Process with GPT to extract numbers and purpose
    ocr_result = process_form_with_gpt(temp_path)
    snr_list = ocr_result.get("numbers", [])
    purpose = ocr_result.get("purpose", "No purpose extracted")
    
    if not snr_list:
        flash('No telecom numbers found in document', 'warning')
        return render_template('search.html', results=[], query="")

    # Create search query from OCR results
    query = f"Check availability for: {', '.join(snr_list)} | Purpose: {purpose}"
    
    # Determine collections based on SNR patterns
    collections = set()
    for snr in snr_list:
        if len(snr) == 10:
            if snr.startswith('0800'):
                collections.update(["tf_status", "applications"])
            elif snr.startswith('0900'):
                collections.update(["pr_status", "applications"])
            else:
                collections.update(["snr_status", "applications"])
        else:
            collections.update(["snr_status", "applications"])

    modified_search = perform_search_operations(query, list(collections))

    # Enhance results with OCR purposes
    for idx, result in enumerate(modified_search):
        if idx < len(snr_list):
            result['purpose'] = purpose
        else:
            result['purpose'] = "Purpose not extracted"

    # In your process_form route after getting modified_search
# In your process_form route after getting modified_search
    for result in modified_search:
        # Get the purpose dictionary from the result
        purpose_dict = result.get('purpose', {})
        
        # Build the purpose string
        name = purpose_dict.get('program_name', 'Unnamed Program')
        description = purpose_dict.get('program_description', '')
        
        # Create combined purpose text
        purpose_text = name
        if description:
            purpose_text += f" - {description}"
        
        # Replace the purpose dictionary with the text
        result['purpose'] = purpose_text



    # Store in session
    session['search_query'] = query
    session['search_results'] = modified_search
    log_activity("OCR Search", f"User {current_user.contact} performed OCR search")

    # Directly render template with results
    if not modified_search:
        flash('No results found for your query.', 'info')
        return render_template('search.html', results=[], query=query)



    print(modified_search)
    return render_template('search.html', results=modified_search, query=query)

# except Exception as e:
#     flash(f'Document processing failed: {str(e)}', 'danger')
#     return redirect(url_for('search'))
    # finally:
    #     if 'temp_path' in locals() and os.path.exists(temp_path):
    #         try:
    #             os.remove(temp_path)
    #         except Exception as cleanup_error:
    #             print(f"Error cleaning up temp file: {cleanup_error}")


def process_form_with_gpt(application_form_path):
    """Process document using GPT-4 vision"""
    # try:
        # Encode image to base64
    with open(application_form_path, "rb") as image_file:
        base64_image = base64.b64encode(image_file.read()).decode('utf-8')
    
    response = openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[
            {
            "role": "system",
            "content": [
                {
                "type": "text",
                "text": "You are a helpful assistant, helping a team identify useful information from documents and images."
                }
            ]
            },
            {
            "role": "user",
            "content": [
                {
                "type": "text",
                "text": "the attached file is a complete application for a telecom numbering resource. please identify the numbers requested by the applicant, normally in the following fields \"Preferred SNR Requested\", \"First Alternative SNR Requested\", \"Second Alternative SNR Requested\". Also include the Program Name and Program description. return the results in the following format:\n{\n\"numbers\": [list of numbers in order of applicant's priority],\n\"purpose\":[program name and purpose]\n}\nIf multiple application forms request different numbers, expand the JSON output to include the other results."
                },
                {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{base64_image}"
                }
                }
            ]
            },
        ],
        response_format={"type": "json_object"},
        temperature=0.2,
        max_tokens=2048
    )
    
    # Parse and validate response
    result = json.loads(response.choices[0].message.content)
    print('checking the results', result)
    
    if not isinstance(result.get('numbers', []), list) or not result.get('purpose'):
        raise ValueError("Invalid GPT response format")
        
    return result
        
    # except Exception as e:
    #     raise RuntimeError(f"GPT processing failed: {str(e)}")
    




######NUMBERING AUDIT CODE######


# Define a custom Jinja2 filter for formatting numbers
@app.template_filter('format_number')
def format_number(value):
    if value is None:
        return "0"
    try:
        return "{:,}".format(int(value))
    except (ValueError, TypeError):
        return str(value)




@app.route('/logout')
@login_required
def logout():
    logout_user()
    log_activity("Logout", f"User {current_user.contact} logged out.")
    flash('You have been logged out.', 'success')
    return redirect(url_for('login'))


if __name__ == '__main__':
    app.run(debug=True)