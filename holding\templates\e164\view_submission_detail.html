<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ page_title }}</title> <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/e164-styles.css') }}">
    <style>
        /* Optional: Add specific styles for read-only view */
        .detail-card {
             background: rgba(255, 255, 255, 0.8);
             backdrop-filter: blur(5px);
             border-radius: 10px;
             padding: 25px;
             margin-bottom: 20px;
             box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        }
        .detail-label {
            font-weight: 600;
            color: #555;
        }
        .detail-value {
            margin-bottom: 0.5rem;
        }
        .table th { background-color: rgba(230, 230, 230, 0.5);}
    </style>
</head>
<body>
    <div class="container py-5">
         <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="mb-0"><i class="fas fa-file-alt me-2"></i> Submission Details</h1>
            <a href="{{ url_for('e164.dashboard', tab='history') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i> Back to History
            </a>
        </div>

        <div class="detail-card">
            <h3 class="mb-3">Submission Overview</h3>
             <div class="row">
                 <div class="col-md-6">
                     <p><span class="detail-label">Operator:</span> <span class="detail-value">{{ submission.operator }}</span></p>
                     <p><span class="detail-label">Reporting Period:</span> <span class="detail-value">{{ submission.reporting_period }}</span></p>
                     <p><span class="detail-label">Submitted At:</span> <span class="detail-value">{{ submission.created_at | format_datetime }}</span></p> {# Assumes you have a datetime filter #}
                     <p><span class="detail-label">Status:</span> <span class="badge bg-{{ submission.status|get_status_badge_class }}">{{ submission.status }}</span></p> {# Assumes status badge filter #}
                 </div>
                 <div class="col-md-6">
                     <p><span class="detail-label">Contact Person:</span> <span class="detail-value">{{ submission.contact_person | default('N/A') }}</span></p>
                     <p><span class="detail-label">Contact Email:</span> <span class="detail-value">{{ submission.contact_email | default('N/A') }}</span></p>
                     <p><span class="detail-label">Last Updated:</span> <span class="detail-value">{{ submission.updated_at | format_datetime }}</span></p>
                 </div>
                 <div class="col-12 mt-2">
                     <p><span class="detail-label">Notes:</span></p>
                     <p class="detail-value bg-light p-2 rounded">{{ submission.notes | default('No notes provided.') }}</p>
                 </div>
             </div>
        </div>

         {% if summary_metrics %}
         <div class="detail-card">
             <h3 class="mb-3">Summary Metrics</h3>
             <div class="row text-center">
                 <div class="col">
                     <span class="detail-label">Total Allocated</span><br><span class="fs-4">{{ summary_metrics.total_allocated | format_number }}</span>
                 </div>
                 <div class="col">
                     <span class="detail-label">Total Active</span><br><span class="fs-4">{{ summary_metrics.total_active | format_number }}</span>
                 </div>
                 <div class="col">
                     <span class="detail-label">Inactive</span><br><span class="fs-4">{{ summary_metrics.total_inactive | format_number }}</span>
                 </div>
                  <div class="col">
                     <span class="detail-label">Reserved</span><br><span class="fs-4">{{ summary_metrics.total_reserved | format_number }}</span>
                 </div>
                 <div class="col">
                     <span class="detail-label">Utilization</span><br><span class="fs-4">{{ "%.2f"|format(summary_metrics.utilization_rate) }}%</span>
                 </div>
             </div>
         </div>
        {% endif %}

        <div class="detail-card">
            <h3 class="mb-3">Submitted Number Ranges ({{ ranges | length }})</h3>
            {% if ranges %}
            <div class="table-responsive">
                <table class="table table-sm table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>NDC</th>
                            <th>Type</th>
                            <th>Start Block</th>
                            <th>End Block</th>
                            <th>Total Allocated</th>
                            <th>Active Subscriber</th>
                            <th>Active Non-Sub</th>
                            <th>Inactive</th>
                            <th>Reserved</th>
                            <th>Util %</th> {# Calculated in template or passed #}
                        </tr>
                    </thead>
                    <tbody>
                        {% for range in ranges %}
                        <tr>
                            <td>{{ range.ndc }}</td>
                            <td>{{ range.type }}</td>
                            <td>{{ range.start_block }}</td>
                            <td>{{ range.end_block }}</td>
                            <td>{{ range.total_allocated | format_number }}</td>
                            <td>{{ range.active_subscriber | format_number }}</td>
                            <td>{{ range.active_non_subscriber | format_number }}</td>
                            <td>{{ range.inactive | format_number }}</td>
                            <td>{{ range.reserved | format_number }}</td>
                            <td>{{ "%.2f"|format(range.utilization_rate) }}%</td> {# Assumes utilization_rate is passed #}
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
             <p class="text-muted">No number range data was submitted.</p>
            {% endif %}
        </div>

        <div class="detail-card">
             <h3 class="mb-3">Submitted Porting Details ({{ porting_details | length }})</h3>
             {% if porting_details %}
             <div class="table-responsive">
                 <table class="table table-sm table-bordered table-hover">
                      <thead>
                          <tr>
                              <th>NDC</th>
                              <th>Range Type</th>
                              <th>Source Operator</th>
                              <th>Target Operator</th>
                              <th>Count</th>
                          </tr>
                      </thead>
                      <tbody>
                          {% for porting in porting_details %}
                          <tr>
                              <td>{{ porting.ndc }}</td>
                              <td>{{ porting.range_type }}</td>
                              <td>{{ porting.source_operator }}</td>
                              <td>{{ porting.target_operator }}</td>
                              <td>{{ porting.count | format_number }}</td>
                          </tr>
                          {% endfor %}
                      </tbody>
                 </table>
             </div>
             {% else %}
             <p class="text-muted">No porting data was submitted.</p>
             {% endif %}
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    {# Add any necessary JS, probably none for a simple view #}
</body>
</html>