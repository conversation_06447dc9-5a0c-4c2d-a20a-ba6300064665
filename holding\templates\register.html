<!-- templates/register.html -->
{% extends "base.html" %}
{% block content %}
<div class="d-flex justify-content-center align-items-center min-vh-100">
    <div class="card glassmorphic-card p-4" style="width: 100%; max-width: 500px;">
        <div class="card-body">
            <h2 class="card-title text-center">Register</h2>
            <form method="POST" enctype="multipart/form-data" novalidate>
                {{ form.hidden_tag() }}
                <div class="form-group">
                    {{ form.vas_id.label(class="form-label") }}
                    {{ form.vas_id(class="form-control", placeholder="Enter your VAS ID") }}
                    {% for error in form.vas_id.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.email.label(class="form-label") }}
                    {{ form.email(class="form-control", placeholder="Enter your email") }}
                    {% for error in form.email.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="form-control", placeholder="Enter your password") }}
                    {% for error in form.password.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.confirm_password.label(class="form-label") }}
                    {{ form.confirm_password(class="form-control", placeholder="Confirm your password") }}
                    {% for error in form.confirm_password.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.contact.label(class="form-label") }}
                    {{ form.contact(class="form-control", placeholder="Enter your contact name") }}
                    {% for error in form.contact.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.phone.label(class="form-label") }}
                    {{ form.phone(class="form-control", placeholder="Enter your phone number") }}
                    {% for error in form.phone.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.national_id.label(class="form-label") }}
                    {{ form.national_id(class="form-control-file") }}
                    {% for error in form.national_id.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.photo.label(class="form-label") }}
                    {{ form.photo(class="form-control-file") }}
                    {% for error in form.photo.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <button type="submit" class="btn btn-primary btn-block mt-4">Register</button>
            </form>
            <p class="mt-3 text-center">Already have an account? <a href="{{ url_for('login') }}">Login here</a></p>
        </div>
    </div>
</div>
{% endblock %}
