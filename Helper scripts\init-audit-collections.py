# init_audit_collections.py
# Run this script once to set up the collections needed for the audit system

from app import mongo
from datetime import datetime

def init_audit_collections():
    """
    Initialize the MongoDB collections required for the audit system.
    This only needs to be run once to set up the initial structure.
    """
    # Create operators collection if it doesn't exist
    if 'operators' not in mongo.db.list_collection_names():
        operators = [
            {"name": "MTN Ghana", "code": "MTN", "contact_email": "<EMAIL>"},
            {"name": "AirtelTigo", "code": "ATG", "contact_email": "<EMAIL>"},
            {"name": "Telecel Ghana", "code": "TCL", "contact_email": "<EMAIL>"}
        ]
        mongo.db.operators.insert_many(operators)
        print("Created operators collection with sample data")
    
    # Create number_ranges collection if it doesn't exist
    if 'number_ranges' not in mongo.db.list_collection_names():
        ranges = [
            {
                "assignee": "MTN Ghana", 
                "ndc": "024", 
                "type": "Mobile",
                "start_block": 1000000,
                "end_block": 1999999
            },
            {
                "assignee": "MTN Ghana", 
                "ndc": "054", 
                "type": "Mobile",
                "start_block": 1000000,
                "end_block": 1999999
            },
            {
                "assignee": "Vodafone Ghana", 
                "ndc": "020", 
                "type": "Mobile",
                "start_block": 1000000,
                "end_block": 1999999
            },
            {
                "assignee": "AirtelTigo", 
                "ndc": "027", 
                "type": "Mobile",
                "start_block": 1000000,
                "end_block": 1999999
            },
            {
                "assignee": "Telecel Ghana", 
                "ndc": "050", 
                "type": "Mobile",
                "start_block": 1000000,
                "end_block": 1999999
            }
        ]
        mongo.db.number_ranges.insert_many(ranges)
        print("Created number_ranges collection with sample data")
    
    # Create audits collection if it doesn't exist
    if 'audits' not in mongo.db.list_collection_names():
        # Sample audit data
        audits = [
            {
                "operator": "MTN Ghana",
                "quarter": "Q1",
                "year": 2024,
                "sn_type": "Mobile",
                "ndc": "024",
                "start_block": 1000000,
                "end_block": 1999999,
                "tan": 1000000,
                "tasn": 780000,
                "active_non_sub": 30000,
                "tin": 120000,
                "inactive_sub": 100000,
                "inactive_non_sub": 20000,
                "trn": 70000,
                "reserved_sub": 50000,
                "reserved_non_sub": 20000,
                "gross_add": 35000,
                "net_add": 15000,
                "cpb": 10000,
                "cpo": 8000,
                "created_by": "admin",
                "timestamp": datetime.now(),
                "processed": {
                    "utilization_rate": 0.78,
                    "percent_inactive": 12.0,
                    "percent_reserved": 7.0,
                    "porting_balance": 2000
                },
                "snr_details": {
                    "total_snrs": 28,
                    "service_categories": ["USSD", "SMS", "Voice"],
                    "digit_categories": {
                        "3_digit": 3,
                        "4_digit": 10,
                        "5_digit": 12,
                        "6_digit": 3
                    }
                },
                "performance_scores": {
                    "utilization": 2.60,
                    "porting_balance": 2.95,
                    "inactive": 2.93,
                    "overall": 8.48
                }
            },
           
            {
                "operator": "Vodafone Ghana",
                "quarter": "Q1",
                "year": 2024,
                "sn_type": "Mobile",
                "ndc": "020",
                "start_block": 1000000,
                "end_block": 1999999,
                "tan": 1000000,
                "tasn": 720000,
                "active_non_sub": 40000,
                "tin": 150000,
                "inactive_sub": 120000,
                "inactive_non_sub": 30000,
                "trn": 90000,
                "reserved_sub": 70000,
                "reserved_non_sub": 20000,
                "gross_add": 25000,
                "net_add": 5000,
                "cpb": 8000,
                "cpo": 12000,
                "created_by": "admin",
                "timestamp": datetime.now(),
                "processed": {
                    "utilization_rate": 0.72,
                    "percent_inactive": 15.0,
                    "percent_reserved": 9.0,
                    "porting_balance": -4000
                },
                "snr_details": {
                    "total_snrs": 22,
                    "service_categories": ["USSD", "SMS", "Voice"],
                    "digit_categories": {
                        "3_digit": 2,
                        "4_digit": 8,
                        "5_digit": 10,
                        "6_digit": 2
                    }
                },
                "performance_scores": {
                    "utilization": 2.40,
                    "porting_balance": 2.33,
                    "inactive": 2.83,
                    "overall": 7.56
                }
            },
            {
                "operator": "AirtelTigo",
                "quarter": "Q1",
                "year": 2024,
                "sn_type": "Mobile",
                "ndc": "027",
                "start_block": 1000000,
                "end_block": 1999999,
                "tan": 1000000,
                "tasn": 650000,
                "active_non_sub": 50000,
                "tin": 200000,
                "inactive_sub": 160000,
                "inactive_non_sub": 40000,
                "trn": 100000,
                "reserved_sub": 80000,
                "reserved_non_sub": 20000,
                "gross_add": 18000,
                "net_add": -2000,
                "cpb": 7000,
                "cpo": 9000,
                "created_by": "admin",
                "timestamp": datetime.now(),
                "processed": {
                    "utilization_rate": 0.65,
                    "percent_inactive": 20.0,
                    "percent_reserved": 10.0,
                    "porting_balance": -2000
                },
                "snr_details": {
                    "total_snrs": 18,
                    "service_categories": ["USSD", "SMS"],
                    "digit_categories": {
                        "3_digit": 0,
                        "4_digit": 5,
                        "5_digit": 8,
                        "6_digit": 5
                    }
                },
                "performance_scores": {
                    "utilization": 2.16,
                    "porting_balance": 2.67,
                    "inactive": 2.67,
                    "overall": 7.50
                }
            },
            {
                "operator": "Telecel Ghana",
                "quarter": "Q1",
                "year": 2024,
                "sn_type": "Mobile",
                "ndc": "050",
                "start_block": 1000000,
                "end_block": 1999999,
                "tan": 1000000,
                "tasn": 580000,
                "active_non_sub": 60000,
                "tin": 250000,
                "inactive_sub": 200000,
                "inactive_non_sub": 50000,
                "trn": 110000,
                "reserved_sub": 80000,
                "reserved_non_sub": 30000,
                "gross_add": 15000,
                "net_add": -5000,
                "cpb": 5000,
                "cpo": 10000,
                "created_by": "admin",
                "timestamp": datetime.now(),
                "processed": {
                    "utilization_rate": 0.58,
                    "percent_inactive": 25.0,
                    "percent_reserved": 11.0,
                    "porting_balance": -5000
                },
                "snr_details": {
                    "total_snrs": 10,
                    "service_categories": ["USSD", "SMS"],
                    "digit_categories": {
                        "3_digit": 0,
                        "4_digit": 2,
                        "5_digit": 5,
                        "6_digit": 3
                    }
                },
                "performance_scores": {
                    "utilization": 1.93,
                    "porting_balance": 1.67,
                    "inactive": 2.50,
                    "overall": 6.10
                }
            }
        ]
        mongo.db.audits.insert_many(audits)
        print("Created audits collection with sample data")
    
    print("Audit collections initialization complete")

if __name__ == "__main__":
    init_audit_collections()
