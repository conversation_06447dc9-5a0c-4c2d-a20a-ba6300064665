#!/bin/bash
# This script processes multiple operator audit files and imports them to MongoDB

# Check for Python environment
if ! command -v python &> /dev/null; then
    echo "Python not found. Please install Python 3.6+ and try again."
    exit 1
fi

# Check for required Python packages
echo "Checking for required Python packages..."
python -c "import pandas, pymongo, numpy" 2>/dev/null || {
    echo "Installing required Python packages..."
    pip install pandas pymongo numpy
}

# Process AT Ghana data
echo "Processing AT Ghana data..."
python operator_audit_converter.py "AT FIRST HALF 2024 AUDIT SUMMARY.xlsx" "AT Ghana"

# Process Telecel Ghana data
echo "Processing Telecel Ghana data..."
python operator_audit_converter.py "TELECEL FIRST HALF 2024 AUDIT SUMMARY.xlsx" "Telecel Ghana"

# Import all JSON files to MongoDB
echo "Importing data to MongoDB..."
python mongodb-import-script.py AT_Ghana_audit_data.json Telecel_Ghana_audit_data.json

echo "Done! All data has been processed and imported to MongoDB."
