/**
 * Simple standalone tab fix for E.164 Number Range Audit system
 * This script can be added directly to any page to fix tab navigation
 */
document.addEventListener('DOMContentLoaded', function() {
    console.log("Tab fix script loaded");
    
    // First, get the active tab from the data attribute
    const activeTab = document.body.getAttribute('data-active-tab') || 'submission';
    console.log("Active tab:", activeTab);
    
    // Find all tab links
    const tabLinks = document.querySelectorAll('.nav-tabs .nav-link');
    
    // Remove 'active' class from all tabs
    tabLinks.forEach(tab => {
        tab.classList.remove('active');
        tab.setAttribute('aria-selected', 'false');
    });
    
    // Add 'active' class to the active tab
    const activeTabElement = document.getElementById(`${activeTab}-tab`);
    if (activeTabElement) {
        activeTabElement.classList.add('active');
        activeTabElement.setAttribute('aria-selected', 'true');
    }
    
    // Remove 'show active' class from all tab panes
    document.querySelectorAll('.tab-pane').forEach(pane => {
        pane.classList.remove('show', 'active');
    });
    
    // Add 'show active' class to the active tab pane
    const activeTabPane = document.getElementById(activeTab);
    if (activeTabPane) {
        activeTabPane.classList.add('show', 'active');
    }
    
    // Setup server navigation for tab links
    const tabRoutes = {
        'submission': '/e164/submission/new',
        'analytics': '/e164/analytics', 
        'history': '/e164/dashboard'
    };
    
    // Add click handlers for tabs
    tabLinks.forEach(tab => {
        tab.addEventListener('click', function(e) {
            // Get the tab ID
            const tabId = this.id.replace('-tab', '');
            
            // If the route should navigate to a different URL
            if (tabRoutes[tabId] && tabId !== activeTab) {
                // Add loading spinner
                const originalHTML = this.innerHTML;
                this.innerHTML += ' <span class="spinner-border spinner-border-sm"></span>';
                
                // Navigate after a short delay
                setTimeout(() => {
                    window.location.href = tabRoutes[tabId];
                }, 100);
            } else {
                // Just activate the tab locally (for same page use)
                // Remove 'active' class from all tabs
                tabLinks.forEach(t => {
                    t.classList.remove('active');
                    t.setAttribute('aria-selected', 'false');
                });
                
                // Add 'active' class to clicked tab
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');
                
                // Show the corresponding tab pane
                document.querySelectorAll('.tab-pane').forEach(pane => {
                    pane.classList.remove('show', 'active');
                });
                
                const tabPane = document.getElementById(tabId);
                if (tabPane) {
                    tabPane.classList.add('show', 'active');
                }
            }
        });
    });
    
    // Initialize charts if analytics tab is active
    if (activeTab === 'analytics') {
        setTimeout(function() {
            if (typeof initializeCharts === 'function') {
                initializeCharts();
            } else {
                console.log("Chart initialization function not found");
            }
        }, 500);
    }
});