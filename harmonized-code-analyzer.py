# Install these dependencies before running the script:
# pip install pymongo pandas numpy matplotlib seaborn scikit-learn fuzzywuzzy python-Levenshtein openpyxl

# Replace the import section at the top of harmonized-code-analyzer.py with this:
import pandas as pd
import numpy as np
from pymongo import MongoClient
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import re
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from fuzzywuzzy import fuzz



# MongoDB connection
def connect_to_mongodb():
    uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
    client = MongoClient(uri)
    return client

# Function to retrieve app data from MongoDB
def get_app_data():
    client = connect_to_mongodb()
    db = client.get_database("captains_log")
    collection = db.get_collection(name="app")
    data = list(collection.find())
    app_df = pd.DataFrame(data)
    return app_df

# Load the predefined harmonized code list with categories
def load_harmonized_codes():
    harmonized_codes = {
        # Common emergency codes (generally 3 digits)
        'emergency': [
            {'code': '110', 'description': 'Police'},
            {'code': '111', 'description': 'Fire Service'},
            {'code': '112', 'description': 'Emergency Services (Combined)'},
            {'code': '191', 'description': 'Police'},
            {'code': '192', 'description': 'Fire'},
            {'code': '193', 'description': 'Ambulance'},
            {'code': '194', 'description': 'Disaster Management'},
            {'code': '198', 'description': 'National Security'},
            {'code': '199', 'description': 'National Emergency'}
        ],
        
        # Customer service codes
        'customer_service': [
            {'code': '100', 'description': 'Operator Assistance'},
            {'code': '101', 'description': 'Customer Service'},
            {'code': '102', 'description': 'Directory Enquiries'},
            {'code': '103', 'description': 'International Access'},
            {'code': '104', 'description': 'Operator Assistance'},
            {'code': '123', 'description': 'Voicemail'}
        ],
        
        # Common service codes
        'service_codes': [
            {'code': '120', 'description': 'Time Information'},
            {'code': '121', 'description': 'Weather Information'},
            {'code': '122', 'description': 'Traffic Information'},
            {'code': '124', 'description': 'Pre-selection Service'},
            {'code': '125', 'description': 'Service Activation'},
            {'code': '126', 'description': 'Service Information'},
            {'code': '127', 'description': 'Network Information'},
            {'code': '128', 'description': 'Service Check'},
            {'code': '129', 'description': 'Prepaid Balance'}
        ],
        
        # Toll-free numbers
        'toll_free': [
            {'code': '800', 'description': 'Toll-Free Prefix'},
            {'code': '0800', 'description': 'Toll-Free Prefix'}
        ],
        
        # Premium rate services
        'premium': [
            {'code': '900', 'description': 'Premium Rate Prefix'},
            {'code': '0900', 'description': 'Premium Rate Prefix'}
        ],
        
        # Mobile money codes
        'mobile_money': [
            {'code': '222', 'description': 'Mobile Money Service'},
            {'code': '200', 'description': 'Money Service'}
        ]
    }
    
    # Flatten the dictionary into a DataFrame
    flat_codes = []
    for category, codes in harmonized_codes.items():
        for code_info in codes:
            flat_codes.append({
                'code': code_info['code'],
                'description': code_info['description'],
                'category': category,
                'is_harmonized': True
            })
    
    return pd.DataFrame(flat_codes)





def preprocess_text(text):
    """Clean and preprocess text for better matching"""
    if text is None:
        return ""
    
    # Convert to string and lowercase
    text = str(text).lower()
    
    # Remove special characters, keeping alphanumeric and spaces
    text = re.sub(r'[^\w\s]', ' ', text)
    
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def quick_fuzzy_match(text1, text2):
    """Fast fuzzy matching using fuzzywuzzy"""
    # Preprocess both texts
    clean_text1 = preprocess_text(text1)
    clean_text2 = preprocess_text(text2)
    
    # If either text is empty after preprocessing
    if not clean_text1 or not clean_text2:
        return 0
    
    # Use token_set_ratio which is good for partial matches regardless of word order
    ratio = fuzz.token_set_ratio(clean_text1, clean_text2)
    
    # Normalize to 0-1 scale (from fuzzywuzzy's 0-100 scale)
    return ratio / 100.0

def fast_text_similarity(query_text, corpus_texts, min_threshold=0.6):
    """
    Much faster text similarity function that doesn't use API calls
    
    Args:
        query_text (str): The text to compare against the corpus
        corpus_texts (list): List of texts to compare with
        min_threshold (float): Minimum similarity threshold
        
    Returns:
        list: Sorted list of (similarity_score, index) tuples
    """
    # Clean inputs
    query_text = preprocess_text(query_text)
    corpus_texts = [preprocess_text(text) for text in corpus_texts]
    
    # Filter out empty texts
    valid_corpus = [(text, i) for i, text in enumerate(corpus_texts) if text]
    
    if not query_text or not valid_corpus:
        return []
    
    # First pass: Use fast fuzzy matching to filter candidates
    candidates = []
    for text, idx in valid_corpus:
        score = quick_fuzzy_match(query_text, text)
        if score >= min_threshold:
            candidates.append((text, idx, score))
    
    # If we have very few candidates or none at all, return fuzzy results
    if len(candidates) <= 5:
        return [(score, idx) for _, idx, score in sorted(candidates, key=lambda x: x[2], reverse=True)]
    
    # For more precision on a smaller set of candidates, use TF-IDF with cosine similarity
    texts = [query_text] + [text for text, _, _ in candidates]
    indices = [idx for _, idx, _ in candidates]
    
    # Create TF-IDF matrix
    vectorizer = TfidfVectorizer(min_df=1, stop_words='english')
    tfidf_matrix = vectorizer.fit_transform(texts)
    
    # Convert to array for easier manipulation
    feature_matrix = tfidf_matrix.toarray()
    
    # Extract query vector (first row) and corpus vectors
    query_vector = feature_matrix[0]
    corpus_vectors = feature_matrix[1:]
    
    # Compute cosine similarity
    similarities = []
    for i, vector in enumerate(corpus_vectors):
        # Cosine similarity calculation
        dot_product = np.dot(query_vector, vector)
        query_norm = np.linalg.norm(query_vector)
        vector_norm = np.linalg.norm(vector)
        
        # Avoid division by zero
        if query_norm == 0 or vector_norm == 0:
            similarity = 0
        else:
            similarity = dot_product / (query_norm * vector_norm)
        
        # Store original index and similarity
        similarities.append((similarity, indices[i]))
    
    # Sort by similarity (highest first)
    similarities.sort(reverse=True)
    
    return similarities










# Function to standardize SNR format
def standardize_snr(snr):
    if pd.isna(snr) or snr is None:
        return ''
    
    snr_str = str(snr).strip()
    
    # Remove spaces, hyphens, etc.
    snr_str = re.sub(r'[\s\-\.]', '', snr_str)
    
    # For toll-free numbers, keep the format
    if snr_str.startswith('0800'):
        return snr_str
    
    # Otherwise, remove leading zeros
    return snr_str.lstrip('0')

# Function to categorize SNRs based on characteristics
def categorize_snr(snr, snr_type):
    snr_str = str(snr)
    length = len(standardize_snr(snr_str))
    
    # Emergency and service codes (usually 3 digits)
    if length == 3:
        if snr_str in ['110', '111', '112', '191', '192', '193', '194', '198', '199']:
            return 'Emergency'
        elif snr_str in ['100', '101', '102', '103', '104', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']:
            return 'Service'
        else:
            return '3-Digit'
    
    # 4-digit codes
    elif length == 4:
        if snr_str.startswith('2'):
            return 'MNO Service'
        else:
            return '4-Digit'
    
    # 5-digit codes
    elif length == 5:
        return '5-Digit'
    
    # Toll-free numbers
    elif 'toll free' in str(snr_type).lower() or str(snr_str).startswith('0800') or str(snr_str).startswith('800'):
        return 'Toll-Free'
    
    # Default category
    return 'Other'

# Function to determine if a number is likely a harmonized code
def is_harmonized_code(snr, harmonized_df):
    std_snr = standardize_snr(snr)
    
    # Check exact matches
    exact_match = harmonized_df[harmonized_df['code'].apply(standardize_snr) == std_snr]
    if not exact_match.empty:
        return True
    
    # Check for toll-free numbers
    if std_snr.startswith('800') or std_snr.startswith('0800'):
        return True
    
    # Check for very short codes (3 digits)
    if len(std_snr) == 3:
        # Most 3-digit codes are harmonized/special purpose
        return True
    
    return False

# Function to check SNR against NCA database
def check_snr_in_database(snr, app_df):
    std_snr = standardize_snr(snr)
    
    # Check if this SNR exists in the app database
    matches = app_df[app_df['snr'].apply(lambda x: standardize_snr(x) == std_snr)]
    
    if matches.empty:
        return {
            'found': False,
            'applicant': 'N/A',
            'vasp': 'N/A',
            'auth_date': None,
            'mtn_check': False,
            'applications': []
        }
    
    # Get the most recent record
    latest = matches.iloc[-1]
    
    applications = []
    if latest.get('ussdcheck') == 'on':
        applications.append('USSD')
    if latest.get('smscheck') == 'on':
        applications.append('SMS')
    if latest.get('odacheck') == 'on':
        applications.append('Other Data Services')
    
    return {
        'found': True,
        'applicant': latest.get('applicant', 'N/A'),
        'vasp': latest.get('vasp', 'N/A'),
        'auth_date': latest.get('cert_date', None),
        'mtn_check': latest.get('mtncheck') == 'on',
        'applications': applications
    }

# Function to get embeddings for similarity comparison
def get_embedding(text, client, model="text-embedding-3-small"):
    """Get embeddings using OpenAI API"""
    
    # Clean text
    text = str(text).replace("\n", " ")
    
    # Get embedding
    try:
        response = client.embeddings.create(input=[text], model=model)
        return response.data[0].embedding
    except Exception as e:
        print(f"Error getting embedding for '{text}': {e}")
        return None

# Function to calculate cosine similarity
def cosine_similarity(vec1, vec2):
    """Calculate cosine similarity between two vectors"""
    if vec1 is None or vec2 is None:
        return 0
    
    dot_product = np.dot(vec1, vec2)
    norm_vec1 = np.linalg.norm(vec1)
    norm_vec2 = np.linalg.norm(vec2)
    
    if norm_vec1 == 0 or norm_vec2 == 0:
        return 0
    
    similarity = dot_product / (norm_vec1 * norm_vec2)
    return similarity

# Replace the compare_text_similarity function with this much faster version
def compare_text_similarity(user_text, db_texts, *args, **kwargs):
    """Drop-in replacement for the OpenAI-based similarity function"""
    # Ignore the openai_client parameter (represented by *args, **kwargs)
    return fast_text_similarity(user_text, db_texts)



# Add this function right before your analyze_mtn_snrs function
def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization"""
    import numpy as np
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_types(i) for i in obj]
    else:
        return obj




# Main function to analyze MTN's SNR submission
def analyze_mtn_snrs(mtn_file_path, output_path="MTN_SNR_Analysis"):
    print(f"Analyzing MTN SNR submission: {mtn_file_path}")
    
    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)
    
    # Load data
    print("Loading data...")
    
    # MTN submission
    mtn_df = pd.read_excel(mtn_file_path)
    
    # Clean column names
    mtn_df.columns = [col.strip() for col in mtn_df.columns]
    
    # NCA database
    app_df = get_app_data()
    print(f"Retrieved {len(app_df)} records from NCA database")
    
    # Harmonized codes
    harmonized_df = load_harmonized_codes()
    print(f"Loaded {len(harmonized_df)} harmonized codes")
    
    # Standardize SNRs
    mtn_df['SNR_Std'] = mtn_df['SNR'].apply(standardize_snr)
    
    # Add category
    mtn_df['Category'] = mtn_df.apply(lambda row: categorize_snr(row['SNR'], row['TYPE (AUTOFILL)']), axis=1)
    
    # Check if SNR is harmonized
    mtn_df['Is_Harmonized'] = mtn_df['SNR'].apply(lambda x: is_harmonized_code(x, harmonized_df))
    
    # Get service type frequencies
    service_counts = mtn_df['SERVICE DESCRIPTION'].value_counts()
    print("\nService Types:")
    print(service_counts)
    
    # Get category frequencies
    category_counts = mtn_df['Category'].value_counts()
    print("\nCategories:")
    print(category_counts)
    
    # Analyze harmonized codes
    harmonized_count = mtn_df['Is_Harmonized'].sum()
    print(f"\nHarmonized Codes: {harmonized_count} ({harmonized_count/len(mtn_df)*100:.2f}%)")
    
    # Check SNRs against NCA database
    print("\nChecking SNRs against NCA database...")
    db_checks = []
    
    for _, row in mtn_df.iterrows():
        snr = row['SNR']
        db_info = check_snr_in_database(snr, app_df)
        db_checks.append(db_info)
    
    # Add database check results to DataFrame
    mtn_df['DB_Found'] = [check['found'] for check in db_checks]
    mtn_df['DB_Applicant'] = [check['applicant'] for check in db_checks]
    mtn_df['DB_VASP'] = [check['vasp'] for check in db_checks]
    mtn_df['DB_MTN_Check'] = [check['mtn_check'] for check in db_checks]
    mtn_df['DB_Applications'] = [','.join(check['applications']) if check['applications'] else 'None' for check in db_checks]
    
    # Calculate database match statistics
    db_match_count = mtn_df['DB_Found'].sum()
    print(f"SNRs found in NCA database: {db_match_count} ({db_match_count/len(mtn_df)*100:.2f}%)")
    
    # Check for discrepancies
    discrepancy_results = []
    for _, row in mtn_df.iterrows():
        if row['DB_Found']:
            has_discrepancy, note = check_discrepancy(row, {
                'applicant': row['DB_Applicant'],
                'vasp': row['DB_VASP']
            })
            discrepancy_results.append({
                'has_discrepancy': has_discrepancy,
                'note': note
            })
        else:
            discrepancy_results.append({
                'has_discrepancy': False,
                'note': ""
            })

    mtn_df['Is_Discrepancy'] = [r['has_discrepancy'] for r in discrepancy_results]
    mtn_df['Discrepancy_Note'] = [r['note'] for r in discrepancy_results]
        
    discrepancy_count = mtn_df['Is_Discrepancy'].sum()
    print(f"Discrepancies found: {discrepancy_count}")
    

    
    # Perform similarity checks for third party users
    print("\nPerforming similarity checks for third party users...")
    third_party_df = mtn_df[mtn_df['USER(OPERATOR OR THIRD PARTY)'] != 'MTN'].copy()
    
    if not third_party_df.empty and not app_df.empty and 'applicant' in app_df.columns and 'vasp' in app_df.columns:
        # Get unique applicants and VASPs from database
        db_entities = set()
        if 'applicant' in app_df.columns:
            db_entities.update(app_df['applicant'].dropna().unique())
        if 'vasp' in app_df.columns:
            db_entities.update(app_df['vasp'].dropna().unique())
        
        db_entities = [e for e in db_entities if e and e != 'N/A' and len(str(e)) > 2]
        
        # Perform similarity check for each third party
        results = []
        
        for _, row in third_party_df.iterrows():
            user = row['USER(OPERATOR OR THIRD PARTY)']
            snr = row['SNR']
            
            if not user or pd.isna(user):
                continue
            print(f"Checking similarity for user: {user}")
            # Compare with database entities
            similarities = compare_text_similarity(user, db_entities)
            
            # Take the top match if similarity is above threshold
            if similarities and similarities[0][0] > 0.6:
                top_sim, top_idx = similarities[0]
                best_match = list(db_entities)[top_idx]
                results.append({
                    'SNR': snr,
                    'User': user,
                    'Best_Match': best_match,
                    'Similarity': top_sim
                })
        
        if results:
            similarity_df = pd.DataFrame(results)
            print(f"Found {len(similarity_df)} potential matches based on text similarity")
            
            # Save similarity results
            similarity_df.to_excel(os.path.join(output_path, "User_Similarity_Matches.xlsx"), index=False)

    # Generate visualizations
    print("\nGenerating visualizations...")
    
    # 1. SNR Category Distribution
    plt.figure(figsize=(10, 6))
    ax = sns.countplot(data=mtn_df, x='Category')
    plt.title('SNR Category Distribution')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'category_distribution.png'))
    plt.close()
    
    # 2. MTN vs Third Party
    user_counts = mtn_df['USER(OPERATOR OR THIRD PARTY)'].apply(
        lambda x: 'MTN' if x == 'MTN' else 'Third Party'
    ).value_counts()
    
    plt.figure(figsize=(8, 8))
    plt.pie(user_counts, labels=user_counts.index, autopct='%1.1f%%', startangle=90)
    plt.title('MTN vs Third Party SNRs')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'mtn_vs_third_party.png'))
    plt.close()
    
    # 3. Harmonized vs Regular
    harmonized_counts = mtn_df['Is_Harmonized'].value_counts()
    
    plt.figure(figsize=(8, 8))
    plt.pie(
        harmonized_counts, 
        labels=['Harmonized' if i else 'Regular' for i in harmonized_counts.index], 
        autopct='%1.1f%%', 
        startangle=90
    )
    plt.title('Harmonized vs Regular SNRs')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'harmonized_vs_regular.png'))
    plt.close()
    
    # 4. Database Match Status
    db_match_counts = mtn_df['DB_Found'].value_counts()
    
    plt.figure(figsize=(8, 8))
    plt.pie(
        db_match_counts, 
        labels=['In NCA Database' if i else 'Not in NCA Database' for i in db_match_counts.index], 
        autopct='%1.1f%%', 
        startangle=90
    )
    plt.title('SNR Database Match Status')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'database_match_status.png'))
    plt.close()
    
    # 5. Status Distribution
    status_counts = mtn_df['STATUS'].value_counts()
    
    plt.figure(figsize=(8, 6))
    ax = sns.barplot(x=status_counts.index, y=status_counts.values)
    plt.title('SNR Status Distribution')
    plt.ylabel('Count')
    plt.tight_layout()
    plt.savefig(os.path.join(output_path, 'status_distribution.png'))
    plt.close()
    
    # Save analysis results
    print("\nSaving analysis results...")
    mtn_df.to_excel(os.path.join(output_path, "MTN_SNR_Analysis.xlsx"), index=False)
    
# Then replace the JSON dump section in the analyze_mtn_snrs function with this:

    # Create summary report
    summary_data = {
        'Total SNRs': len(mtn_df),
        'MTN Exclusive': len(mtn_df[mtn_df['USER(OPERATOR OR THIRD PARTY)'] == 'MTN']),
        'Third Party': len(mtn_df[mtn_df['USER(OPERATOR OR THIRD PARTY)'] != 'MTN']),
        'Active SNRs': len(mtn_df[mtn_df['STATUS'] == 'ACTIVE']),
        'Inactive SNRs': len(mtn_df[mtn_df['STATUS'] == 'INACTIVE']),
        'Harmonized Codes': harmonized_count,
        'Database Matches': db_match_count,
        'Discrepancies': discrepancy_count
    }

    # Convert NumPy types to native Python types before JSON serialization
    summary_data_converted = convert_numpy_types(summary_data)

    with open(os.path.join(output_path, "summary_statistics.json"), 'w') as f:
        json.dump(summary_data_converted, f, indent=4)


    
    # Create a detailed report for harmonized codes
    harmonized_df = mtn_df[mtn_df['Is_Harmonized']].copy()
    
    if not harmonized_df.empty:
        harmonized_df.to_excel(os.path.join(output_path, "Harmonized_Codes_Analysis.xlsx"), index=False)
    
    # Create a report for discrepancies
    discrepancy_df = mtn_df[mtn_df['Is_Discrepancy']].copy()
    
    if not discrepancy_df.empty:
        discrepancy_df.to_excel(os.path.join(output_path, "Database_Discrepancies.xlsx"), index=False)
    
    print(f"\nAnalysis complete! Results saved to {output_path}")
    return mtn_df



def is_name_similar(name1, name2, threshold=70):
    """
    Check if two organization names are similar using fuzzy matching
    
    Args:
        name1: First organization name
        name2: Second organization name
        threshold: Similarity threshold (0-100)
        
    Returns:
        bool: True if names are similar, False otherwise
    """
    import re
    from fuzzywuzzy import fuzz
    
    if not name1 or not name2:
        return False
    
    # Convert to strings
    name1 = str(name1).lower().strip()
    name2 = str(name2).lower().strip()
    
    # Remove common words and characters that don't add meaning
    stopwords = ['limited', 'ltd', 'inc', 'incorporated', 'corporation', 'corp', 
                'company', 'co', 'llc', 'plc', 'group', 'holdings', 'ghana', 'services']
    
    for word in stopwords:
        name1 = re.sub(r'\b' + word + r'\b', '', name1)
        name2 = re.sub(r'\b' + word + r'\b', '', name2)
    
    # Clean up punctuation and extra spaces
    name1 = re.sub(r'[^\w\s]', ' ', name1)
    name2 = re.sub(r'[^\w\s]', ' ', name2)
    name1 = re.sub(r'\s+', ' ', name1).strip()
    name2 = re.sub(r'\s+', ' ', name2).strip()
    
    # If either name is now empty after cleaning
    if not name1 or not name2:
        return False
    
    # Special case for MTN
    if 'mtn' in name1 and 'mtn' in name2:
        return True
    
    # Use token set ratio for best partial matching regardless of word order
    ratio = fuzz.token_set_ratio(name1, name2)
    
    return ratio >= threshold

def check_name_match(mtn_user, db_applicant, db_vasp):
    """
    Check if MTN's user matches either the applicant or VASP in the database
    
    Args:
        mtn_user: User name from MTN submission
        db_applicant: Applicant name from database
        db_vasp: VASP name from database
        
    Returns:
        bool: True if there's a match, False otherwise
    """
    if not mtn_user:
        return False
    
    # Convert to strings and handle None values
    mtn_user = str(mtn_user) if mtn_user else ""
    db_applicant = str(db_applicant) if db_applicant and db_applicant != "N/A" else ""
    db_vasp = str(db_vasp) if db_vasp and db_vasp != "N/A" else ""
    
    # Check if MTN is the user in MTN's submission
    is_mtn_user = is_name_similar(mtn_user, "MTN")
    
    # Check if MTN is the applicant or VASP in the database
    is_mtn_in_db = is_name_similar(db_applicant, "MTN") or is_name_similar(db_vasp, "MTN")
    
    # If MTN is claiming it's theirs but database doesn't show MTN
    if is_mtn_user and not is_mtn_in_db and (db_applicant or db_vasp):
        return False
    
    # If MTN says it's for a third party but database shows MTN
    if not is_mtn_user and is_mtn_in_db:
        return False
    
    # If it's a third party, check if the names match
    if not is_mtn_user and not is_mtn_in_db:
        return is_name_similar(mtn_user, db_applicant) or is_name_similar(mtn_user, db_vasp)
    
    # Otherwise, no discrepancy
    return True

# Usage in the match_snrs_with_protected function:
def check_discrepancy(row, db_info):
    """Determine if there's a discrepancy between MTN submission and NCA database"""
    mtn_user = row['USER(OPERATOR OR THIRD PARTY)']
    db_applicant = db_info['applicant']
    db_vasp = db_info['vasp']
    
    # Skip check if no database record
    if db_applicant == 'N/A' and db_vasp == 'N/A':
        return False, ""
    
    # Check for name match
    name_match = check_name_match(mtn_user, db_applicant, db_vasp)
    
    if not name_match:
        if is_name_similar(mtn_user, "MTN"):
            return True, "MTN claims ownership but database shows different entity"
        else:
            return True, f"Listed as '{mtn_user}' but database shows different entity"
    
    return False, ""









# Entry point
if __name__ == "__main__":
    print("MTN Special Numbering Resources (SNR) Analysis Tool")
    print("--------------------------------------------------")
    
    # Get the file path
    mtn_file_path = input("Enter path to MTN SNR file (default: MTN SNR 2024 EXTRACT.xlsx): ")
    
    if not mtn_file_path:
        mtn_file_path = "MTN SNR 2024 EXTRACT.xlsx"
    
    # Run the analysis
    analyze_mtn_snrs(mtn_file_path)
