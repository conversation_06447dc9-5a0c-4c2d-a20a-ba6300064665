# app.py - Using the Application Factory Pattern

import functools
import os
import json
import re
import base64
import tempfile
from datetime import datetime, timedelta, timezone
from io import BytesIO
from typing import Union, List, Dict
from bson import ObjectId
from bson.json_util import dumps
from flask import Flask, current_app, session, request, jsonify, render_template, redirect, url_for, flash, send_file
from flask_login import login_user, logout_user, login_required, current_user
from pymongo import MongoClient
from pymongo.collection import Collection
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from dateutil import parser
import pandas as pd
import qrcode
import plotly.express as px
import plotly.graph_objects as go
import requests
import openai
from dotenv import load_dotenv

# Import application components
from extensions import mongo, login_manager, cache, cors, login_required, current_user, admin_required, allowed_file
from models import User, Anonymous
from e164.utils import log_activity, add_notification, allowed_file, admin_required
from e164 import e164_bp

# PDF generation imports
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, inch
from reportlab.lib.units import inch
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import Paragraph, SimpleDocTemplate, Spacer, Image, Table, TableStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_JUSTIFY
from reportlab.lib.colors import black, grey, HexColor
from reportlab.platypus import PageTemplate, BaseDocTemplate, Frame
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
from reportlab.platypus import Image

from e164.routes import get_status_badge_class

# Font configuration for reports
try:
    pdfmetrics.registerFont(TTFont('Helvetica-Oblique', 'HelveticaOblique.ttf'))
    italic_font_name = 'Helvetica-Oblique'
except Exception as e:
    print(f"Error registering Helvetica-Oblique: {e}. Using Helvetica for italic style as fallback.")
    italic_font_name = 'Helvetica'

# Load environment variables
load_dotenv()

# Initialize OpenAI client
openai_client = openai.OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Pricing constants
PRICING = {
    "3 Digit": 2100,
    "4 Digit": 500,
    "5 Digit": 300,
    "6 Digit": 250,
    "Tollfree": 40,
    "Premium": 100
}

# MongoDB collection schemas for reference (as docstrings)
"""
applications_schema = {
  "applicant_name": "String",       # Name of the applicant
  "application_date": "String",     # Date of the application in ISO format (YYYY-MM-DD)
  "status": "String",              # Status of the application (e.g., "Certificate_printable")
  "remarks": "String",              # Remarks or notes about the application (optional)
  "created_at": {
    "$date": "Timestamp" # Timestamp of when the application was created
  },
  "updated_at": {
    "$date": "Timestamp" # Timestamp of when the application was last updated
  },
  "certificate_expiry_date": "String",   # Expiry date of the certificate in MM/DD/YY format
  "certificate_generated": "Boolean",    # Indicates whether the certificate has been generated
  "certificate_start_date": "String", # Start date of the certificate in MM/DD/YY format
  "processed_by": "String"          # Name of the person who processed the application
}

snr_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                  # Shortcode Special Numbering Resource (SNR) from 100 to 699999
  "STATUS": "String",              # Status of the SNR (AVAILABLE, EXPIRED, ASSIGNED, RESERVED)
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or ""
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or ""
}

tf_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                  # Tollfree Special Numbering Resource (SNR)
  "STATUS": "String",              # Status of the SNR (AVAILABLE, EXPIRED, ASSIGNED, RESERVED)
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or ""
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or ""
}

pr_status_schema = {
  "_id": {
    "$oid": "ObjectId"
  },
  "SNR": "String",                 # Premium-rate Special Numbering Resource (SNR)
  "STATUS": "String",              # Status of the SNR (AVAILABLE, EXPIRED, ASSIGNED, RESERVED)
  "EXPIRY": "String",              # Expiry date of the SNR in the format (mm/dd/yyyy) or ""
  "ASSIGNEE": "String"             # Name of the assignee of the SNR or ""
}

snrs_schema = {
  "application_id": "String",     # Unique identifier for the application
  "snr": "String",                 # Special Numbering Resource (SNR)
  "purpose": "String",             # Purpose or description of usage of the SNR
  "status": "String",              # Status of the SNR
  "type": "String",                # Type of the SNR
  "certificate_id": "String",     # Unique identifier for the certificate
}
"""

def create_app(config=None):
    """Application factory function to create and configure the Flask app."""
    app = Flask(__name__)

    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', b'_5#y2L"F4Q8z\n\xec')
    app.config["MONGO_URI"] = os.getenv('MONGO_URI', "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0")
    app.config['UPLOAD_FOLDER'] = os.path.join('static', 'uploads')
    app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16 MB limit

    # Create temp directory if not exists
    os.makedirs(os.path.join(app.config['UPLOAD_FOLDER'], 'tmp'), exist_ok=True)

    # Initialize extensions
    mongo.init_app(app)
    mongo.cx = MongoClient(app.config["MONGO_URI"])  # Explicit client creation
    login_manager.init_app(app)
    cache.init_app(app)
    cors.init_app(app)

    # Configure login_manager
    login_manager.login_view = 'login'
    login_manager.login_message = "Please log in to access this page."
    login_manager.login_message_category = "warning"
    login_manager.anonymous_user = Anonymous

    # Register blueprints
    app.register_blueprint(e164_bp, url_prefix='/e164')

    # Register Jinja filters
    app.jinja_env.filters['status_color'] = get_status_badge_class

    # Set up before_request handler for session persistence
    @app.before_request
    def make_session_permanent():
        session.permanent = True
        app.permanent_session_lifetime = timedelta(minutes=60)

    # Register user loader for Flask-Login
    @login_manager.user_loader
    def load_user(user_id):
        user = mongo.db.users.find_one({'_id': ObjectId(user_id)})
        if user:
            return User(user)
        return None

    # Define a Jinja2 filter for formatting numbers
    @app.template_filter('format_number')
    def format_number(value):
        """Format numbers with commas for thousands."""
        if value is None:
            return "0"
        try:
            return "{:,}".format(int(value))
        except (ValueError, TypeError):
            return str(value)


    @app.template_filter('format_datetime')
    def format_datetime(value, format='%Y-%m-%d %H:%M:%S'):
        """Format a datetime object to a string."""
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except:
                pass
        if isinstance(value, datetime):
            return value.strftime(format)
        return value

    # Keep the original 'datetime' filter for backward compatibility
    @app.template_filter('datetime')
    def datetime_filter(value, format='%Y-%m-%d %H:%M:%S'):
        return format_datetime(value, format)

    # Register the context processor for notifications
    @app.context_processor
    def inject_notifications():
        """Inject notifications into template context."""
        if current_user.is_authenticated:
            notification_query_conditions = []
            base_query = {'read': False}

            # Always include notifications addressed to the user's specific contact name
            notification_query_conditions.append({'recipient': current_user.contact})

            # If the user is an operator, also fetch notifications for their operator_id
            if hasattr(current_user, 'operator_id') and current_user.operator_id:
                # Assuming operator_id in User model and notifications collection are both strings
                # If operator_id in User model is ObjectId, ensure it's cast to str for comparison,
                # or if notifications.recipient stores ObjectIds for operator_id, query with ObjectId.
                # For simplicity, assuming string comparison is intended based on submission.get('operator_id').
                notification_query_conditions.append({'recipient': str(current_user.operator_id)})
            
            # If the user is an admin, they might also receive general "Admin" role notifications
            # This assumes notifications for "Admin" are created with recipient="Admin"
            # The default contact for an admin in your model is 'Admin' if not specified otherwise.
            # So, the first condition {'recipient': current_user.contact} would cover this
            # if current_user.contact is 'Admin'.

            if len(notification_query_conditions) > 1:
                final_query = {'$and': [base_query, {'$or': notification_query_conditions}]}
            elif notification_query_conditions: # Should always be at least one if authenticated
                final_query = {'$and': [base_query, notification_query_conditions[0]]}
            else: # Should not happen for authenticated user, but as a fallback
                final_query = base_query 
                final_query['recipient'] = "____nouser____" # Ensure no results if conditions are empty

            # Fetch distinct notifications based on the constructed query
            # Using a set to avoid duplicates if a notification somehow matches multiple conditions
            # (e.g., an operator's contact name is the same as their operator_id string)
            # However, a simple find should be okay if recipient logic is distinct.
            
            # Let's simplify the query logic slightly for clarity if $or is used:
            if hasattr(current_user, 'operator_id') and current_user.operator_id:
                # For operators, look for messages to their contact OR their operator_id
                # This structure is cleaner for $or
                final_notification_query = {
                    'read': False,
                    '$or': [
                        {'recipient': current_user.contact},
                        {'recipient': str(current_user.operator_id)} # Ensure this matches the type in DB
                    ]
                }
            else:
                # For admins and other users
                final_notification_query = {
                    'read': False,
                    'recipient': current_user.contact
                }

            unread_notifications = list(mongo.db.notifications.find(final_notification_query)
                                        .sort('timestamp', -1).limit(10)) # Limit for display in navbar

            for notif in unread_notifications:
                notif['_id'] = str(notif['_id']) # Ensure _id is a string for url_for

            # Get the total count of unread notifications matching the criteria
            unread_notifications_count = mongo.db.notifications.count_documents(final_notification_query)

            return {
                'notifications': unread_notifications,
                'unread_notifications_count': unread_notifications_count
            }
        return { # For anonymous users
            'notifications': [],
            'unread_notifications_count': 0
        }



    # Register routes and functions
    register_routes(app)

    return app

def register_routes(app):
    """Register all application routes."""

    # Authentication routes
    @app.route('/register', methods=['GET', 'POST'])
    def register():
        from forms import RegistrationForm
        form = RegistrationForm()
        if form.validate_on_submit():
            vas_id = form.vas_id.data.strip()
            # Cross-check vas_id with MongoDB
            vas_entry = mongo.db.vas_table.find_one({'ID': vas_id})
            if not vas_entry:
                flash('Invalid VAS ID.')
                return redirect(url_for('register'))

            # Check if email already exists
            existing_user = mongo.db.users.find_one({'email': form.email.data})
            if existing_user:
                flash('Email already registered.')
                return redirect(url_for('register'))

            # Handle file uploads
            national_id_file = form.national_id.data
            photo_file = form.photo.data

            if not (allowed_file(national_id_file.filename) and allowed_file(photo_file.filename)):
                flash('Invalid file format.')
                return redirect(url_for('register'))

            national_id_filename = secure_filename(national_id_file.filename)
            photo_filename = secure_filename(photo_file.filename)

            # Save files
            national_id_path = os.path.join(app.config['UPLOAD_FOLDER'], 'national_ids', national_id_filename)
            photo_path = os.path.join(app.config['UPLOAD_FOLDER'], 'photos', photo_filename)

            national_id_file.save(national_id_path)
            photo_file.save(photo_path)

            # Hash the password
            hashed_password = generate_password_hash(form.password.data)

            # Create user document
            user_data = {
                'vas_id': vas_id,
                'email': form.email.data,
                'password': hashed_password,
                'vas_company': vas_entry['VASP'],
                'contact': form.contact.data,
                'created_timestamp': datetime.now(timezone.utc),
                'national_id': national_id_path,
                'photo': photo_path,
                'phone': form.phone.data
            }

            # Insert into MongoDB
            mongo.db.users.insert_one(user_data)

            flash('Registration successful.')
            return redirect(url_for('search'))

        return render_template('register.html', form=form)

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        from forms import LoginForm
        if current_user.is_authenticated:
            return redirect(url_for('search'))

        form = LoginForm()
        if form.validate_on_submit():
            username = form.username.data.strip()
            password = form.password.data.strip()

            # Fetch user from database
            user_data = mongo.db.users.find_one({'contact': username})
            if user_data and check_password_hash(user_data['password'], password):
                user = User(user_data)
                login_user(user, remember=form.remember.data)

                # Log user login activity
                log_activity("User Login", f"User {username} logged in.")

                flash('Logged in successfully.', 'success')
                next_page = request.args.get('next')
                return redirect(next_page) if next_page else redirect(url_for('search'))
            else:
                flash('Invalid username or password.', 'danger')
                return redirect(url_for('login'))

        return render_template('login.html', form=form)

    @app.route('/logout')
    @login_required
    def logout():
        log_activity("Logout", f"User {current_user.contact} logged out.")
        logout_user()
        flash('You have been logged out.', 'success')
        return redirect(url_for('login'))

    @app.route('/update_account', methods=['GET', 'POST'])
    @login_required
    def update_account():
        if request.method == 'POST':
            # Update user details
            email = request.form['email']
            phone = request.form['phone']

            # Update in MongoDB (we ensure we don't modify the vas_company by mistake)
            mongo.db.users.update_one(
                {'_id': ObjectId(current_user.id)},
                {'$set': {'email': email, 'phone': phone}}
            )
            flash('Account updated successfully.', 'success')
            return redirect(url_for('update_account'))

        # Fetch current user data for populating the form
        user_data = mongo.db.users.find_one({'_id': ObjectId(current_user.id)})
        return render_template('update_account.html', user_data=user_data)

    # Search and application routes
    @app.route('/', methods=['GET', 'POST'])
    def search():
        query = "" # Initialize query
        results_to_render = [] # Initialize as an empty list

        if request.method == 'POST':
            query = request.form['query'].strip()
            if not query:
                flash('Please enter a search query.', 'warning')
                return redirect(url_for('search'))

            if current_user.is_authenticated:
                log_activity("User Search", f"User {current_user.contact} performed a search for '{query}'.")

            collections = determine_collections(query)
            current_app.logger.info(f"Determined collections: {collections} for query: {query}")
            
            # perform_search_operations now returns the list directly
            results_to_render = perform_search_operations(query, collections)
            
            session['search_query'] = query
            # Store the actual list in session
            session['search_results_list'] = results_to_render 

            if not results_to_render:
                flash('No results found for your query.', 'info')
            # No return here, proceed to render_template at the end
            
        elif 'from_review' in request.args and 'search_query' in session and 'search_results_list' in session:
            query = session.get('search_query')
            results_to_render = session.get('search_results_list', []) # Expect a list
            current_app.logger.info(f"Returning from review. Query: '{query}', Results count: {len(results_to_render)}")
            if not isinstance(results_to_render, list): # Safety check
                results_to_render = []
                flash('There was an error loading your previous results. Please try again.', 'danger')
        else:
            # Clear session for a new search page visit
            session.pop('search_query', None)
            session.pop('search_results_list', None)

        return render_template('search.html', results=results_to_render, query=query)









        # Application management routes
    @app.route('/apply/<snr>', methods=['GET', 'POST'])
    @login_required
    def apply(snr):
            if request.method == 'POST':
                purpose = request.form.get('purpose', '').strip()
                if not purpose:
                    flash('Purpose is required.', 'danger')
                    return redirect(url_for('apply', snr=snr))

                # Store in session for confirmation
                session['pending_application'] = {
                    'snr': snr,
                    'purpose': purpose,
                    'application_id': generate_ids()[0]  # Use your ID generator
                }

                return redirect(url_for('confirm_apply'))

            return render_template('apply.html', snr=snr)

    @app.route('/confirm_apply', methods=['GET', 'POST'])
    @login_required
    def confirm_apply():
            # Retrieve pending application from Flask's session
            pending_app = session.get('pending_application')

            if not pending_app:
                flash('No pending application to confirm.', 'danger')
                return redirect(url_for('search'))

            # For GET requests, render the review page with the new template
            if request.method == 'GET':
                # Ensure that the stored SNR is a list.
                # (If not, convert it to one.)
                if not isinstance(pending_app.get('snr'), list):
                    pending_app['snr'] = [pending_app['snr']]
                    session['pending_application'] = pending_app

                # Generate a certificate ID if not already present.
                if 'certificate_id' not in pending_app:
                    # generate_ids() returns (application_id, certificate_id)
                    _, certificate_id = generate_ids()
                    pending_app['certificate_id'] = certificate_id
                    session['pending_application'] = pending_app

                # Determine SNR type based on the first number in the list.
                first_snr = pending_app['snr'][0] if pending_app['snr'] else ""
                if len(first_snr) <= 6:
                    snr_type = "shortcode"
                elif first_snr.startswith("0800"):
                    snr_type = "tollfree"
                else:
                    snr_type = "premium"

                # Prepare snr_info as a list with a single dictionary.
                snr_info = [{
                    'snr': pending_app['snr'],  # a list of numbers
                    'type': snr_type,
                    'purpose': pending_app['purpose'],
                    'status': "Pending Review",
                    'certificate_id': pending_app['certificate_id']
                }]

                return render_template(
                    'review_single_application.html',
                    applicant_name=current_user.contact,
                    email=current_user.email,
                    phone=current_user.phone,
                    company=current_user.company,
                    application_date=datetime.now(timezone.utc).strftime('%Y-%m-%d'),
                    snr_info=snr_info,
                    application_id=pending_app['application_id']
                )

            # For POST requests, process the confirmation and persist to the database.
            if request.method == 'POST':
                try:
                    # Use the IDs stored in the session.
                    application_id = pending_app['application_id']
                    certificate_id = pending_app['certificate_id']

                    # Create application document (metadata)
                    application_doc = {
                        "application_id": application_id,
                        "user_id": current_user.id,
                        "applicant_name": current_user.contact,
                        "email": current_user.email,
                        "phone": current_user.phone,
                        "company": current_user.company,
                        "application_date": datetime.now(timezone.utc).strftime('%Y-%m-%d'),
                        "status": "Pending Review",
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }

                    # Determine SNR type based on the first number
                    first_snr = pending_app['snr'][0] if pending_app['snr'] else ""
                    if len(first_snr) <= 6:
                        snr_type = "shortcode"
                    elif first_snr.startswith("0800"):
                        snr_type = "tollfree"
                    else:
                        snr_type = "premium"

                    # Create SNR document (specific details)
                    snr_doc = {
                        "application_id": application_id,
                        "snr": pending_app['snr'],  # now a list of numbers
                        "type": snr_type,
                        "purpose": pending_app['purpose'],
                        "status": "Pending Review",
                        "certificate_id": certificate_id,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }

                    # Use transaction for atomic operation (rename MongoDB session variable)
                    with mongo.cx.start_session() as mongo_session:
                        with mongo_session.start_transaction():
                            # Get the default database instance
                            db = mongo.cx.get_default_database()

                            # Insert application metadata
                            db.applications.insert_one(application_doc, session=mongo_session)

                            # Insert SNR details
                            db.snrs.insert_one(snr_doc, session=mongo_session)

                            # Update status collection
                            status_collection = get_status_table(pending_app['snr'][0])
                            db[status_collection].update_one(
                                {'SNR': pending_app['snr'][0]},
                                {'$set': {
                                    'STATUS': 'PENDING REVIEW',
                                    'ASSIGNEE': current_user.company,
                                    'EXPIRY': '',  # Clear previous expiry if exists
                                    'UPDATED_AT': datetime.now(timezone.utc)
                                }},
                                upsert=True,
                                session=mongo_session
                            )

                    # Clear pending application from Flask's session
                    session.pop('pending_application', None)

                    # Log activity
                    log_activity("Application Submitted",
                                f"User {current_user.contact} confirmed application for {pending_app['snr'][0]}")

                    flash('Application submitted successfully!', 'success')
                    return redirect(url_for('application_details', application_id=application_id))

                except Exception as e:
                    flash(f'Error submitting application: {str(e)}', 'danger')
                    return redirect(url_for('apply', snr=pending_app['snr'][0]))

    @app.route('/apply_multiple/preview', methods=['POST'])
    @login_required
    def apply_multiple_preview():
            if current_user.is_admin:
                log_activity("Unapproved Application Bypass Attempt", f"User {current_user.contact} attempted applying as an Admin.")
                flash('You cannot apply using an Admin account. Please use the right Applicant account.', 'danger')
                return redirect(url_for('search'))

            # Retrieve data from the hidden inputs
            purposes_json = request.form.get('purposes')

            if not purposes_json:
                flash('Please provide a purpose for the selected numbers.', 'warning')
                return redirect(url_for('search'))

            # Parse purposes JSON
            try:
                purposes = json.loads(purposes_json)
            except json.JSONDecodeError as e:
                flash(f'Error parsing purposes: {e}', 'danger')
                return redirect(url_for('search'))

            # Validate purposes and SNRs
            for purpose_group in purposes:
                purpose_text = purpose_group.get('purpose', '').strip()
                snrs_list = purpose_group.get('snrs', [])
                print(f"Purpose: {purpose_text}, SNRs: {snrs_list}")  # Debugging line

                if not purpose_text:
                    flash('Each purpose must have a valid description.', 'warning')
                    return redirect(url_for('search', from_review=1))
                if not snrs_list:
                    flash('Each purpose must have at least one associated number.', 'warning')
                    return redirect(url_for('search', from_review=1))

            # Generate unique application ID for preview
            application_id = f"APP{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"

            # Prepare list of SNRs for the application
            snrs = []
            for purpose_group in purposes:
                purpose_text = purpose_group['purpose']
                snrs_list = purpose_group['snrs']

                for snr in snrs_list:
                    certificate_id = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
                    snrs.append({
                        "snr": snr,
                        "type": "shortcode" if len(snr) <= 6 else ("tollfree" if snr.startswith('0800') else "premium"),
                        "purpose": purpose_text,
                        "status": "Pending Review",
                        "certificate_id": certificate_id
                    })

            # Save the search results and the application details in the session to allow going back
            session['application_id'] = application_id
            session['snrs'] = snrs

            log_activity("Final Application Preview", f"User {current_user.contact} reviewed their application for multiple snrs.")

            # Store COMPLETE application data in session
            session['pending_application'] = {
                'application_id': application_id,
                'snrs': snrs,
                'purposes': purposes  # Store original purposes structure
            }

            # Render review page with the gathered details
            return render_template(
                'review_multiple_application.html',
                application_id=application_id,
                applicant_name=current_user.contact,
                email=current_user.email,
                phone=current_user.phone,
                contact=current_user.contact,
                company=current_user.company,
                application_date=datetime.now(timezone.utc).strftime('%Y-%m-%d'),
                snrs=snrs
            )

    @app.route('/apply_multiple/confirm', methods=['POST'])
    @login_required
    def confirm_apply_multiple():
            application_id = request.form['application_id']
            pending_app = session.get('pending_application')

            # Validate session data
            if not pending_app or pending_app['application_id'] != application_id:
                flash('Invalid or expired application session', 'danger')
                return redirect(url_for('search'))

            # Use session-stored data instead of form data
            purposes = pending_app['purposes']

            certificate_id_base = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}"

            # Parse and validate input data
            if not isinstance(purposes, list) or len(purposes) == 0:
                flash('Invalid request format', 'danger')
                return redirect(url_for('search'))

            # Create main application document
            application_doc = {
                "application_id": application_id,
                "user_id": current_user.id,
                "applicant_name": current_user.contact,
                "email": current_user.email,
                "phone": current_user.phone,
                "company": current_user.company,
                "application_date": datetime.now(timezone.utc).strftime('%Y-%m-%d'),
                "status": "Pending Review",
                "remarks": f"{current_user.company} requested {len(purposes)} number groups",
                "created_at": datetime.now(timezone.utc),
                "updated_at": datetime.now(timezone.utc)
            }

            # Prepare SNR documents
            snr_docs = []
            for idx, purpose_group in enumerate(purposes, 1):
                for snr in purpose_group['snrs']:
                    # Generate unique certificate ID for each SNR
                    certificate_id = f"{certificate_id_base}-{idx}-{snr}"

                    snr_doc = {
                        "application_id": application_id,
                        "snr": snr,
                        "type": "shortcode" if len(snr) <= 6 else
                                "tollfree" if snr.startswith('0800') else
                                "premium",
                        "purpose": purpose_group['purpose'],
                        "status": "Pending Review",
                        "certificate_id": certificate_id,
                        "created_at": datetime.now(timezone.utc),
                        "updated_at": datetime.now(timezone.utc)
                    }

                    snr_docs.append(snr_doc)

            # Database operations using transaction
            with mongo.cx.start_session() as mongo_session:
                with mongo_session.start_transaction():
                    # Get the default database instance
                    db = mongo.cx.get_default_database()

                    # Insert application metadata
                    db.applications.insert_one(application_doc, session=mongo_session)

                    # Insert all SNR documents
                    if snr_docs:
                        db.snrs.insert_many(snr_docs, session=mongo_session)

                    # Update status tables
                    for snr_doc in snr_docs:
                        status_table = get_status_table(snr_doc['snr'])
                        db[status_table].update_one(
                            {'SNR': snr_doc['snr']},
                            {'$set': {
                                'STATUS': 'PENDING REVIEW',
                                'ASSIGNEE': current_user.company,
                                'EXPIRY': '',  # Clear previous expiry if exists
                                'UPDATED_AT': datetime.now(timezone.utc)
                            }},
                            upsert=True,
                            session=mongo_session
                        )

            log_activity("Multiple Application Submitted",
                        f"User {current_user.contact} applied for {len(snr_docs)} numbers")

            # Clear session data after successful submission
            session.pop('pending_application', None)

            flash('Application submitted successfully!', 'success')
            return redirect(url_for('application_details', application_id=application_id))

    @app.route('/dashboard')
    @login_required
    def dashboard():
            log_activity("User Applications Dashboard", f"User {current_user.contact} viewed their applications dashboard.")

            # Get filter parameters
            search_query = request.args.get('search', '')
            sort_field = request.args.get('sort', 'application_date')
            sort_order = int(request.args.get('order', -1))  # -1 for descending

            # Base pipeline
            pipeline = [
                {"$match": {"user_id": str(current_user.id)}},
                {"$lookup": {
                    "from": "snrs",
                    "localField": "application_id",
                    "foreignField": "application_id",
                    "as": "snr_details"
                }}
            ]

            # Add search filter
            if search_query:
                pipeline.insert(1, {"$match": {
                    "$or": [
                        {"application_id": {"$regex": f".*{search_query}.*", "$options": "i"}},
                        {"snr_details.snr": {"$regex": f".*{search_query}.*", "$options": "i"}},
                        {"snr_details.purpose": {"$regex": f".*{search_query}.*", "$options": "i"}},
                        {"snr_details.status": {"$regex": f".*{search_query}.*", "$options": "i"}}
                    ]
                }})

            # Add sorting
            pipeline.append({"$sort": {sort_field: sort_order}})

            # Add aggregation stages
            pipeline.extend([
                {"$addFields": {
                    "total_numbers": {"$size": "$snr_details"},
                    "status_summary": {
                        "$arrayToObject": {
                            "$reduce": {
                                "input": "$snr_details.status",
                                "initialValue": [],
                                "in": {
                                    "$concatArrays": [
                                        "$$value",
                                        [{
                                            "k": "$$this",
                                            "v": {"$sum": [{"$cond": [{"$eq": ["$$this", "$$value.k"]}, 1, 0]}]}
                                        }]
                                    ]
                                }
                            }
                        }
                    }
                }},
                {"$project": {
                    "application_id": 1,
                    "application_date": 1,
                    "total_numbers": 1,
                    "status_summary": 1,
                    "snr_details": 1
                }}
            ])

            applications = list(mongo.db.applications.aggregate(pipeline))

            # Process data
            for app in applications:
                app['_id'] = str(app['_id'])
                app['application_date'] = app['application_date']
                app['purposes'] = {}

                # Group by purpose
                for snr in app.get('snr_details', []):
                    purpose = snr.get('purpose', 'Unknown')
                    if purpose not in app['purposes']:
                        app['purposes'][purpose] = {'count': 0, 'statuses': set()}
                    app['purposes'][purpose]['count'] += 1
                    app['purposes'][purpose]['statuses'].add(snr.get('status', 'Unknown'))

                # Convert sets to lists
                for purpose in app['purposes'].values():
                    purpose['statuses'] = list(purpose['statuses'])

            return render_template('dashboard.html',
                                applications=applications,
                                current_sort=sort_field,
                                current_order=sort_order,
                                search_query=search_query)

    @app.route('/application_details/<application_id>', methods=['GET'])
    @login_required
    def application_details(application_id):
            log_activity("User Application Details", f"User {current_user.contact} viewed details of application {application_id}.")

            # Fetch application with all SNR details
            application = mongo.db.applications.find_one({"application_id": application_id})
            if not application:
                flash('Application not found.', 'warning')
                return redirect(url_for('dashboard'))

            # Convert ObjectId and dates
            application['_id'] = str(application['_id'])
            application['created_at'] = application['created_at'].strftime('%Y-%m-%d %H:%M:%S')
            application['updated_at'] = application['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            # Fetch all related SNRs
            snrs = list(mongo.db.snrs.find({"application_id": application_id}))
            for snr in snrs:
                snr['_id'] = str(snr['_id'])
                snr['created_at'] = snr['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                snr['updated_at'] = snr['updated_at'].strftime('%Y-%m-%d %H:%M:%S')

            application['snr_details'] = snrs

            return render_template('application_details.html', application=application)

    # File processing routes
    @app.route('/upload_csv', methods=['POST'])
    @login_required
    def upload_csv():
            if 'file' not in request.files:
                flash('No file part', 'danger')
                return redirect(url_for('dashboard'))

            file = request.files['file']

            try:
                # Read and process CSV/Excel file
                if file.filename.endswith('.csv'):
                    df = pd.read_csv(file)
                else:
                    df = pd.read_excel(file)

                # Create list to store enhanced results
                processed_results = []

                # Convert all column names to lowercase and strip whitespace from ends of column names
                df.columns = [col.lower().strip() for col in df.columns]

                # Process each row in the CSV
                for _, row in df.iterrows():
                    raw_number = str(row['number'])
                    purpose = row.get('purpose', 'No purpose provided')

                    # Clean and validate number
                    clean_number = preprocess_snr(raw_number)
                    if not clean_number:
                        continue  # Skip invalid numbers
                    print(f"Processing number: {clean_number}")

                    # Create individual query for each number
                    query = f"Check availability for: {clean_number}"
                    collections = determine_collections(clean_number)
                    print(f'collections for {clean_number}', collections)

                    # Perform search operations for this number
                    number_results = perform_search_operations(query, collections)
                    print("Search results structure:", number_results)

                    # Find matching result (use first match)
                    main_result = next(
                        (r for r in number_results if str(r.get('snr')) == clean_number),
                        None
                    )

                    # Create enhanced result entry
                    if main_result:
                        print("Matched result:", main_result)
                        # Extract availability from GPT-formatted structure
                        availability = main_result.get('availability', 'Unknown')

                        processed_result = {
                            'snr': raw_number,
                            'purpose': purpose,
                            'availability': availability,  # Use actual availability status
                            'source': 'csv',
                            'description': main_result.get('description', '')
                        }
                    else:
                        print("No matching result found")
                        processed_result = {
                            'snr': raw_number,
                            'purpose': purpose,
                            'availability': 'Unavailable',
                            'source': 'csv',
                            'description': f'Number {raw_number} not found in registry'
                        }

                    processed_results.append(processed_result)
                print('all results', processed_results)
                return render_template('search.html',
                                    results=processed_results,
                                    query=f"CSV Analysis - {len(df)} numbers processed")

            except Exception as e:
                flash(f'Error processing file: {str(e)}', 'danger')
                return redirect(url_for('/'))

    @app.route('/process_form', methods=['POST'])
    def process_form():
            if 'file' not in request.files:
                flash('No file uploaded', 'danger')
                return redirect(url_for('search'))

            file = request.files['file']

            if file.filename == '':
                flash('No selected file', 'danger')
                return redirect(url_for('search'))

            if not allowed_file(file.filename):
                flash('Invalid file type. Allowed: PNG, JPG, PDF', 'danger')
                return redirect(url_for('search'))

            # Save uploaded file temporarily
            with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
                file.save(tmp_file.name)
                temp_path = tmp_file.name

            try:
                # Process with GPT to extract numbers and purpose
                ocr_result = process_form_with_gpt(temp_path)
                snr_list = ocr_result.get("numbers", [])
                purpose = ocr_result.get("purpose", "No purpose extracted")

                if not snr_list:
                    flash('No telecom numbers found in document', 'warning')
                    return render_template('search.html', results=[], query="")

                # Create search query from OCR results
                query = f"Check availability for: {', '.join(snr_list)} | Purpose: {purpose}"

                # Determine collections based on SNR patterns
                collections = set()
                for snr in snr_list:
                    if len(snr) == 10:
                        if snr.startswith('0800'):
                            collections.update(["tf_status", "applications"])
                        elif snr.startswith('0900'):
                            collections.update(["pr_status", "applications"])
                        else:
                            collections.update(["snr_status", "applications"])
                    else:
                        collections.update(["snr_status", "applications"])

                modified_search = perform_search_operations(query, list(collections))

                # Enhance results with OCR purposes
                for idx, result in enumerate(modified_search):
                    if idx < len(snr_list):
                        result['purpose'] = purpose
                    else:
                        result['purpose'] = "Purpose not extracted"

                # Process purpose data
                for result in modified_search:
                    # Get the purpose dictionary from the result
                    purpose_dict = result.get('purpose', {})

                    # Build the purpose string
                    if isinstance(purpose_dict, dict):
                        name = purpose_dict.get('program_name', 'Unnamed Program')
                        description = purpose_dict.get('program_description', '')

                        # Create combined purpose text
                        purpose_text = name
                        if description:
                            purpose_text += f" - {description}"

                        # Replace the purpose dictionary with the text
                        result['purpose'] = purpose_text

                # Store in session
                session['search_query'] = query
                session['search_results'] = modified_search

                if current_user.is_authenticated:
                    log_activity("OCR Search", f"User {current_user.contact} performed OCR search")

                # Directly render template with results
                if not modified_search:
                    flash('No results found for your query.', 'info')
                    return render_template('search.html', results=[], query=query)

                print(modified_search)
                return render_template('search.html', results=modified_search, query=query)

            except Exception as e:
                flash(f'Document processing failed: {str(e)}', 'danger')
                return redirect(url_for('search'))
            finally:
                if 'temp_path' in locals() and os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except Exception as cleanup_error:
                        print(f"Error cleaning up temp file: {cleanup_error}")

    # Notification routes
    @app.route('/notifications', methods=['GET', 'POST'])
    @login_required
    def view_notifications():
            if request.method == 'POST':
                # Mark selected notifications as read
                notification_ids = request.form.getlist('notification_ids')
                for notification_id in notification_ids:
                    mongo.db.notifications.update_one(
                        {'_id': ObjectId(notification_id)},
                        {'$set': {'read': True}}
                    )
                flash('Notifications marked as read.', 'success')
                return redirect(url_for('view_notifications'))

            # Fetch all notifications for the current user
            notifications = list(mongo.db.notifications.find({
                'recipient': current_user.contact
            }).sort('timestamp', -1))

            return render_template('notifications.html', notifications=notifications)

    @app.route('/notification/<notification_id>', methods=['GET'])
    @login_required
    def view_notification(notification_id):
            # Fetch the notification to validate its existence and determine next steps
            notification = mongo.db.notifications.find_one({'_id': ObjectId(notification_id)})

            if not notification:
                flash('Notification not found.', 'danger')
                return redirect(url_for('view_notifications'))

            # Ensure that the notification belongs to the current user
            if notification['recipient'] != current_user.contact:
                flash('You are not authorized to view this notification.', 'danger')
                return redirect(url_for('view_notifications'))

            # Mark the notification as read
            mongo.db.notifications.update_one(
                {'_id': ObjectId(notification_id)},
                {'$set': {'read': True}}
            )

            # Redirect to the appropriate application or dashboard
            if notification and "Application" in notification['message']:
                if current_user.is_admin:
                    # Admin user - Redirect to the application review page
                    return redirect(url_for('review_applications'))
                else:
                    # Non-admin user (applicant) - Redirect to the application status page
                    application_id = notification['message'].split()[1]  # Extract application ID from the message
                    return redirect(url_for('view_application_status', application_id=application_id))

            # Default redirection to the user's dashboard if no specific handling is required
            return redirect(url_for('dashboard'))

    @app.route('/application_status/<application_id>', methods=['GET'])
    @login_required
    def view_application_status(application_id):
            # Fetch the application details for the user
            application = mongo.db.applications.find_one({
                'application_id': application_id,
                'user_id': current_user.id  # Ensure user only sees their own applications
            })

            if not application:
                flash('You are not authorized to view this application or it does not exist.', 'danger')
                return redirect(url_for('dashboard'))

            return render_template('applications_status.html', application=application)

        # Certificate generation routes
    @app.route('/generate_certificate_form/<application_id>', methods=['GET'])
    @login_required
    def generate_certificate_form(application_id):
            application = mongo.db.applications.find_one({'application_id': application_id})
            if not application:
                flash('Application not found.', 'danger')
                return redirect(url_for('review_applications'))

            application_snrs = list(mongo.db.snrs.find({'application_id': application_id}))
            number = application_snrs[0]['snr'] if application_snrs else None

            default_start_date = datetime.now().date()
            default_expiry_date = default_start_date + timedelta(days=365)

            if number:
                status, old_cert_sdate_str, old_cert_edate_str, applicant_name = Check_SNR_EXPIRY(number)
                print(f'{status} from {old_cert_sdate_str} - {old_cert_edate_str} for {applicant_name}')

                # Dispatch Table for Date Handling
                date_handlers = {
                    "RENEWAL": handle_renewal_dates,
                    "NEW": handle_new_certificate_dates,
                    "UNAVAILABLE": handle_reprint_dates,
                    "ERROR": handle_error_dates,
                    None: handle_new_certificate_dates
                }

                handler = date_handlers.get(status, handle_default_dates)
                default_dates_info = handler(
                    status=status,
                    old_cert_sdate_str=old_cert_sdate_str,
                    old_cert_edate_str=old_cert_edate_str,
                    applicant_name=applicant_name,
                    application=application,
                    number=number
                )

                default_start_date = default_dates_info['start_date']
                default_expiry_date = default_dates_info['expiry_date']
                reprint_template = default_dates_info.get('reprint_template')
                if reprint_template:
                    return reprint_template

            log_activity("Generate Certificate Form", f"User {current_user.contact} viewed certificate generation form for application {application_id}.")
            return render_template(
                'generate_certificate.html',
                application=application,
                application_snrs=application_snrs,
                default_start_date=default_start_date,
                default_expiry_date=default_expiry_date
            )

    @app.route('/generate_certificate/<application_id>', methods=['POST'])
    @login_required
    def generate_certificate(application_id):
            application = mongo.db.applications.find_one({'application_id': application_id})

            if not application:
                flash('Application not found.', 'danger')
                return redirect(url_for('review_applications'))

            start_date_str = request.form.get('start_date')
            expiry_date_str = request.form.get('expiry_date')

            if not start_date_str or not expiry_date_str:
                flash('Please provide both start and expiry dates.', 'danger')
                return redirect(url_for('generate_certificate_form', application_id=application_id))

            try:
                start_date = parser.parse(start_date_str).date()
                expiry_date = parser.parse(expiry_date_str).date()
            except ValueError:
                flash('Invalid date format. Please use YYYY-MM-DD.', 'danger')
                return redirect(url_for('generate_certificate_form', application_id=application_id))

            cert_id = application['application_id']
            company = application.get('company', 'Unknown Company')
            email_address = application.get('email', 'No email provided')
            filename = f'{company}_certificate_{cert_id}.pdf'
            filepath = os.path.join('certificates', filename)

            if not os.path.exists('certificates'):
                os.makedirs('certificates')

            # Fetch SNRs for this application
            application_snrs = list(mongo.db.snrs.find({'application_id': application_id}))

            # Generate certificate PDF (function implementation not shown for brevity)
            generate_certificate_pdf(
                filepath,
                company,
                email_address,
                application_snrs,
                start_date,
                expiry_date,
                cert_id
            )

            # Update application and SNRs in database
            mongo.db.applications.update_one(
                {'application_id': application_id},
                {
                    '$set': {
                        'certificate_generated': True,
                        'certificate_start_date': start_date_str,
                        'certificate_expiry_date': expiry_date_str,
                        'processed_by': current_user.contact
                    }
                }
            )

            # Update each SNR document with certificate expiry date
            for snr_doc in application_snrs:
                mongo.db.snrs.update_one(
                    {'_id': snr_doc['_id']},
                    {'$set': {'certificate_expiry_date': expiry_date_str, 'status':'certificate_generated'}},
                )

                # Update the SNR status collection
                snr_collection = get_status_table(snr_doc['snr'])
                mongo.db[snr_collection].update_one(
                    {'SNR': snr_doc['snr']},
                    {'$set': {'STATUS': 'ASSIGNED', 'EXPIRY': expiry_date_str, 'UPDATED_AT': datetime.now(timezone.utc)}}
                )

            log_activity("Generated Certificate", f"User {current_user.contact} generated a certificate for application {application_id}.")
            return redirect(url_for('download_certificate', filename=filename))

    @app.route('/download_certificate/<filename>')
    @login_required
    def download_certificate(filename):
            certificates_dir = os.path.join(app.root_path, 'certificates')
            filepath = os.path.join(certificates_dir, filename)
            if not os.path.exists(filepath):
                flash('Certificate not found.', 'danger')
                return redirect(url_for('review_applications'))
            return send_file(filepath, as_attachment=True)

        # Admin routes
    @app.route('/review_applications', methods=['GET', 'POST'])
    @admin_required
    def review_applications():
            if not current_user.is_admin:
                log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to view Admin dashboard with a non-admin account.")
                flash("You do not have permission to access this page.", "danger")
                return redirect(url_for('dashboard'))

            if request.method == 'POST':
                date_today = datetime.strftime(datetime.now().date(), "%Y-%m-%d")

                # Check if there is an index created today. If not, create one.
                index_check = mongo.db.index_check.find_one({'created_date': date_today})

                if not index_check:
                    # Create indexes if they don't exist (run once during setup)
                    mongo.db.applications.create_index([("application_id", 1)])
                    mongo.db.applications.create_index([("status", 1)])
                    mongo.db.applications.create_index([("application_date", -1)])
                    mongo.db.applications.create_index([("applicant_name", "text")])
                    try:
                        index_counter = index_check['index_counter']
                        mongo.db.index_check.insert_one({"index_counter": index_counter+1, "created_at": date_today})
                    except:
                        mongo.db.index_check.insert_one({"index_counter": 1, "created_at": date_today})

            log_activity("Base Admin Dashboard", f"User {current_user.contact} viewed Base Admin Dashboard.")
            return render_template('admin_review_applications.html')

    @app.route('/get_applications', methods=['GET'])
    @login_required
    @cache.cached(timeout=60, query_string=True)
    def get_applications():
            if not current_user.is_admin:
                return jsonify({'error': 'Unauthorized'}), 403

            # Parse parameters
            draw = int(request.args.get('draw', 1))
            start = int(request.args.get('start', 0))
            length = int(request.args.get('length', 10))
            search_value = request.args.get('search[value]', '').strip()
            status = request.args.get('status')
            date_from = request.args.get('date_from')
            date_to = request.args.get('date_to')

            # Build base query
            query_filter = {}

            # Add text search
            if search_value:
                query_filter['$text'] = {'$search': search_value}

            # Add status filter (only if not "All")
            if status and status != "All":
                query_filter['status'] = status

            # Handle date filters
            if date_from or date_to:
                date_filter = {}
                if date_from:
                    date_filter['$gte'] = date_from
                if date_to:
                    date_filter['$lte'] = date_to
                query_filter['application_date'] = date_filter

            print('Final query filter:', query_filter)  # Debugging

            # Get counts
            records_total = mongo.db.applications.estimated_document_count()
            records_filtered = mongo.db.applications.count_documents(query_filter)

            # Get paginated results
            applications = mongo.db.applications.find(
                query_filter,
                {'_id': 0, 'application_id': 1, 'applicant_name': 1, 'status': 1, 'application_date': 1}
            ).sort('application_date', -1).skip(start).limit(length)

            # Format data
            data = []
            for app in applications:
                data.append([
                    app['application_id'],
                    app['applicant_name'],
                    app['status'],
                    app['application_date'],
                    f'<button class="btn btn-sm btn-info details-button" data-id="{app["application_id"]}">View</button>'
                ])

            return jsonify({
                "draw": draw,
                "recordsTotal": records_total,
                "recordsFiltered": records_filtered,
                "data": data
            })

    @app.route('/get_application_details/<application_id>', methods=['GET'])
    @login_required
    def get_application_details(application_id):
            if not current_user.is_admin:
                log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to view an application's details with a non-admin account.")
                return jsonify({'error': 'Unauthorized access'}), 403

            application = mongo.db.applications.find_one({'application_id': application_id})
            snr_details = list(mongo.db.snrs.find({'application_id': application_id}))

            print('these are the snr details', snr_details)

            if not application and snr_details:
                return jsonify({'error': 'Application not found'}), 404

            log_activity("Applications Details View", f"User {current_user.contact} viewed details of applicaton {application_id}.")

            # Create HTML for application details
            details_html = render_application_details_html(application, snr_details)

            footer_html = f"""
            <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            {'<a href="' + url_for('generate_certificate_form', application_id=application_id) + '" class="btn btn-primary">Generate Certificate</a>' if application.get("status") == 'Approved' else ''}
            <a href="{url_for('edit_application', application_id=application_id)}" class="btn btn-warning">Edit Application</a>
            """

            return jsonify({'html': details_html, 'footer_html': footer_html})

    @app.route('/edit_application/<application_id>', methods=['GET', 'POST'])
    @login_required
    def edit_application(application_id):
            if not current_user.is_admin:
                log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to edit an application with a non-admin account.")
                flash("You do not have permission to access this page.", "danger")
                return redirect(url_for('dashboard'))

            # Fetch the application details from the database
            application = mongo.db.applications.find_one({'application_id': application_id})
            snrs = list(mongo.db.snrs.find({'application_id': application_id}))

            if not application:
                flash('Application not found.', 'danger')
                return redirect(url_for('review_applications'))

            change_log = []

            if request.method == 'POST':
                # Collect updated data from the form
                new_status = request.form.get('status')
                new_purposes = []
                for key, value in request.form.items():
                    if key.startswith('purpose_'):
                        new_purposes.append(value)
                new_expiry_date = request.form.get('expiry_date')
                new_applicant_name = request.form.get('applicant_name')
                new_remarks = request.form.get('remarks')

                application_expiry_date = application.get('certificate_expiry_date', 'N/A')

                # Check for changes in application fields
                if new_status != application['status']:
                    message = f"Application {application_id} status changed to {new_status}."
                    add_notification(message, application['applicant_name'])

                    change_log.append({
                        'field': 'status',
                        'old_value': application['status'],
                        'new_value': new_status,
                        'modified_by': current_user.contact,
                        'modified_at': datetime.now(timezone.utc)
                    })
                    application['status'] = new_status

                if new_applicant_name != application['applicant_name']:
                    change_log.append({
                        'field': 'applicant_name',
                        'old_value': application['applicant_name'],
                        'new_value': new_applicant_name,
                        'modified_by': current_user.contact,
                        'modified_at': datetime.now(timezone.utc)
                    })
                    application['applicant_name'] = new_applicant_name

                if new_remarks != application.get('remarks', ''):
                    change_log.append({
                        'field': 'remarks',
                        'old_value': application.get('remarks', ''),
                        'new_value': new_remarks,
                        'modified_by': current_user.contact,
                        'modified_at': datetime.now(timezone.utc)
                    })
                    application['remarks'] = new_remarks

                if new_expiry_date and new_expiry_date != application_expiry_date:
                    change_log.append({
                        'field': 'certificate_expiry_date',
                        'old_value': application_expiry_date,
                        'new_value': new_expiry_date,
                        'modified_by': current_user.contact,
                        'modified_at': datetime.now(timezone.utc)
                    })
                    application['certificate_expiry_date'] = new_expiry_date

                # Update application collection with change_log
                mongo.db.applications.update_one(
                    {'application_id': application_id},
                    {
                        '$set': application,
                        '$push': {'change_log': {'$each': change_log}}
                    }
                )

                # Update SNRs collection for the specific application
                for idx, snr in enumerate(snrs):
                    if idx < len(new_purposes) and new_purposes[idx] and new_purposes[idx] != snr.get('purpose'):
                        mongo.db.snrs.update_one(
                            {'_id': snr['_id']},
                            {'$set': {'purpose': new_purposes[idx]}}
                        )
                        change_log.append({
                            'field': 'purpose',
                            'old_value': snr['purpose'],
                            'new_value': new_purposes[idx],
                            'modified_by': current_user.contact,
                            'modified_at': datetime.now(timezone.utc)
                        })

                    # Update status in the SNR collection
                    if new_status and new_status != snr.get('status'):
                        mongo.db.snrs.update_one(
                            {'_id': snr['_id']},
                            {'$set': {'status': new_status}}
                        )
                        change_log.append({
                            'field': 'status',
                            'old_value': snr['status'],
                            'new_value': new_status,
                            'modified_by': current_user.contact,
                            'modified_at': datetime.now(timezone.utc)
                        })

                    # Update expiry date in the SNR collection
                    if new_expiry_date and new_expiry_date != snr.get('certificate_expiry_date', 'N/A'):
                        mongo.db.snrs.update_one(
                            {'_id': snr['_id']},
                            {'$set': {'certificate_expiry_date': new_expiry_date}}
                        )
                        change_log.append({
                            'field': 'certificate_expiry_date',
                            'old_value': snr.get('certificate_expiry_date', 'N/A'),
                            'new_value': new_expiry_date,
                            'modified_by': current_user.contact,
                            'modified_at': datetime.now(timezone.utc)
                        })

                # Save the changes to the change_log
                if change_log:
                    mongo.db.applications.update_one(
                        {'application_id': application_id},
                        {
                            '$push': {'change_log': {'$each': change_log}}
                        }
                    )

                # Log the activity and flash a success message
                flash('Application details updated successfully.', 'success')
                log_activity("Admin Application Editing", f"User {current_user.contact} successfully edited details of application {application_id}.")
                return redirect(url_for('review_applications'))

            return render_template('edit_application.html', application=application, snrs=snrs, datetime=datetime, timedelta=timedelta)

    # Report generation routes
    @app.route('/generate_report', methods=['GET'])
    @admin_required
    def generate_report():
            log_activity("Report Generation", f"User {current_user.contact} viewed Admin Report generation page.")
            return render_template('reports.html')


    @app.route('/generate_natural_report', methods=['POST'])
    @login_required
    def generate_natural_report():
            if not current_user.is_admin:
                log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to generate a natural language report.")
                return jsonify({'error': 'Unauthorized access'}), 403

            user_input = request.json.get('description', '')
            if not user_input:
                return jsonify({'error': 'Please provide a valid report description'}), 400

            # Use GPT-4o-mini to analyze user input and determine relevant collections
            collections = determine_reports_collections(user_input)
            print('collections', collections)  # Debugging output

            mongodb_queries_full = create_report_mongodb_queries_with_gpt(user_input, collections)

            print('mongodb_queries_full', mongodb_queries_full)  # Debugging output
            report_type = mongodb_queries_full.get("report_type", 'Custom Report')
            mongodb_queries = mongodb_queries_full.get('queries', {})
            limits = mongodb_queries_full.get('limit')

            # Determine if join is needed based on collections involved
            use_applications = 'applications' in mongodb_queries and mongodb_queries['applications']
            use_snrs = 'snrs' in mongodb_queries and mongodb_queries['snrs']

            search_results = []

            if use_applications and use_snrs:
                # When both are needed, default to using pipeline1
                applications_query = mongodb_queries.get('applications', {})

                aggregation_pipeline = [
                    {"$match": applications_query},
                    {"$lookup": {
                        "from": 'snrs',
                        "localField": 'application_id',
                        "foreignField": 'application_id',
                        "as": 'snr_details'
                    }},
                    {"$unwind": {"path": '$snr_details'}}
                ]

                # Add limit if applicable
                if limits and limits > 0:
                    aggregation_pipeline.append({"$limit": limits})

                # Perform aggregation on applications collection
                print('performing aggregation on applications collection')
                search_results = list(mongo.db.applications.aggregate(aggregation_pipeline))

            elif use_applications:
                # Only applications collection is available, use pipeline1
                applications_query = mongodb_queries.get('applications', {})

                aggregation_pipeline = [
                    {"$match": applications_query},
                    {"$lookup": {
                        "from": 'snrs',
                        "localField": 'application_id',
                        "foreignField": 'application_id',
                        "as": 'snr_details'
                    }},
                    {"$unwind": {"path": '$snr_details'}}
                ]

                # Add limit if applicable
                if limits and limits > 0:
                    aggregation_pipeline.append({"$limit": limits})

                # Perform aggregation on applications collection
                print('performing aggregation on applications collection')
                search_results = list(mongo.db.applications.aggregate(aggregation_pipeline))

            elif use_snrs:
                # Only snrs collection is available, use pipeline2
                snrs_query = mongodb_queries.get('snrs', {})

                aggregation_pipeline = [
                    {"$match": snrs_query},
                    {"$lookup": {
                        "from": 'applications',
                        "localField": 'application_id',
                        "foreignField": 'application_id',
                        "as": 'snr_details'
                    }},
                    {"$unwind": {"path": '$snr_details'}}
                ]

                # Add limit if applicable
                if limits and limits > 0:
                    aggregation_pipeline.append({"$limit": limits})

                # Perform aggregation on snrs collection
                print('performing aggregation on snrs collection')
                search_results = list(mongo.db.snrs.aggregate(aggregation_pipeline))

            else:
                # Perform individual collection queries if neither applications nor snrs requires join
                print('Perform individual collection queries if neither applications nor snrs requires join')
                for collection_name, mongo_query in mongodb_queries.items():
                    if mongo_query:
                        collection = mongo.db[collection_name]
                        if limits is None or limits == 'null':
                            search_results += list(collection.find(mongo_query))
                        else:
                            search_results += list(collection.find(mongo_query).limit(limits))

            # Format the search results
            flattened_results = [flatten_document(doc) for doc in search_results]

            for result in flattened_results:
                convert_object_types(result)

            print('search results flattened: ', flattened_results)  # Debugging output

            # Convert results to a DataFrame
            df = pd.DataFrame(flattened_results)

            if df.empty:
                return jsonify({'error': 'No data found for the given criteria'}), 404

            # Add additional calculated columns
            if 'snr_details_snr' in df.columns:
                df['category'] = df['snr_details_snr'].apply(lambda x: snr_length_finder(x))
                df["fees"] = df['category'].apply(lambda x: PRICING.get(x, 0))
            elif 'snr' in df.columns:
                df['category'] = df['snr'].apply(lambda x: snr_length_finder(x))
                df["fees"] = df['category'].apply(lambda x: PRICING.get(x, 0))

            print('df columns and 5 rows: ', df.columns, df.head(5))  # Debugging output

            # Generate summary with GPT-4o-mini
            search_results_summary = generate_report_prose_with_gpt(df, report_type)

            # Generate chart based on GPT suggestion
            chart_json = generate_chart_suggestion(df)

            log_activity("Report Generated", f"User {current_user.contact} generated a Natural Language Report.")

            # Send JSON response with summary and graph
            return jsonify({
                "summary": search_results_summary,
                "visualization": chart_json
            })

    @app.route('/generate_static_report', methods=['POST'])
    @login_required
    def generate_static_report():
            if not current_user.is_admin:
                log_activity("Unapproved Attempt to Restricted Area", f"User {current_user.contact} attempted to generate a static report with a non-admin account.")
                return jsonify({'error': 'Unauthorized access'}), 403

            # Parse the structured form inputs from the request
            form_data = request.json
            report_type = form_data.get('report_type', 'monthly')
            number_type = form_data.get('number_type', 'applications')
            date_range = form_data.get('date_range', '').split(' to ')

            if len(date_range) != 2:
                return jsonify({'error': 'Invalid date range provided'}), 400

            start_date_str = date_range[0].strip()
            end_date_str = date_range[1].strip()

            # Fetch applications from MongoDB based on date range
            applications_cursor = mongo.db.applications.find(
                {
                    "application_date": {"$gte": start_date_str, "$lte": end_date_str}
                }
            )
            applications_list = list(applications_cursor)
            if not applications_list:
                return jsonify({'error': 'No application data found for the given criteria'}), 404
            applications_df = pd.DataFrame(applications_list)

            # Fetch all SNRs from MongoDB
            snrs_cursor = mongo.db.snrs.find()
            snrs_list = list(snrs_cursor)
            snrs_df = pd.DataFrame(snrs_list)
            if snrs_df.empty:
                return jsonify({'error': 'No SNR data found in the database'}), 404

            # Merge DataFrames using application_id as the key
            merged_df = pd.merge(applications_df, snrs_df, on='application_id', suffixes=('_app', '_snr'), how='left')
            if merged_df.empty:
                return jsonify({'error': 'No data found after merging applications and SNRs'}), 404

            df = merged_df.copy()  # Use a copy to avoid modifying original merged_df

            # Convert ObjectId columns to strings
            df = convert_object_ids_and_timestamps_to_str(df)

            # Add category based on SNR
            if 'snr' in df.columns:
                df['category'] = df['snr'].apply(lambda x: snr_length_finder(x))
                # Apply filters based on number type
                number_type_filters = {
                    'applications': lambda df: df,
                    'snr_status': lambda df: df[df['category'].isin(["3 Digit", "4 Digit", "5 Digit", "6 Digit"])],
                    'tf_status': lambda df: df[df['category'] == "Tollfree"],
                    'pr_status': lambda df: df[df['category'] == "Premium"],
                }
                df = number_type_filters.get(number_type, lambda df: df)(df)

                # Add fees column
                df["fees"] = df['category'].apply(lambda x: PRICING.get(x, 0))

            # Generate visualizations using GPT suggestion
            visualizations = generate_multiple_charts(df)

            # Create reduced dataframe for display
            reduced_df = df[['snr', 'status_app', 'category', 'fees']].copy()
            reduced_df = reduced_df.rename(columns={'status_app': 'status'})

            # Group by category and summarize
            reduced_df_summary = reduced_df.groupby('category').agg(
                total_count=('category', 'size'),
                total_fees=('fees', 'sum')
            ).reset_index()

            # Generate table visualizations
            table_json = create_table_visualization(reduced_df)
            table_json2 = create_table_visualization(reduced_df_summary)

            # Generate summary
            summary = generate_report_prose_with_gpt(df.copy().astype(str), report_type)

            log_activity("Report Generated", f"User {current_user.contact} generated a Static Language Report.")

            return jsonify({
                "summary": summary,
                "visualizations": visualizations,
                "dataframe_visualization": json.loads(table_json),
                "dataframe_visualization2": json.loads(table_json2),
                "reduced_dataframe": reduced_df.to_dict(orient='records'),
                "reduced_dataframe_summary": reduced_df_summary.to_dict(orient='records')
            })

    @app.route('/activity_log_summary', methods=['GET'])
    @login_required
    @admin_required
    def activity_log_summary():
            log_activity("Activity Log Summary", f"User {current_user.contact} viewed Activity Log Summary.")
            # Fetch and group activities by user and action over time
            aggregation_pipeline = [
                {"$group": {
                    "_id": {"user_id": "$user_id", "action": "$action"},
                    "total": {"$sum": 1},
                    "first_action": {"$min": "$timestamp"},
                    "last_action": {"$max": "$timestamp"}
                }},
                {"$sort": {"last_action": -1}}  # Sort descending
            ]

            activity_summary = list(mongo.db.activity_log.aggregate(aggregation_pipeline))

            # Prepare data for visualization
            activities = []
            for activity in activity_summary:
                activities.append({
                    "user_id": activity["_id"]["user_id"],
                    "action": activity["_id"]["action"],
                    "total": activity["total"],
                    "first_action": activity["first_action"].strftime('%Y-%m-%d %H:%M:%S'),
                    "last_action": activity["last_action"].strftime('%Y-%m-%d %H:%M:%S')
                })

            return jsonify(activities)

        # Add API routes
    register_api_routes(app)

    # Application cleanup
    @app.route('/application/cleanup', methods=['POST'])
    def cleanup_applications():
            session.pop('pending_application', None)
            return '', 204

        # Create the index for the application
    return app

def register_api_routes(app):
    """Register API routes for the application."""
    # E.164 SNR API routes
    @app.route('/api/snrs/approved', methods=['GET'])
    def get_approved_snrs():
        """Get all approved SNRs that are ready for implementation by operators."""
        try:
            # Get aggregated data
            aggregated_data = aggregate_applications_and_snrs()

            # Filter for approved SNRs that don't have implementation status yet
            approved_snrs = []
            for record in aggregated_data:
                if record.get('snr_data', {}).get('status') == 'Approved' or record.get('snr_data', {}).get('status') == 'certificate_generated':
                    # Create a simplified record with essential fields
                    approved_snr = {
                        "application_id": record.get('application_id'),
                        "applicant_name": record.get('applicant_name'),
                        "company": record.get('company'),
                        "application_date": record.get('application_date'),
                        "snr": record.get('snr_data', {}).get('snr'),
                        "type": record.get('snr_data', {}).get('type'),
                        "purpose": record.get('snr_data', {}).get('purpose'),
                        "status": record.get('snr_data', {}).get('status'),
                        "certificate_id": record.get('snr_data', {}).get('certificate_id'),
                        "implementation_status": record.get('snr_data', {}).get('implementation_status', None)
                    }
                    approved_snrs.append(approved_snr)

            log_activity("API Request", f"Operator API requested list of approved SNRs")
            return jsonify(approved_snrs)
        except Exception as e:
            log_activity("API Error", f"Error in get_approved_snrs: {str(e)}")
            return jsonify({"error": f"Error retrieving approved SNRs: {str(e)}"}), 500

    @app.route('/api/snrs/<certificate_id>/claim', methods=['POST'])
    def claim_snr_implementation(certificate_id):
        """Claim a specific SNR for implementation by an operator."""
        data = request.json
        operator_name = data.get('operator')

        if not operator_name:
            return jsonify({"error": "Operator name is required"}), 400

        # Use a method to update SNR status
        result = update_snr_status(
            certificate_id=certificate_id,
            new_status="active",
            operator_name=operator_name
        )

        if result.get("success", False):
            log_activity("SNR Claim", f"Operator {operator_name} claimed SNR {result.get('snr')}")
            return jsonify(result), 200
        else:
            return jsonify(result), 400

    @app.route('/api/search', methods=['GET'])
    def search_snr():
        """API to search for SNRs by query."""
        query = request.args.get('query')
        search_results = mongo.db.applications.aggregate([
            {"$unwind": "$snrs"},
            {"$match": {"$or": [
                {"snrs.snr": query},
                {"snrs.certificate_id": query},
                {"applicant_name": {"$regex": query, "$options": "i"}}
            ]}},
            {"$project": {
                "application_id": 1,
                "snrs.snr": 1,
                "snrs.type": 1,
                "snrs.purpose": 1,
                "snrs.status": 1,
                "snrs.certificate_id": 1,
                "status": 1,
                "applicant_name": 1
            }}
        ])
        return jsonify(list(search_results))

    @app.route('/verify_certificate/<certificate_id>')
    def verify_certificate(certificate_id):
        """Verify a certificate by its ID."""
        application = mongo.db.applications.find_one({'application_id': certificate_id})
        if not application:
            return jsonify({'valid': False, 'message': 'Certificate ID not found.'}), 404

        expiry_date_str = application.get('certificate_expiry_date')
        if not expiry_date_str:
            return jsonify({'valid': False, 'message': 'Expiry date not set for this certificate.'}), 400

        try:
            expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'valid': False, 'message': 'Invalid expiry date format in database.'}), 500

        is_expired = datetime.now().date() > expiry_date
        status_message = "Certificate is VALID." if not is_expired else "Certificate is EXPIRED."
        validity_status = not is_expired

        certificate_info = {
            'valid': validity_status,
            'message': status_message,
            'certificate_id': application['application_id'],
            'applicant_name': application['applicant_name'],
            'effective_date': application.get('certificate_start_date'),
            'expiry_date': expiry_date_str
        }

        return jsonify(certificate_info), 200

    # Webhook routes for SNR notifications
    @app.route('/webhook/register', methods=['POST'])
    def register_webhook():
        """Register a webhook URL for notifications about SNR status changes."""
        data = request.json
        operator_name = data.get('operator')
        webhook_url = data.get('webhook_url')
        events = data.get('events', ['snr_approved', 'snr_claimed', 'snr_implemented', 'snr_deactivated'])

        if not operator_name or not webhook_url:
            return jsonify({"error": "Operator name and webhook URL are required"}), 400

        # Validate URL format
        try:
            result = requests.head(webhook_url, timeout=5)
        except requests.RequestException:
            return jsonify({"error": "Invalid webhook URL or endpoint not reachable"}), 400

        # Store the webhook configuration
        webhook_config = {
            "operator": operator_name,
            "webhook_url": webhook_url,
            "events": events,
            "created_at": datetime.now(timezone.utc),
            "last_updated": datetime.now(timezone.utc),
            "active": True
        }

        mongo.db.webhooks.update_one(
            {"operator": operator_name},
            {"$set": webhook_config},
            upsert=True
        )

        log_activity("Webhook Registration", f"Operator {operator_name} registered webhook for events: {', '.join(events)}")

        return jsonify({
            "message": "Webhook registered successfully.",
            "operator": operator_name,
            "events": events
        }), 200

    # API to run SNR audit update
    @app.route('/api/snrs/audit/update', methods=['POST'])
    @login_required
    @admin_required
    def run_snr_audit_update():
        """API endpoint to manually trigger SNR audit update."""
        data = request.json
        operator_name = data.get('operator')

        try:
            updates = update_audit_snr_counts(operator_name)
            log_activity("SNR Audit Update", f"User {current_user.contact} triggered SNR audit update for {operator_name or 'all operators'}")
            return jsonify({
                "message": f"Updated SNR audit counts for {len(updates)} operators",
                "updates": updates
            }), 200
        except Exception as e:
            log_activity("SNR Audit Update Error", f"Error updating SNR audit counts: {str(e)}")
            return jsonify({"error": f"Error updating SNR audit counts: {str(e)}"}), 500

# Helper functions

def generate_ids():
    """Generate unique application and certificate IDs."""
    application_id = f"APP{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
    certificate_id = f"CERT{datetime.now(timezone.utc).strftime('%Y%m%d%H%M%S')}{ObjectId()}"
    return application_id, certificate_id

def determine_collections(query):
    """Determine which MongoDB collections to search based on the query."""
    query = query.lower()
    collections = ['applications']

    # Keywords for determining collections
    if re.search(r'\bshortcode\b|\b3 digit\b|\b4 digit\b|\b5 digit\b|\b6 digit\b|^[0-9]{3,6}$', query):
        collections.append('snr_status')

    if re.search(r'\btoll-?free\b|\b0800\b|^0800[0-9]{6}$', query):
        collections.append('tf_status')

    if re.search(r'\bpremium-?rate\b|\b0900\b|^0900[0-9]{6}$', query):
        collections.append('pr_status')

    # If the query contains general terms like "application", "history", "details", or an applicant's name
    if re.search(r'\bapplication\b|\bhistory\b|\bapplicant\b|\bdetails\b|^[0-9]{3,6}$', query):
        collections.append('applications')

    # Include 'snrs' collection to ensure completeness of SNR data
    collections.append('snrs')

    return list(set(collections))  # Remove duplicates

def determine_reports_collections(query):
    """Determine collections for report generation."""
    query = query.lower()
    collections = ['applications']  # Default collections

    # Determine specific status tables if a number type is referenced
    if re.search(r'\bshortcode\b|\b3 digit\b|\b4 digit\b|\b5 digit\b|\b6 digit\b|^[0-9]{3,6}$', query):
        collections.append('snr_status')

    if re.search(r'\btoll-?free\b|\b0800\b|^0800[0-9]{6}$', query):
        collections.append('tf_status')

    if re.search(r'\bpremium-?rate\b|\b0900\b|^0900[0-9]{6}$', query):
        collections.append('pr_status')

    return list(set(collections))

def aggregate_applications_and_snrs():
    """Aggregate data from applications and SNRs collections."""
    try:
        pipeline = [
            {
                "$lookup": {
                    "from": "snrs",
                    "localField": "application_id",
                    "foreignField": "application_id",
                    "as": "snr_data"
                }
            },
            {
                "$unwind": {
                    "path": "$snr_data",
                    "preserveNullAndEmptyArrays": True
                }
            }
        ]

        aggregated_data = list(mongo.db.applications.aggregate(pipeline))
        return aggregated_data
    except Exception as e:
        print(f"Error aggregating applications and snrs: {e}")
        return []

def create_mongodb_queries_with_gpt(query, collections):
    """Create MongoDB queries using GPT-4o-mini."""
    prompt = f"""
    You are an assistant helping to create MongoDB queries based on the user's search intent. First, explain your thought process clearly based on the user's query. Then generate the MongoDB query based on that explanation.
    - The 'SNR' field is stored as a string in the database.
    - If the user's query involves numeric values, DO NOT use exact match with regex like "^1234$". Instead, use a more flexible approach:
      - For 'snr_status', use a regex that contains the number, like: {{"SNR": {{"$regex": "1234", "$options": "i"}}}}
      - For 'applications', search in multiple fields: application_id, applicant_name, remarks
      - For 'snrs', search in the snr field with a contains approach, not exact match
    - Generate MongoDB queries that accurately reflect the user's search while handling type mismatches appropriately.
    - IMPORTANT: When searching for numbers, use a contains approach rather than exact match to ensure we find partial matches.

    Collections to be queried:
    - 'snr_status' (shortcodes, including fields: SNR, STATUS, EXPIRY, ASSIGNEE). The SNRs in this collection are 3, 4, 5, and 6-digit shortcodes.
    - 'tf_status' (toll-free numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE).
    - 'pr_status' (premium-rate numbers, including fields: SNR, STATUS, EXPIRY, ASSIGNEE).
    - 'applications' (application history, including fields: applicant_id, user_id, applicant_name, application_date, status, remarks, created_at, updated_at, certificate_expiry_date, certificate_generated, certificate_start_date, processed_by).
    - 'snrs' (SNR-specific data, including fields: application_id, snr, type, purpose, status, certificate_id, created_at, updated_at).

    User Query: "{query}"
    Collections: {collections}

    Based on the user's query, provide the MongoDB queries for each relevant collection.
    Return your response in JSON format.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )

        reply = response.choices[0].message.content.strip()
        print('reply', reply)  # Print the reply for debugging purposes

        query_data = json.loads(reply)
        return query_data
    except Exception as e:
        print(f"Error creating MongoDB queries with GPT: {e}")
        return {}

def create_report_mongodb_queries_with_gpt(query, collections):
    """Create MongoDB queries for report generation using GPT-4o-mini."""
    prompt = f"""
    You are an assistant helping to create MongoDB queries based on the user's search intent.
    Below are the collections and their respective fields that you can use to generate the MongoDB queries.

    Collections and their available fields:
    - 'snrs': {{
        "_id": ObjectId,
        "snr": string,          # Special Numbering Resource (e.g., "8001")
        "type": string,         # Type of number (e.g., "shortcode", "tollfree", "premium-rate")
        "status": string,       # Status of the SNR (e.g., "Approved", "Available", "Assigned", "Pending Review", "Reserved", "Certifcate_printable" )
        "purpose": string,      # Purpose for which the SNR is requested
        "certificate_id": string,
        "expiry_date": date,    # Expiry date of the number if applicable
    }}

    - 'applications': {{
        "_id": ObjectId,
        "application_id": string,
        "applicant_name": string,
        "email": string,
        "contact": string,
        "phone": string,
        "company": string,
        "application_date": date,  # Date of the application submission
        "status": string,          # Status of the application (e.g., "Pending Review", "Approved", "Rejected")
        "snrs": array,             # List of linked SNRs to the application
        "remarks": string,
        "created_at": date,
        "updated_at": date
    }}

    - 'snr_status', 'tf_status', 'pr_status': {{
        "SNR": string,            # Numbering Resource identifier
        "STATUS": string,         # Status of the SNR ("AVAILABLE", "EXPIRED", "ASSIGNED", "RESERVED")
        "EXPIRY": date,           # Expiry date if applicable
        "ASSIGNEE": string        # Name of the assignee if assigned
    }}

    User Query: "{query}"
    Collections to Query: {collections}

    Provide the report type and MongoDB queries for each relevant collection in JSON format.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )

        reply = response.choices[0].message.content.strip()
        query_data = json.loads(reply)
        print('query_data', query_data)  # Debugging line
        return query_data
    except Exception as e:
        print(f"Error creating MongoDB queries with GPT: {e}")
        return {}

def perform_search_operations(query, collections):
    """Execute search operations on MongoDB collections."""
    mongodb_queries_full = create_mongodb_queries_with_gpt(query, collections)
    
    if not isinstance(mongodb_queries_full.get('queries'), dict):
        current_app.logger.error(f"OpenAI API did not return a valid 'queries' dictionary. Response: {mongodb_queries_full}")
        # It's better to return an empty list or specific error structure from here
        return [] # Return empty list indicating no valid queries were formed
        
    mongodb_queries = mongodb_queries_full.get('queries', {})
    limits = mongodb_queries_full.get('limit') 

    if limits is not None:
        try:
            limits = int(limits)
            if limits <= 0: 
                limits = 10 
        except (ValueError, TypeError):
            current_app.logger.warning(f"Invalid limit value received: {limits}. Defaulting.")
            limits = 10 
    else:
        limits = 10 

    search_results_raw = [] # Renamed from search_results to avoid confusion
    for collection_name, query_wrapper in mongodb_queries.items():
        if query_wrapper and isinstance(query_wrapper, dict):
            actual_mongo_query = query_wrapper.get('query') 

            if actual_mongo_query and isinstance(actual_mongo_query, dict):
                try:
                    collection = mongo.db[collection_name]
                    current_app.logger.info(f"Executing find on '{collection_name}' with query: {actual_mongo_query}, limit: {limits}")
                    
                    cursor = collection.find(actual_mongo_query)
                    if limits is not None: 
                        cursor = cursor.limit(limits)
                    search_results_raw.extend(list(cursor))
                except Exception as e:
                    current_app.logger.error(f"Error querying collection '{collection_name}' with query {actual_mongo_query}: {e}")
            else:
                current_app.logger.warning(f"No valid 'query' object found in wrapper for collection '{collection_name}': {query_wrapper}")
        else:
            current_app.logger.warning(f"Invalid or empty query wrapper for collection '{collection_name}': {query_wrapper}")

    processed_search_results = []
    for result_item in search_results_raw: # Corrected variable name here
        for key, value in result_item.items():
            if isinstance(value, ObjectId):
                result_item[key] = str(value)
            elif isinstance(value, datetime):
                result_item[key] = value.isoformat()
        processed_search_results.append(result_item)
    
    current_app.logger.info(f"Raw search results ({len(processed_search_results)} items) before formatting by GPT: {str(processed_search_results)[:500]}...")

    if not processed_search_results:
        current_app.logger.info("No results found from database queries.")
        return [] # Return an empty list

    # --- CRITICAL CHANGE HERE ---
    # The create_results_with_gpt function returns a dict like {'results': LIST_OF_ITEMS}
    # We need to extract the LIST_OF_ITEMS.
    gpt_formatted_output = create_results_with_gpt(processed_search_results)
    current_app.logger.info(f"Formatted results by GPT (type: {type(gpt_formatted_output)}): {str(gpt_formatted_output)[:500]}...")
    
    if isinstance(gpt_formatted_output, dict) and 'results' in gpt_formatted_output:
        final_results_list = gpt_formatted_output['results']
    elif isinstance(gpt_formatted_output, list): # If GPT sometimes returns a list directly
        final_results_list = gpt_formatted_output
    else:
        current_app.logger.error(f"Unexpected format from create_results_with_gpt: {gpt_formatted_output}")
        final_results_list = [] # Default to empty list on unexpected format
        flash('Error interpreting search results from AI.', 'danger')
        
    return final_results_list




def create_results_with_gpt(search_results):
    """Format search results using GPT-4o-mini."""
    def serialize_dates(obj):
        """Recursively convert datetime objects to ISO strings"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: serialize_dates(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [serialize_dates(item) for item in obj]
        elif isinstance(obj, ObjectId):
            return str(obj)
        return obj

    prompt = f"""
    You are an assistant helping to create search result descriptions based on the search results.

    Given the following search results, if the results given to you are of the same subject e.g. the same SNR or the same applicant, then compare carefully and summarize the results into one brief summary or a timeline of summaries. if the results do not share a common SNR or Assignee, then generate a descriptive summary for each entry individually. If the results are empty, return an empty list.

    The summary/summaries should be in a format that is easy to understand and read, and should include key information such as number or Special Numbering Resource (SNRs), status, expiry, and assignee where available.
    All references to 'applications' are applications for Special Numbering Resource (SNRs) such as short codes for USSD and SMS mobile applications, and toll-free and premium-rate numbers for toll free voice and premium rate voice applications.

    Search Results: {json.dumps(serialize_dates(search_results))}

    Your response must be in JSON format without any extraneous words or additional explanations.
    It should strictly be a JSON array where each element is an object with the following keys:
    - "snr": SNR, the Special Numbering Resource (SNR) number i.e. the shortcode, toll-free number or premium-rate number. If the SNR is not available, this field should be "N/A".
    - "description": A descriptive summary for the SNR or the application.
    - "availability": Status of availability. Compare the status of the SNR in the collections to determine the availability. If the SNR is available, this field should be "Available". If the SNR is not available, this field should be "Not Available". If the availability status is not clear from the data, this field should be "Unknown".
    - "applicant_info": An object containing:
        - "status": The current status of the application (e.g., "Approved", "Pending").
  - "expiry": The expiry date of the SNR.
        - "assignee": The name of the assignee.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )

        reply = response.choices[0].message.content.strip()
        print('formatted search results', reply)

        results = json.loads(reply)
        return results
    except Exception as e:
        print(f"Error creating results with GPT: {e}")
        return []

def generate_report_prose_with_gpt(data, report_type="General"):
    """Generate natural language report from data."""
    # Custom JSON serializer for DataFrame elements
    def custom_serializer(obj):
        if isinstance(obj, (datetime, pd.Timestamp)):
            return obj.isoformat()
        if isinstance(obj, ObjectId):
            return str(obj)
        raise TypeError(f"Type {type(obj)} not serializable")

    # Convert data to JSON-serializable format
    if isinstance(data, pd.DataFrame):
        json_data = data.to_json(orient='records', date_format='iso')
    else:
        json_data = json.dumps(data, indent=2, default=custom_serializer)

    prompt = f"""
    You are an assistant that creates detailed natural language summaries of data for a report.
    Here is the data for the report. It represents a {report_type}:

    Data: {json_data}

    Please provide an executive summary for this data. Your response should highlight key statistics, trends, and observations,
    such as the number of assigned versus available SNRs, the total fees collected, the number of applications by category,
    and any notable observations or anomalies. The summary should be written in a professional tone suitable for a formal report.

    Your response must be in JSON format with the following keys:
    - "report": A detailed natural language summary of the data for the report, capturing an overview, statistics, and key findings.
    - "statistics": An object containing key statistical data, including:
        - "total_applications": Total number of applications processed.
        - "assigned_snr": Number of SNRs that have been assigned.
        - "total_fees_collected": Total amount of fees collected.
        - "applications_by_category": An object that further breaks down the applications by category (e.g., "Tollfree", "Shortcode").
    - "observations": An array of any notable observations or trends identified in the data. Each observation should be a brief natural language description.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )

        reply = response.choices[0].message.content.strip()
        results = json.loads(reply)

        # Ensure summary is a list if needed
        if not isinstance(results, list):
            results = [results]

        return results
    except Exception as e:
        print(f"Error generating report prose with GPT: {e}")
        return {"error": "Error generating report prose. Please try again later."}

def process_form_with_gpt(application_form_path):
    """Process document using GPT-4 vision."""
    try:
        # Encode image to base64
        with open(application_form_path, "rb") as image_file:
            base64_image = base64.b64encode(image_file.read()).decode('utf-8')

        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {
                "role": "system",
                "content": [
                    {
                    "type": "text",
                    "text": "You are a helpful assistant, helping a team identify useful information from documents and images. Return your response in JSON format."
                    }
                ]
                },
                {
                "role": "user",
                "content": [
                    {
                    "type": "text",
                    "text": "the attached file is a complete application for a telecom numbering resource. please identify the numbers requested by the applicant, normally in the following fields \"Preferred SNR Requested\", \"First Alternative SNR Requested\", \"Second Alternative SNR Requested\". Also include the Program Name and Program description. return the results in the following JSON format:\n{\n\"numbers\": [list of numbers in order of applicant's priority],\n\"purpose\":[program name and purpose]\n}\nIf multiple application forms request different numbers, expand the JSON output to include the other results."
                    },
                    {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                    }
                ]
                },
            ],
            response_format={"type": "json_object"},
            temperature=0.2,
            max_tokens=2048
        )

        # Parse and validate response
        result = json.loads(response.choices[0].message.content)
        print('checking the results', result)

        if not isinstance(result.get('numbers', []), list) or not result.get('purpose'):
            raise ValueError("Invalid GPT response format")

        return result
    except Exception as e:
        raise RuntimeError(f"GPT processing failed: {str(e)}")

def preprocess_snr(snr):
    """Clean and format SNR number."""
    if not snr:
        return None

    # Remove spaces and hyphens
    snr = str(snr).replace(" ", "").replace("-", "")

    # Ensure it's a valid numeric string
    if snr.isdigit():
        return snr
    else:
        return None

def snr_length_finder(snr):
    """Determine SNR type based on length and format."""
    snr = preprocess_snr(snr)  # Preprocess the SNR

    if snr is None:
        return "Undefined"

    # If the snr starts with "0800", it's Tollfree
    if len(snr) >= 9 and (snr.startswith("0800") or snr.startswith("800")):
        ans = "Tollfree"
    elif len(snr) >= 9 and (snr.startswith("0900") or snr.startswith("900")):
        ans = "Premium"
    elif len(snr) == 3 and 100 <= int(snr) <= 999:
        ans = "3 Digit"
    elif len(snr) == 4 and 1000 <= int(snr) <= 9999:
        ans = "4 Digit"
    elif len(snr) == 5 and 10000 <= int(snr) <= 99999:
        ans = "5 Digit"
    elif len(snr) == 6 and 100000 <= int(snr) <= 999999:
        ans = "6 Digit"
    else:
        ans = "Undefined"

    return ans

def get_status_table(snr):
    """Determine which status collection to update based on SNR."""
    if str(snr).startswith('0800'):
        return 'tf_status'
    elif str(snr).startswith('0900'):
        return 'pr_status'
    else:
        return 'snr_status'

def flatten_document(doc):
    """Flatten nested MongoDB document for easier processing."""
    flattened = {}

    # Handle the top-level fields
    for key, value in doc.items():
        if isinstance(value, dict):
            if '$oid' in value:
                flattened[key] = value['$oid']  # Extract ObjectId
            elif '$date' in value:
                flattened[key] = value['$date']  # Extract Date
            else:
                # If it's another nested dict, prefix the keys
                for sub_key, sub_value in value.items():
                    flattened[f"{key}_{sub_key}"] = sub_value
        else:
            flattened[key] = value

    # Handle the nested snr_details separately
    if 'snr_details' in doc:
        for key, value in doc['snr_details'].items():
            if isinstance(value, dict) and '$oid' in value:
                flattened[f"snr_details_{key}"] = value['$oid']  # Extract ObjectId
            else:
                flattened[f"snr_details_{key}"] = value

    return flattened

def convert_object_types(result):
    """Convert ObjectId and datetime types to strings in nested structures."""
    if isinstance(result, dict):
        for key, value in result.items():
            if isinstance(value, ObjectId):
                result[key] = str(value)
            elif isinstance(value, datetime):
                result[key] = value.strftime('%Y-%m-%d %H:%M:%S')
            elif isinstance(value, dict):
                convert_object_types(value)
            elif isinstance(value, list):
                for item in value:
                    convert_object_types(item)
    elif isinstance(result, list):
        for item in result:
            convert_object_types(item)

def convert_object_ids_and_timestamps_to_str(df):
    """Convert ObjectId and datetime objects in DataFrame to strings."""
    def convert_element(x):
        if isinstance(x, (datetime, pd.Timestamp)):
            return x.isoformat()
        if isinstance(x, ObjectId):
            return str(x)
        if isinstance(x, list):
            return [convert_element(e) for e in x]
        if isinstance(x, dict):
            return {k: convert_element(v) for k, v in x.items()}
        return x

    for col in df.columns:
        df[col] = df[col].apply(convert_element)

    return df

def generate_chart_suggestion(df):
    """Generate chart suggestion using GPT-4o-mini."""
    prompt = f"""
    Given the following data columns: {list(df.columns)},
    determine which columns should be used for plotting a meaningful graph.
    Suggest which column to use for x-axis and which to use for y-axis.
    The response must be in JSON format as follows:
    {{
        "x_axis": "<suggested_column_for_x_axis>",
        "y_axis": "<suggested_column_for_y_axis>",
        "chart_type": "<suggested_chart_type>"  # Options: "line", "bar", "scatter", etc.
    }}
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )
        chart_config = json.loads(response.choices[0].message.content.strip())

        # Extract suggested configurations
        x_axis = chart_config.get("x_axis", "snr")  # Default to 'snr'
        y_axis = chart_config.get("y_axis", "fees")  # Default to 'fees'
        chart_type = chart_config.get("chart_type", "line")

        # Generate the chart based on the suggested configuration
        if x_axis in df.columns and y_axis in df.columns:
            if chart_type == "line":
                fig = px.line(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
            elif chart_type == "bar":
                fig = px.bar(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
            elif chart_type == "scatter":
                fig = px.scatter(df, x=x_axis, y=y_axis, title=f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
            else:
                # Fallback to line chart if an unsupported type is suggested
                fig = px.line(df, x=x_axis, y=y_axis, title=f'Line Chart of {y_axis} vs {x_axis}')

            return json.loads(fig.to_json())
        else:
            # If suggested columns don't exist, use first numeric column for y and first string column for x
            numeric_cols = df.select_dtypes(include=['number']).columns
            string_cols = df.select_dtypes(include=['object']).columns

            if len(numeric_cols) > 0 and len(string_cols) > 0:
                fig = px.bar(df, x=string_cols[0], y=numeric_cols[0], title=f'Bar Chart of {numeric_cols[0]} vs {string_cols[0]}')
                return json.loads(fig.to_json())
            else:
                # If no appropriate columns exist, return None
                return None
    except Exception as e:
        print(f"Error with GPT chart suggestion: {e}")
        return None

def generate_multiple_charts(df):
    """Generate multiple chart visualizations for reports."""
    prompt = f"""
    Given the following data columns from a report on telecom number applications: {list(df.columns)}.
    The data includes information about applications for different types of telecom numbers (Shortcodes, Toll-free, Premium-rate).
    Based on these columns, suggest insightful graph visualizations that would be valuable for a business report.
    Your response must be in JSON format as a list of graph configurations.
    """

    try:
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[
                {"role": "system", "content": "You are a helpful assistant. Return your response in JSON format."},
                {"role": "user", "content": prompt}
            ],
            temperature=0,
            response_format={"type": "json_object"}
        )
        chart_configs = json.loads(response.choices[0].message.content.strip())

        visualizations = []  # List to hold all graph JSONs and descriptions

        if isinstance(chart_configs, list):  # Ensure it's a list of configs
            for config in chart_configs:
                x_axis = config.get("x_axis")
                y_axis = config.get("y_axis")
                chart_type = config.get("chart_type", "line")  # Default to line if missing
                graph_title = config.get("graph_title", f'{chart_type.capitalize()} Chart of {y_axis} vs {x_axis}')
                graph_description = config.get("description", "A chart visualization.")

                # Check if the suggested columns exist in the dataframe
                if x_axis in df.columns and y_axis in df.columns:
                    # Generate the chart based on the suggested configuration
                    if chart_type == "line":
                        fig = px.line(df, x=x_axis, y=y_axis, title=graph_title)
                    elif chart_type == "bar":
                        fig = px.bar(df, x=x_axis, y=y_axis, title=graph_title)
                    elif chart_type == "pie":  # Added pie chart option
                        fig = px.pie(df, names=x_axis, values=y_axis, title=graph_title)
                    elif chart_type == "scatter":
                        fig = px.scatter(df, x=x_axis, y=y_axis, title=graph_title)
                    else:
                        fig = px.line(df, x=x_axis, y=y_axis, title=graph_title)

                    graph_json = fig.to_json()
                    visualizations.append({
                        "visualization": json.loads(graph_json),
                        "description": graph_description,
                        "title": graph_title
                    })

        if not visualizations:
            # Fallback to simple visualization if no valid configurations found
            numeric_cols = df.select_dtypes(include=['number']).columns
            string_cols = df.select_dtypes(include=['object']).columns

            if len(numeric_cols) > 0 and len(string_cols) > 0:
                fig = px.bar(df, x=string_cols[0], y=numeric_cols[0], title=f'Bar Chart')
                graph_json = fig.to_json()
                visualizations.append({
                    "visualization": json.loads(graph_json),
                    "description": "Default chart visualization.",
                    "title": f'Bar Chart of {numeric_cols[0]} by {string_cols[0]}'
                })

        return visualizations
    except Exception as e:
        print(f"Error generating multiple charts: {e}")
        return []

def create_table_visualization(df):
    """Create Plotly table visualization from DataFrame."""
    try:
        fig = go.Figure(data=[go.Table(
            header=dict(values=list(df.columns),
                      fill_color='paleturquoise',
                      align='left'),
            cells=dict(values=[df[col] for col in df.columns],
                     fill_color='lavender',
                     align='left'))
        ])
        return fig.to_json()
    except Exception as e:
        print(f"Error creating table visualization: {e}")
        return "{}"

def render_application_details_html(application, snr_details):
    """Render HTML for application details."""
    html = f"""
    <h4>Applicant Information</h4>
    <p><strong>Applicant Name:</strong> {application.get("applicant_name", "")}</p>
    <p><strong>Email:</strong> {application.get("email", "")}</p>
    <p><strong>Phone:</strong> {application.get("phone", "")}</p>
    <p><strong>Company:</strong> {application.get("company", "")}</p>
    <p><strong>Application Date:</strong> {application.get("application_date", "")}</p>

    <h4>Numbers and Purposes</h4>
    """

    # Add SNR details
    for snr in snr_details:
        html += f"""
        <div class="card mt-3">
            <div class="card-body">
                <h5><strong>SNR:</strong> {snr.get("snr", "")}</h5>
                <p><strong>Type:</strong> {snr.get("type", "")}</p>
                <p><strong>Purpose:</strong> {snr.get("purpose", "")}</p>
                <p><strong>Status:</strong> {snr.get("status", "")}</p>
                <p><strong>Certificate ID:</strong> {snr.get("certificate_id", "")}</p>
            </div>
        </div>
        """

    return html

def Check_SNR_EXPIRY(snr_number):
    """Check SNR expiry and certificate history."""
    try:
        # Find the latest application for the SNR
        latest_application = mongo.db.applications.find_one(
            {'snrs.snr': snr_number},
            sort=[('certificate_expiry_date', -1)]  # Sort descending to get latest
        )

        if not latest_application:
            print(f"No previous application found for SNR: {snr_number}. Treating as NEW.")
            return "NEW", "", "", ""  # No prior application, treat as new

        # Extract relevant data from the latest application
        expiry_date_str = latest_application.get('certificate_expiry_date')
        start_date_str = latest_application.get('certificate_start_date')
        applicant_name = latest_application.get('applicant_name')

        if not expiry_date_str or not start_date_str or not applicant_name:
            print(f"Incomplete certificate data found for SNR: {snr_number}. Treating as NEW.")
            return "NEW", "", "", applicant_name if applicant_name else ""

        try:
            expiry_date = datetime.strptime(expiry_date_str, '%Y-%m-%d').date()
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
        except ValueError as e:
            print(f"Error parsing dates for SNR {snr_number}: {e}. Treating as ERROR.")
            return "ERROR", "", "", applicant_name  # Date parsing error

        today = datetime.now().date()

        if today <= expiry_date:
            status = "UNAVAILABLE"  # Current, consider unavailable/reprint
        elif (today - expiry_date).days <= 180:
            status = "RENEWAL"  # Within 6 months expiry
        else:
            status = "RENEWAL"  # Expired > 6 months

        return status, datetime.strftime(start_date, '%B %d, %Y'), datetime.strftime(expiry_date, '%B %d, %Y'), applicant_name

    except Exception as e:
        print(f"Error checking SNR expiry for {snr_number}: {e}. Treating as ERROR.")
        return "ERROR", "", "", ""  # General error case

# Certificate date handling functions
def handle_renewal_dates(**kwargs):
    """Handle renewal dates for certificates."""
    status = kwargs.get('status')
    old_cert_sdate_str = kwargs.get('old_cert_sdate_str')
    old_cert_edate_str = kwargs.get('old_cert_edate_str')

    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)

    if old_cert_sdate_str and old_cert_edate_str:
        old_cert_sdate = datetime.strptime(old_cert_sdate_str, '%B %d, %Y')
        old_cert_edate = datetime.strptime(old_cert_edate_str, '%B %d, %Y')

        if 0 < (datetime.today() - old_cert_edate).days <= 180:
            # Renewal within 6 months
            default_start_date = (old_cert_sdate + timedelta(days=364)).date()
            default_expiry_date = (old_cert_edate + timedelta(days=364)).date()
        elif 0 > (datetime.today() - old_cert_edate).days <= 180:
            # Future/Unexpired - Reprint
            old_cert_date = old_cert_sdate.strftime('%m/%d/%Y')
            future_expiry = (old_cert_edate + timedelta(days=1)).strftime('%m/%d/%Y')
            future_edate = (datetime.today() - old_cert_edate).days
            since_last_print = (datetime.today() - old_cert_sdate).days
            reprint_template = render_template(
                "Reprint.html",
                delta=since_last_print,
                snr=kwargs.get('number'),
                future_edate=future_edate,
                future_expiry=future_expiry,
                old_date=old_cert_date,
                sid=kwargs.get('application').get('application_id')
            )
            return {
                'start_date': datetime.now().date(),
                'expiry_date': default_expiry_date,
                'reprint_template': reprint_template
            }
        else:
            # Expired > 6 months
            default_start_date = datetime.now().date()
            default_expiry_date = (datetime.today() + timedelta(days=365)).date()
    else:
        print("Error: Old certificate dates not found for renewal. Using current dates.")

    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}

def handle_new_certificate_dates(**kwargs):
    """Handle new certificate dates."""
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}

def handle_reprint_dates(**kwargs):
    """Handle reprint dates for certificates."""
    old_cert_sdate_str = kwargs.get('old_cert_sdate_str')
    old_cert_edate_str = kwargs.get('old_cert_edate_str')

    if old_cert_sdate_str and old_cert_edate_str:
        old_cert_sdate = datetime.strptime(old_cert_sdate_str, '%B %d, %Y')
        old_cert_edate = datetime.strptime(old_cert_edate_str, '%B %d, %Y')

        old_cert_date = old_cert_sdate.strftime('%m/%d/%Y')
        future_expiry = (old_cert_edate + timedelta(days=1)).strftime('%m/%d/%Y')
        future_edate = (datetime.today() - old_cert_edate).days
        since_last_print = (datetime.today() - old_cert_sdate).days
        reprint_template = render_template(
            "Reprint.html",
            delta=since_last_print,
            snr=kwargs.get('number'),
            future_edate=future_edate,
            future_expiry=future_expiry,
            old_date=old_cert_date,
            sid=kwargs.get('application').get('application_id')
        )
        return {
            'start_date': datetime.now().date(),
            'expiry_date': datetime.now().date() + timedelta(days=365),
            'reprint_template': reprint_template
        }
    else:
        # Error case if unavailable but no old dates, treat as new
        print("Error: Unavailable SNR status but no old certificate dates found. Treating as new certificate.")
        return handle_new_certificate_dates(**kwargs)

def handle_error_dates(**kwargs):
    """Handle error dates for certificates."""
    print("Error in Check_SNR_EXPIRY. Using default dates.")
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}

def handle_default_dates(**kwargs):
    """Fallback/Default handler for certificate dates."""
    print(f"Using default date handler for status: {kwargs.get('status')}")
    default_start_date = datetime.now().date()
    default_expiry_date = default_start_date + timedelta(days=365)
    return {'start_date': default_start_date, 'expiry_date': default_expiry_date}

def generate_certificate_pdf(filepath, company, email_address, application_snrs, start_date, expiry_date, cert_id):
    """Generate PDF certificate for approved application."""
    # Implementation not shown for brevity
    # This function would use reportlab to generate a PDF certificate
    pass

def generate_qr_code(cert_id):
    """Generate QR code for certificate verification."""
    from flask import url_for

    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=6,
        border=4,
    )
    # Generate verification URL
    verification_url = url_for('verify_certificate', certificate_id=cert_id, _external=True)
    qr.add_data(verification_url)
    qr.make(fit=True)

    img_byte_array = BytesIO()
    img = qr.make_image(fill_color="black", back_color="white")
    img.save(img_byte_array)
    img_byte_array.seek(0)  # Reset to beginning of byte array


    return Image(img_byte_array, width=0.6*inch, height=0.6*inch)

def update_snr_status(certificate_id, new_status, operator_name):
    """Update SNR status with implementation information."""
    # Find the SNR by certificate_id
    snr_record = mongo.db.snrs.find_one(
        {"certificate_id": certificate_id},
        {"_id": 0, "snr": 1}
    )

    if not snr_record:
        return {"success": False, "error": "Certificate ID not found"}

    snr_number = snr_record.get("snr")

    # Update the SNR record
    mongo.db.snrs.update_one(
        {"certificate_id": certificate_id},
        {"$set": {
            "implementing_operator": operator_name,
            "implementation_status": new_status,
            "implementation_date": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }}
    )

    # Log the activity
    log_activity(
        "SNR Implementation Status Update",
        f"Operator {operator_name} updated status of SNR {snr_number} to {new_status}"
    )

    return {
        "success": True,
        "snr": snr_number,
        "operator": operator_name,
        "status": new_status
    }

def update_audit_snr_counts(operator_name=None):
    """Update the SNR counts in audit records based on operational data."""
    current_year = datetime.now().year
    current_quarter = f"Q{(datetime.now().month-1)//3+1}"

    # Build query for audits
    audit_query = {
        "year": current_year,
        "quarter": current_quarter
    }

    if operator_name:
        audit_query["operator"] = operator_name

    # Get all relevant audit records
    audit_records = mongo.db.audits.find(audit_query)

    updates = []
    for audit in audit_records:
        operator = audit.get("operator")

        # Get active SNRs for this operator from operational records
        active_snrs = list(mongo.db.snr_operations.find(
            {
                "assigned_operator": operator,
                "implementation_status": "active"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "certificate_id": 1,
                "application_id": 1,
                "activation_date": 1
            }
        ))

        # Get deactivated SNRs
        deactivated_snrs = list(mongo.db.snr_operations.find(
            {
                "assigned_operator": operator,
                "implementation_status": "deactivated"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "certificate_id": 1,
                "application_id": 1,
                "deactivation_date": 1,
                "deactivation_reason": 1
            }
        ))

        # Prepare SNR records for audit
        active_snr_records = []
        for snr in active_snrs:
            # Get SNR details
            snr_detail = mongo.db.snrs.find_one(
                {"snr": snr["snr"]},
                {"_id": 0, "purpose": 1}
            )

            active_snr_records.append({
                "snr": snr["snr"],
                "status": "active",
                "activated_date": snr.get("activation_date", datetime.now(timezone.utc)),
"purpose": snr_detail.get("purpose", "") if snr_detail else "",
                "certificate_id": snr.get("certificate_id", ""),
                "type": snr.get("type", "")
            })

        deactivated_snr_records = []
        for snr in deactivated_snrs:
            # Get SNR details
            snr_detail = mongo.db.snrs.find_one(
                {"snr": snr["snr"]},
                {"_id": 0, "purpose": 1}
            )

            deactivated_snr_records.append({
                "snr": snr["snr"],
                "status": "deactivated",
                "deactivation_date": snr.get("deactivation_date", datetime.now(timezone.utc)),
                "reason": snr.get("deactivation_reason", "Not specified"),
                "purpose": snr_detail.get("purpose", "") if snr_detail else "",
                "certificate_id": snr.get("certificate_id", ""),
                "type": snr.get("type", "")
            })

        # Update the audit record
        mongo.db.audits.update_one(
            {"_id": audit["_id"]},
            {"$set": {
                "active_snrs": active_snr_records,
                "deactivated_snrs": deactivated_snr_records,
                "active_snrs_count": len(active_snr_records),
                "deactivated_snrs_count": len(deactivated_snr_records),
                "last_synchronized": datetime.now(timezone.utc)
            }}
        )

        updates.append({
            "operator": operator,
            "active_count": len(active_snr_records),
            "deactivated_count": len(deactivated_snr_records)
        })

    return updates

def notify_operators(event_data):
    """Send notifications to registered webhooks based on event type."""
    event_type = event_data.get('event')

    # Get webhooks subscribed to this event
    webhooks = mongo.db.webhooks.find({
        "active": True,
        "events": event_type
    })

    # Track successful and failed notifications
    results = {
        "successful": [],
        "failed": []
    }

    for webhook in webhooks:
        operator = webhook.get('operator')
        url = webhook.get('webhook_url')

        try:
            # Add timestamp and webhook ID to the payload
            notification_data = {
                **event_data,
                "notification_id": str(ObjectId()),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

            # Send the notification with timeout
            response = requests.post(
                url,
                json=notification_data,
                timeout=5,
                headers={"Content-Type": "application/json"}
            )

            # Log notification attempt
            log_entry = {
                "webhook_id": str(webhook.get('_id')),
                "operator": operator,
                "event": event_type,
                "notification_data": notification_data,
                "status_code": response.status_code,
                "timestamp": datetime.now(timezone.utc),
                "successful": 200 <= response.status_code < 300
            }

            mongo.db.webhook_logs.insert_one(log_entry)

            # Track result
            if 200 <= response.status_code < 300:
                results["successful"].append(operator)
            else:
                results["failed"].append({
                    "operator": operator,
                    "status_code": response.status_code,
                    "reason": response.text[:100]  # Truncate long error messages
                })

        except requests.RequestException as e:
            # Log the failure
            log_entry = {
                "webhook_id": str(webhook.get('_id')),
                "operator": operator,
                "event": event_type,
                "notification_data": event_data,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc),
                "successful": False
            }

            mongo.db.webhook_logs.insert_one(log_entry)

            # Track failure
            results["failed"].append({
                "operator": operator,
                "error": str(e)
            })

    return results


class MongoJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        if isinstance(obj, datetime):
            return obj.isoformat()
        return json.JSONEncoder.default(self, obj)

def setup_json_encoder(app):
    app.json_encoder = MongoJSONEncoder

@functools.lru_cache(maxsize=128)
def tojson_mongo_filter(value):
    """Convert value to proper JSON with MongoDB handling."""
    def convert(obj):
        if isinstance(obj, ObjectId):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, list):
            return [convert(item) for item in obj]
        elif isinstance(obj, dict):
            return {key: convert(value) for key, value in obj.items()}
        return obj

    processed_value = convert(value)
    return json.dumps(processed_value)

def register_jinja_filters(app):
    app.jinja_env.filters['tojson_mongo'] = tojson_mongo_filter

    # Register the format_datetime filter
    @app.template_filter('format_datetime')
    def format_datetime(value, format='%Y-%m-%d %H:%M:%S'):
        """Format a datetime object to a string."""
        if value is None:
            return ""
        if isinstance(value, str):
            try:
                value = datetime.fromisoformat(value.replace('Z', '+00:00'))
            except:
                pass
        if isinstance(value, datetime):
            return value.strftime(format)
        return value

    # Register the status_badge_class filter

    @app.template_filter('get_status_badge_class')
    def get_status_badge_class(status):
        """Maps a submission status string to a Bootstrap background color class."""
        status_lower = status.lower() if status else ''
        if status_lower == 'approved':
            return 'success'
        elif status_lower == 'pending':
            return 'warning'
        elif status_lower == 'rejected':
            return 'danger'
        elif status_lower == 'draft':
            return 'secondary'
        else:
            return 'light' # Default or unknown status





# Create the Flask application instance
app = create_app()

setup_json_encoder(app)
register_jinja_filters(app)



# Run the application when script is executed directly
if __name__ == '__main__':
    app.run(debug=True)
