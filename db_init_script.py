# MongoDB Initialization Script for E.164 Number Range Audit System
# ---------------------------------------------------------------
# This script initializes the MongoDB collections and indexes required for the E.164 audit system.
# It also populates the number_allocations collection with sample data for testing.

from pymongo import MongoClient, ASCENDING, DESCENDING, TEXT
from datetime import datetime, timezone
import json

# MongoDB connection string - replace with your actual connection string
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"

# Connect to MongoDB
client = MongoClient(MONGO_URI)
db = client.get_database()

# Create collections if they don't exist
if "e164_submissions" not in db.list_collection_names():
    db.create_collection("e164_submissions")
    print("Created e164_submissions collection")

if "e164_ranges" not in db.list_collection_names():
    db.create_collection("e164_ranges")
    print("Created e164_ranges collection")

if "number_allocations" not in db.list_collection_names():
    db.create_collection("number_allocations")
    print("Created number_allocations collection")

if "e164_analytics" not in db.list_collection_names():
    db.create_collection("e164_analytics")
    print("Created e164_analytics collection")

# Create indexes for e164_submissions collection
submissions_collection = db.e164_submissions
submissions_collection.create_index([("operator_id", ASCENDING)])
submissions_collection.create_index([("reporting_period", DESCENDING)])
submissions_collection.create_index([("status", ASCENDING)])
submissions_collection.create_index([("created_at", DESCENDING)])
submissions_collection.create_index([("operator", TEXT)], default_language="english")
print("Created indexes for e164_submissions collection")

# Create indexes for e164_ranges collection
ranges_collection = db.e164_ranges
ranges_collection.create_index([("submission_id", ASCENDING)])
ranges_collection.create_index([("ndc", ASCENDING)])
ranges_collection.create_index([("created_at", DESCENDING)])
print("Created indexes for e164_ranges collection")

# Create indexes for number_allocations collection
allocations_collection = db.number_allocations
allocations_collection.create_index([("operator_id", ASCENDING)])
allocations_collection.create_index([("ndc", ASCENDING)], unique=True)
allocations_collection.create_index([("status", ASCENDING)])
print("Created indexes for number_allocations collection")

# Create indexes for e164_analytics collection
analytics_collection = db.e164_analytics
analytics_collection.create_index([("operator_id", ASCENDING)])
analytics_collection.create_index([("reporting_period", DESCENDING)])
analytics_collection.create_index([("submission_id", ASCENDING)])
print("Created indexes for e164_analytics collection")

# Sample Operator Data
operators = [
    {
        "name": "MTN",
        "full_name": "MTN Ghana Limited",
        "license_number": "NCAT-001",
        "contact_person": "Angela Ampofo",
        "contact_email": "<EMAIL>",
        "contact_phone": "+233 24 0000000",
        "address": "MTN House, Independence Avenue, Accra",
        "active": True,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "name": "TELECEL",
        "full_name": "TELECEL Ghana Limited",
        "license_number": "NCAT-002",
        "contact_person": "Benjamin Fio",
        "contact_email": "<EMAIL>",
        "contact_phone": "+233 20 1000000",
        "address": "TELECEL Tower, Ring Road, Accra",
        "active": True,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "name": "AT",
        "full_name": "AirtelTigo Ghana Limited",
        "license_number": "NCAT-003",
        "contact_person": "Kwabena Anaafi",
        "contact_email": "<EMAIL>",
        "contact_phone": "+233 27 1000000",
        "address": "AT Plaza, Spintex Road, Accra",
        "active": True,
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }
]

# Check if operators collection exists and create if not
if "operators" not in db.list_collection_names():
    db.create_collection("operators")
    print("Created operators collection")
    
    # Add sample operators
    operators_collection = db.operators
    for operator in operators:
        # Check if operator already exists
        existing = operators_collection.find_one({"name": operator["name"]})
        if not existing:
            operators_collection.insert_one(operator)
            print(f"Added operator: {operator['name']}")
        else:
            print(f"Operator {operator['name']} already exists")

# Sample Number Allocations
mtn_id = str(db.operators.find_one({"name": "MTN"})["_id"])
telecel_id = str(db.operators.find_one({"name": "TELECEL"})["_id"])
at_id = str(db.operators.find_one({"name": "AT"})["_id"])

number_allocations = [
    # MTN Allocations
    {
        "operator_id": mtn_id,
        "operator": "MTN",
        "ndc": "024",
        "type": "WIRELESS DEDICATED",
        "start_block": "0240000000",
        "end_block": "0249999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2000, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "operator_id": mtn_id,
        "operator": "MTN",
        "ndc": "054",
        "type": "WIRELESS DEDICATED",
        "start_block": "0540000000",
        "end_block": "0549999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2008, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "operator_id": mtn_id,
        "operator": "MTN",
        "ndc": "055",
        "type": "WIRELESS DEDICATED",
        "start_block": "0550000000",
        "end_block": "0559999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2010, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "operator_id": mtn_id,
        "operator": "MTN",
        "ndc": "0591-0599",
        "type": "WIRELESS SHARED",
        "start_block": "0591000000",
        "end_block": "0599999999",
        "total_allocated": 9000000,
        "allocation_date": datetime(2015, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    # TELECEL Allocations
    {
        "operator_id": telecel_id,
        "operator": "TELECEL",
        "ndc": "020",
        "type": "WIRELESS DEDICATED",
        "start_block": "0200000000",
        "end_block": "0209999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2002, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "operator_id": telecel_id,
        "operator": "TELECEL",
        "ndc": "050",
        "type": "WIRELESS DEDICATED",
        "start_block": "0500000000",
        "end_block": "0509999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2006, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    # AT Allocations
    {
        "operator_id": at_id,
        "operator": "AT",
        "ndc": "026",
        "type": "WIRELESS DEDICATED",
        "start_block": "0260000000",
        "end_block": "0269999999",
        "total_allocated": 10000000,
        "allocation_date": datetime(2003, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    },
    {
        "operator_id": at_id,
        "operator": "AT",
        "ndc": "056",
        "type": "WIRELESS SHARED",
        "start_block": "0560000000",
        "end_block": "0561999999",
        "total_allocated": 2000000,
        "allocation_date": datetime(2011, 1, 1),
        "status": "active",
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc)
    }
]

# Add sample number allocations
for allocation in number_allocations:
    # Check if allocation already exists
    existing = allocations_collection.find_one({"ndc": allocation["ndc"]})
    if not existing:
        allocations_collection.insert_one(allocation)
        print(f"Added number allocation: {allocation['ndc']} to {allocation['operator']}")
    else:
        print(f"Allocation for NDC {allocation['ndc']} already exists")

print("\nDatabase initialization complete!")
