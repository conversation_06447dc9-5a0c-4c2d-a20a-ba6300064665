# forms.py
from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, StringField, PasswordField, SubmitField, FileField, <PERSON>oleanField
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length

class RegistrationForm(FlaskForm):
    vas_id = String<PERSON>ield('VAS ID', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    confirm_password = Password<PERSON>ield('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    contact = StringField('Contact Name', validators=[DataRequired()])
    phone = StringField('Phone Number', validators=[DataRequired()])
    national_id = FileField('National ID', validators=[DataRequired()])
    photo = FileField('Photo', validators=[DataRequired()])
    submit = SubmitField('Register')

class LoginForm(FlaskForm):
    username = String<PERSON>ield('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')
    remember = BooleanField('Remember Me')

# forms.py (add to your existing file)
class UserCreationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired()])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirm Password', 
                                    validators=[DataRequired(), EqualTo('password')])
    user_type = SelectField('User Type', choices=[
        ('regular', 'Regular User'),
        ('vas', 'VAS Provider'),
        ('operator', 'Network Operator'),
        ('admin', 'Administrator')
    ])
    vas_id = StringField('VAS Provider ID')
    operator_id = SelectField('Network Operator', choices=[])
    submit = SubmitField('Create User')