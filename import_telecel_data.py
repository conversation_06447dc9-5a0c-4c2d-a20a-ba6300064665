#!/usr/bin/env python
"""
Import script for TELECEL's E.164 data for 2024-H1 to MongoDB.
Usage: python import_telecel_data.py
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime, timezone
import json
from bson import ObjectId

# MongoDB connection details - update as needed
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

# The JSON data for E.164 submissions
e164_submission = {
    "operator_id": "TELECEL_OP_ID",  # Will be updated if we find the real operator ID
    "operator": "TELECEL",
    "reporting_period": "2024-h1",
    "contact_person": "<PERSON> Fio",
    "contact_email": "<EMAIL>",
    "notes": "First half 2024 E.164 number audit",
    "status": "approved"
}

# The JSON data for E.164 ranges - derived from the provided table
e164_ranges = [
    {
        "ndc": "020",
        "type": "WIRELESS DEDICATED",
        "start_block": "0200000000",
        "end_block": "0209999999",
        "total_allocated": 10000000,
        "active_subscriber": 2687712,
        "active_non_subscriber": 0,
        "inactive": 6855598,
        "reserved": 40587,
        "gross_addition": 423538,
        "net_addition": -7435,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 26.88,  # (active_subscriber + active_non_subscriber) / total_allocated * 100
        "dormancy_rate": 68.56,     # inactive / total_allocated * 100
        "reservation_rate": 0.41     # reserved / total_allocated * 100
    },
    {
        "ndc": "050",
        "type": "WIRELESS DEDICATED",
        "start_block": "0500000000",
        "end_block": "0509999999",
        "total_allocated": 10000000,
        "active_subscriber": 2168492,
        "active_non_subscriber": 0,
        "inactive": 6919184,
        "reserved": 470324,
        "gross_addition": 439143,
        "net_addition": 2857,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 21.68,
        "dormancy_rate": 69.19,
        "reservation_rate": 4.70
    },
    {
        "ndc": "0322",
        "type": "FIXED SHARED",
        "start_block": "0322000000",
        "end_block": "0322599999",
        "total_allocated": 600000,
        "active_subscriber": 1310,
        "active_non_subscriber": 0,
        "inactive": 598690,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.22,
        "dormancy_rate": 99.78,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0352",
        "type": "FIXED SHARED",
        "start_block": "0352000000",
        "end_block": "0352799999",
        "total_allocated": 800000,
        "active_subscriber": 301,
        "active_non_subscriber": 0,
        "inactive": 799699,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.04,
        "dormancy_rate": 99.96,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0332",
        "type": "FIXED SHARED",
        "start_block": "0332000000",
        "end_block": "0332399999",
        "total_allocated": 400000,
        "active_subscriber": 138,
        "active_non_subscriber": 0,
        "inactive": 399862,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0.00
    },
    {
        "ndc": "034",
        "type": "FIXED SHARED",
        "start_block": "0342000000",
        "end_block": "0343099999",
        "total_allocated": 1100000,
        "active_subscriber": 315,
        "active_non_subscriber": 0,
        "inactive": 1099685,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0.00
    },
    {
        "ndc": "030",
        "type": "FIXED SHARED",
        "start_block": "0302000000",
        "end_block": "0303599999",
        "total_allocated": 1600000,
        "active_subscriber": 24819,
        "active_non_subscriber": 0,
        "inactive": 1575181,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 1.55,
        "dormancy_rate": 98.45,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0372",
        "type": "FIXED SHARED",
        "start_block": "0372000000",
        "end_block": "0372699999",
        "total_allocated": 700000,
        "active_subscriber": 141,
        "active_non_subscriber": 0,
        "inactive": 699859,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0382",
        "type": "FIXED SHARED",
        "start_block": "0382000000",
        "end_block": "0382299999",
        "total_allocated": 300000,
        "active_subscriber": 28,
        "active_non_subscriber": 0,
        "inactive": 299972,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.01,
        "dormancy_rate": 99.99,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0392",
        "type": "FIXED SHARED",
        "start_block": "0392000000",
        "end_block": "0392099999",
        "total_allocated": 100000,
        "active_subscriber": 76,
        "active_non_subscriber": 0,
        "inactive": 99924,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.08,
        "dormancy_rate": 99.92,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0362",
        "type": "FIXED SHARED",
        "start_block": "0362000000",
        "end_block": "0362699999",
        "total_allocated": 700000,
        "active_subscriber": 339,
        "active_non_subscriber": 0,
        "inactive": 699661,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.05,
        "dormancy_rate": 99.95,
        "reservation_rate": 0.00
    },
    {
        "ndc": "0312",
        "type": "FIXED SHARED",
        "start_block": "0312000000",
        "end_block": "0312699999",
        "total_allocated": 700000,
        "active_subscriber": 537,
        "active_non_subscriber": 0,
        "inactive": 699463,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.08,
        "dormancy_rate": 99.92,
        "reservation_rate": 0.00
    }
]

# The JSON data for porting details - derived from the provided porting table
e164_porting_details = [
    {
        "ndc": "023",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "TELECEL",
        "count": 1,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "024",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 23,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "026",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "TELECEL",
        "count": 26,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "026",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 40,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "027",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "TELECEL",
        "count": 15,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "027",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 13,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "054",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 3,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "056",
        "range_type": "WIRELESS SHARED",
        "source_operator": "OTHER",
        "target_operator": "TELECEL",
        "count": 1,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "056",
        "range_type": "WIRELESS SHARED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 3,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "057",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "OTHER",
        "target_operator": "TELECEL",
        "count": 15,
        "reporting_period": "2024-h1"
    },
    {
        "ndc": "057",
        "range_type": "WIRELESS DEDICATED",
        "source_operator": "TELECEL",
        "target_operator": "OTHER",
        "count": 2,
        "reporting_period": "2024-h1"
    }
]

def import_data():
    """Import the data into MongoDB"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Add timestamps and generate unique ID
    submission_id = str(ObjectId())
    now = datetime.now(timezone.utc)
    
    # Create the submission
    e164_submission.update({
        "_id": ObjectId(submission_id),
        "created_at": now,
        "updated_at": now,
        "created_by": "import_script",
        "updated_by": "import_script"
    })
    
    try:
        # Get the TELECEL operator ID from the database if possible
        telecel_operator = db.operators.find_one({"name": "TELECEL"})
        if telecel_operator:
            e164_submission["operator_id"] = str(telecel_operator["_id"])
            print(f"Found TELECEL operator ID: {e164_submission['operator_id']}")
    except Exception as e:
        print(f"Warning: Could not find TELECEL operator in database: {e}")
        print("Using placeholder operator_id")
    
    # Insert submission
    print("Inserting submission...")
    db.e164_submissions.insert_one(e164_submission)
    
    # Update ranges with submission_id and timestamps
    print(f"Processing {len(e164_ranges)} ranges...")
    for r in e164_ranges:
        r.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert ranges
    if e164_ranges:
        db.e164_ranges.insert_many(e164_ranges)
        print(f"Inserted {len(e164_ranges)} ranges")
    
    # Update porting data with submission_id and timestamps
    print(f"Processing {len(e164_porting_details)} porting records...")
    for p in e164_porting_details:
        p.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert porting data
    if e164_porting_details:
        db.e164_porting_details.insert_many(e164_porting_details)
        print(f"Inserted {len(e164_porting_details)} porting records")
    
    print(f"Import complete. Submission ID: {submission_id}")
    return submission_id

if __name__ == "__main__":
    submission_id = import_data()
    print("Data successfully imported!")
    print(f"Submission ID: {submission_id}")
