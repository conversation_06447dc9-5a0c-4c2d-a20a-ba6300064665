{% extends "base.html" %}

{% block title %}Operator Performance Comparison{% endblock %}

{% block styles %}
<style>
    /* Glassmorphic style for the comparison interface */
    .glass-card {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .section-title {
        position: relative;
        font-size: 1.5rem;
        color: #333;
        margin-bottom: 20px;
        border-bottom: 2px solid rgba(31, 38, 135, 0.2);
        padding-bottom: 10px;
    }
    
    .header-underline {
        height: 3px;
        width: 50px;
        background: linear-gradient(90deg, rgba(72, 149, 239, 0.8), rgba(72, 149, 239, 0.2));
        margin-bottom: 20px;
    }
    
    .btn-glass {
        background: rgba(72, 149, 239, 0.6);
        color: white;
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        transition: all 0.3s ease;
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
    }
    
    .btn-glass:hover {
        background: rgba(72, 149, 239, 0.8);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(31, 38, 135, 0.2);
    }
    
    .filter-label {
        font-weight: 500;
        margin-right: 10px;
    }
    
    .chart-container {
        height: 400px;
        position: relative;
    }
    
    .score-card {
        padding: 15px;
        border-radius: 10px;
        text-align: center;
        height: 100%;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(5px);
        -webkit-backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .score-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(31, 38, 135, 0.1);
    }
    
    .score-value {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 10px 0;
    }
    
    .score-label {
        font-size: 1rem;
        color: #666;
    }
    
    .score-card.top {
        background: linear-gradient(135deg, rgba(75, 192, 192, 0.1), rgba(75, 192, 192, 0.3));
        border: 1px solid rgba(75, 192, 192, 0.4);
    }
    
    .score-card.average {
        background: linear-gradient(135deg, rgba(255, 205, 86, 0.1), rgba(255, 205, 86, 0.3));
        border: 1px solid rgba(255, 205, 86, 0.4);
    }
    
    .score-card.low {
        background: linear-gradient(135deg, rgba(255, 99, 132, 0.1), rgba(255, 99, 132, 0.3));
        border: 1px solid rgba(255, 99, 132, 0.4);
    }
    
    .table-glass {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 10px;
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .table-glass thead {
        background: rgba(72, 149, 239, 0.1);
    }
    
    .table-glass thead th {
        font-weight: 600;
        color: #333;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .table-glass tbody tr:hover {
        background: rgba(255, 255, 255, 0.15);
    }
    
    .progress-bar-container {
        height: 10px;
        background-color: rgba(0, 0, 0, 0.05);
        border-radius: 5px;
        overflow: hidden;
        margin-top: 5px;
    }
    
    .progress-bar {
        height: 100%;
        border-radius: 5px;
        transition: width 0.5s ease;
    }
    
    .progress-good {
        background: linear-gradient(90deg, rgba(75, 192, 192, 0.5), rgba(75, 192, 192, 0.8));
    }
    
    .progress-average {
        background: linear-gradient(90deg, rgba(255, 205, 86, 0.5), rgba(255, 205, 86, 0.8));
    }
    
    .progress-poor {
        background: linear-gradient(90deg, rgba(255, 99, 132, 0.5), rgba(255, 99, 132, 0.8));
    }
    
    .metric-label {
        font-weight: 500;
        margin-bottom: 5px;
    }
    
    .badge-rank {
        border-radius: 50%;
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 14px;
    }
    
    .badge-rank-1 {
        background-color: rgba(255, 215, 0, 0.2);
        color: #FFD700;
        border: 1px solid rgba(255, 215, 0, 0.5);
    }
    
    .badge-rank-2 {
        background-color: rgba(192, 192, 192, 0.2);
        color: #C0C0C0;
        border: 1px solid rgba(192, 192, 192, 0.5);
    }
    
    .badge-rank-3 {
        background-color: rgba(205, 127, 50, 0.2);
        color: #CD7F32;
        border: 1px solid rgba(205, 127, 50, 0.5);
    }
    
    body {
        background-color: #f8f9fc;
        background-image: 
            radial-gradient(at 47% 33%, rgba(72, 149, 239, 0.1) 0, transparent 59%), 
            radial-gradient(at 82% 65%, rgba(72, 149, 239, 0.15) 0, transparent 55%);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="glass-card">
                <h2 class="text-center">Operator Performance Comparison</h2>
                <div class="header-underline mx-auto"></div>
                <p class="text-center text-muted">Compare telecom operators based on numbering resource utilization and performance metrics</p>
            </div>
        </div>
    </div>
    
    <!-- Filters Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="glass-card">
                <form method="GET" action="{{ url_for('audit.operator_comparison') }}" class="row align-items-center">
                    <div class="col-md-4 mb-2">
                        <label for="year" class="filter-label">Year</label>
                        <select class="form-select" id="year" name="year">
                            {% for yr in years %}
                            <option value="{{ yr }}" {% if selected_year == yr|string %}selected{% endif %}>{{ yr }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 mb-2">
                        <label for="quarter" class="filter-label">Quarter</label>
                        <select class="form-select" id="quarter" name="quarter">
                            {% for q in quarters %}
                            <option value="{{ q }}" {% if selected_quarter == q %}selected{% endif %}>{{ q }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-4 mb-2">
                        <button type="submit" class="btn btn-glass w-100">
                            <i class="fas fa-filter me-2"></i> Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Top Performers Section -->
    {% if comparison_data %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="glass-card">
                <h4 class="section-title">Top Performers</h4>
                <div class="row">
                    {% set top_operators = comparison_data|sort(attribute='overall_score', reverse=true)|slice(0, 3) %}
                    
                    {% for operator in top_operators %}
                    <div class="col-md-4 mb-4">
                        <div class="score-card {% if loop.index == 1 %}top{% elif loop.index == 2 %}average{% else %}low{% endif %}">
                            <div class="badge-rank badge-rank-{{ loop.index }}">{{ loop.index }}</div>
                            <h3>{{ operator._id }}</h3>
                            <div class="score-value">{{ operator.overall_score|round(2) }}</div>
                            <div class="score-label">Overall Score</div>
                            
                            <div class="mt-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <span>Utilization</span>
                                    <span>{{ (operator.avg_utilization * 100)|round(1) }}%</span>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar {% if operator.avg_utilization > 0.8 %}progress-good{% elif operator.avg_utilization > 0.5 %}progress-average{% else %}progress-poor{% endif %}" style="width: {{ (operator.avg_utilization * 100)|round }}%;"></div>
                                </div>
                                
                                <div class="d-flex justify-content-between align-items-center mb-2 mt-3">
                                    <span>Inactive Rate</span>
                                    <span>{{ operator.avg_inactive|round(1) }}%</span>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar {% if operator.avg_inactive < 5 %}progress-good{% elif operator.avg_inactive < 15 %}progress-average{% else %}progress-poor{% endif %}" style="width: {{ min(100, operator.avg_inactive * 3)|round }}%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Performance Visualization -->
    <div class="row mb-4">
        <div class="col-md-8 mb-4">
            <div class="glass-card chart-container">
                <h4 class="section-title">Operator Performance Comparison</h4>
                <canvas id="operatorComparisonChart"></canvas>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="glass-card chart-container">
                <h4 class="section-title">Utilization by Operator</h4>
                <canvas id="utilizationDonutChart"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Detailed Metrics Table -->
    <div class="row">
        <div class="col-12">
            <div class="glass-card">
                <h4 class="section-title">Detailed Performance Metrics</h4>
                <div class="table-responsive">
                    <table class="table table-glass">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Operator</th>
                                <th>Utilization Rate</th>
                                <th>Inactive Rate</th>
                                <th>Porting Balance</th>
                                <th>Total Numbers</th>
                                <th>Overall Score</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for operator in comparison_data|sort(attribute='overall_score', reverse=true) %}
                            <tr>
                                <td>
                                    <span class="badge-rank {% if loop.index <= 3 %}badge-rank-{{ loop.index }}{% endif %}">{{ loop.index }}</span>
                                </td>
                                <td>{{ operator._id }}</td>
                                <td>
                                    <div class="metric-label">{{ (operator.avg_utilization * 100)|round(1) }}%</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar {% if operator.avg_utilization > 0.8 %}progress-good{% elif operator.avg_utilization > 0.5 %}progress-average{% else %}progress-poor{% endif %}" style="width: {{ (operator.avg_utilization * 100)|round }}%;"></div>
                                    </div>
                                </td>
                                <td>
                                    <div class="metric-label">{{ operator.avg_inactive|round(1) }}%</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar {% if operator.avg_inactive < 5 %}progress-good{% elif operator.avg_inactive < 15 %}progress-average{% else %}progress-poor{% endif %}" style="width: {{ min(100, operator.avg_inactive * 3)|round }}%;"></div>
                                    </div>
                                </td>
                                <td>{{ operator.porting_balance|int }}</td>
                                <td>{{ operator.total_numbers|format_number }}</td>
                                <td>
                                    <div class="metric-label">{{ operator.overall_score|round(2) }}/10</div>
                                    <div class="progress-bar-container">
                                        <div class="progress-bar {% if operator.overall_score > 7 %}progress-good{% elif operator.overall_score > 5 %}progress-average{% else %}progress-poor{% endif %}" style="width: {{ (operator.overall_score * 10)|round }}%;"></div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    {% else %}
    <div class="row">
        <div class="col-12">
            <div class="glass-card">
                <div class="alert alert-info text-center">
                    <i class="fas fa-info-circle me-2"></i> No comparison data available for the selected period.
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        initializeCharts();
    });
    
    function initializeCharts() {
        {% if comparison_data %}
        const comparisonData = {{ comparison_data|tojson|safe }};
        
        // Sort by overall score (descending)
        comparisonData.sort((a, b) => b.overall_score - a.overall_score);
        
        // Extract data for charts
        const operators = comparisonData.map(d => d._id);
        const utilizationRates = comparisonData.map(d => (d.avg_utilization * 100).toFixed(1));
        const inactiveRates = comparisonData.map(d => d.avg_inactive.toFixed(1));
        const overallScores = comparisonData.map(d => d.overall_score.toFixed(2));
        
        // Chart 1: Operator Comparison (Radar Chart)
        new Chart(document.getElementById('operatorComparisonChart'), {
            type: 'radar',
            data: {
                labels: ['Utilization Rate', 'Numbers Management', 'Inactive Rate', 'Porting Balance', 'Overall Efficiency'],
                datasets: comparisonData.slice(0, 5).map((operator, index) => {
                    const color = getColorFromIndex(index);
                    
                    // Normalize values between 0-100
                    const utilizationScore = operator.avg_utilization * 100; // already 0-100
                    const numbersScore = Math.min(100, operator.total_numbers / 10000); // arbitrary scaling
                    const inactiveScore = 100 - Math.min(100, operator.avg_inactive * 2); // reverse scale and normalize
                    const portingScore = 50 + Math.min(50, Math.max(-50, operator.porting_balance / 1000)); // normalize around 50
                    const efficiencyScore = operator.overall_score * 10; // scale to 0-100
                    
                    return {
                        label: operator._id,
                        data: [utilizationScore, numbersScore, inactiveScore, portingScore, efficiencyScore],
                        backgroundColor: `${color}33`,
                        borderColor: color,
                        borderWidth: 2,
                        pointBackgroundColor: color,
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: color
                    };
                })
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                elements: {
                    line: {
                        tension: 0.1
                    }
                },
                scales: {
                    r: {
                        angleLines: {
                            display: true,
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        suggestedMin: 0,
                        suggestedMax: 100,
                        ticks: {
                            display: false
                        }
                    }
                }
            }
        });
        
        // Chart 2: Utilization Donut Chart
        new Chart(document.getElementById('utilizationDonutChart'), {
            type: 'doughnut',
            data: {
                labels: operators,
                datasets: [{
                    data: utilizationRates,
                    backgroundColor: operators.map((_, i) => getColorFromIndex(i)),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                        labels: {
                            boxWidth: 12,
                            font: {
                                size: 10
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `${context.label}: ${context.raw}%`;
                            }
                        }
                    }
                },
                cutout: '60%'
            }
        });
        {% endif %}
    }
    
    // Helper function to get a color based on index
    function getColorFromIndex(index) {
        const colors = [
            'rgba(72, 149, 239, 1)',
            'rgba(255, 99, 132, 1)',
            'rgba(255, 205, 86, 1)',
            'rgba(75, 192, 192, 1)',
            'rgba(153, 102, 255, 1)',
            'rgba(255, 159, 64, 1)',
            'rgba(201, 203, 207, 1)'
        ];
        return colors[index % colors.length];
    }
</script>
{% endblock %}
