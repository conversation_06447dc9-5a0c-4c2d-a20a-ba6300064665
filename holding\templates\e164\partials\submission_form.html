<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>E.164 Number Range Audit Submission</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/css/bootstrap.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #1abc9c;
            --background-color: #ecf0f1;
            --glass-bg: rgba(255, 255, 255, 0.25);
            --glass-border: 1px solid rgba(255, 255, 255, 0.18);
            --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }

        body {
            font-family: '<PERSON><PERSON>e <PERSON>', Tahoma, Geneva, Verdana, sans-serif;
            background-color: var(--background-color);
            background-image:
                radial-gradient(at 47% 33%, rgba(52, 152, 219, 0.25) 0, transparent 59%),
                radial-gradient(at 82% 65%, rgba(26, 188, 156, 0.15) 0, transparent 55%);
            min-height: 100vh;
        }

        .glass-card {
            background: var(--glass-bg);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            border: var(--glass-border);
            box-shadow: var(--glass-shadow);
            padding: 25px;
            margin-bottom: 25px;
        }

        .header {
            padding: 20px 0;
            color: var(--primary-color);
        }

        .tab-content {
            padding: 20px 0;
        }

        .nav-tabs {
            border-bottom: none;
        }

        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px;
            margin-right: 5px;
            color: var(--primary-color);
            font-weight: 500;
            padding: 12px 20px;
            transition: all 0.3s ease;
        }

        .nav-tabs .nav-link:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .nav-tabs .nav-link.active {
            background: var(--glass-bg);
            border: var(--glass-border);
            color: var(--secondary-color);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .form-control, .form-select {
            background: rgba(255, 255, 255, 0.5);
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 8px;
            padding: 12px 15px;
            backdrop-filter: blur(5px);
            transition: all 0.3s;
        }

        .form-control:focus, .form-select:focus {
            background: rgba(255, 255, 255, 0.7);
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
            border-color: var(--secondary-color);
        }

        .table {
            background: rgba(255, 255, 255, 0.7);
            border-radius: 10px;
            overflow: hidden;
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
            transition: all 0.3s;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .btn-outline-secondary {
            border-color: var(--secondary-color);
            color: var(--secondary-color);
            border-radius: 8px;
            padding: 10px 20px;
            font-weight: 600;
        }

        .btn-outline-secondary:hover {
            background-color: var(--secondary-color);
            color: white;
        }

        .metric-card {
            padding: 20px;
            text-align: center;
            height: 100%;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: bold;
            color: var(--secondary-color);
        }

        .chart-container {
            height: 300px;
            margin-bottom: 20px;
        }

        .help-text {
            color: #7f8c8d;
            font-size: 0.85rem;
        }

        .locked-field {
            background-color: rgba(236, 240, 241, 0.7) !important;
            cursor: not-allowed;
        }

        .table th {
            font-weight: 600;
            color: var(--primary-color);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(44, 62, 80, 0.2);
            border-radius: 10px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(44, 62, 80, 0.4);
        }

        /* Animation */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .animated {
            animation: fadeIn 0.5s ease forwards;
        }

        .form-section {
            opacity: 0;
            animation: fadeIn 0.5s ease forwards;
            animation-delay: 0.2s;
        }
    </style>

    <script>
// Create a global namespace for our data
window.E164Data = {
    latestData: {% if latest_data %}{{ latest_data|tojson|safe }}{% else %}null{% endif %},
    historicalData: {% if historical_data %}{{ historical_data|tojson|safe }}{% else %}[]{% endif %},
    chartData: {% if chart_data %}{{ chart_data|tojson|safe }}{% else %}{"labels":[],"utilization":[],"dormancy":[],"reservation":[]}{% endif %},
    isAdmin: {% if is_admin is defined %}{{ is_admin|tojson|safe }}{% else %}false{% endif %},
    activeTab: "{{ active_tab }}"
};

// Debug log
console.log("E164 Data initialized:", window.E164Data);
</script>
</head>
<body data-active-tab="{{ active_tab }}">
    <div class="container py-5">
        <div class="header text-center mb-4 animated">
            <h1><i class="fas fa-phone-alt me-2"></i> E.164 Number Range Audit Submission</h1>
            <p class="text-muted">National Communications Authority - Biannual Numbering Audit</p>
        </div>

        <ul class="nav nav-tabs mb-4 animated" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="submission-tab" type="button" role="tab" aria-controls="submission" aria-selected="false">
                    <i class="fas fa-edit me-2"></i>Data Submission
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="analytics-tab" type="button" role="tab" aria-controls="analytics" aria-selected="false">
                    <i class="fas fa-chart-bar me-2"></i>Analytics
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="history-tab" type="button" role="tab" aria-controls="history" aria-selected="false">
                    <i class="fas fa-history me-2"></i>Submission History
                </button>
            </li>
        </ul>

        <div class="tab-content">
            <!-- Submission Tab -->
            <div class="tab-pane fade show active" id="submission" role="tabpanel" aria-labelledby="submission-tab">
                <div class="glass-card form-section">
                    <h3 class="mb-4">Operator Information</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="operator" class="form-label">Operator</label>
                            <select class="form-select" id="operator" required>
                                <option value="" selected disabled>Select Operator</option>
                                {% for op in all_operators %}
                                    <option value="{{ op._id }}">{{ op.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reporting-period" class="form-label">Reporting Period</label>
                            <select class="form-select" id="reporting-period" required>
                                <option value="" disabled>Select Period</option>
                                {% for period in reporting_periods %}
                                    <option value="{{ period.value }}" {% if period.selected %}selected{% endif %}>
                                        {{ period.text }}
                                    </option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>

                <div class="glass-card form-section" style="animation-delay: 0.4s;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Number Range Data</h3>
                        <div>
                            <button class="btn btn-outline-secondary me-2" id="preview-btn">
                                <i class="fas fa-eye me-2"></i>Preview
                            </button>
                            <button class="btn btn-primary" id="add-range-btn">
                                <i class="fas fa-plus me-2"></i>Add Range
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="number-ranges-table">
                            <thead>
                                <tr>
                                    <th>NDC</th>
                                    <th>Type</th>
                                    <th>Starting Block</th>
                                    <th>Ending Block</th>
                                    <th>Total Allocated</th>
                                    <th>Active Subscriber</th>
                                    <th>Active Non-Subscriber</th>
                                    <th>Inactive</th>
                                    <th>Reserved</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Range rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>

<!-- Add this HTML to the submission_form.html file -->
                <!-- Insert this right after the "Number Range Data" section in the submission tab -->

                <div class="glass-card form-section" style="animation-delay: 0.5s;">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h3>Porting Data</h3>
                        <div>
                            <button class="btn btn-outline-secondary me-2" id="import-porting-btn">
                                <i class="fas fa-file-import me-2"></i>Import CSV
                            </button>
                            <button class="btn btn-primary" id="add-porting-btn">
                                <i class="fas fa-plus me-2"></i>Add Porting Record
                            </button>
                        </div>
                    </div>

                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This section is for detailed porting data between operators. Please specify source and target operators for each NDC where numbers have been ported.
                    </div>

                    <div class="table-responsive">
                        <table class="table table-hover" id="porting-data-table">
                            <thead>
                                <tr>
                                    <th>NDC</th>
                                    <th>Range Type</th>
                                    <th>Source Operator</th>
                                    <th>Target Operator</th>
                                    <th>Number of Ports</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Porting data rows will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>
                </div>



                <div class="glass-card form-section" style="animation-delay: 0.6s;">
                    <h3 class="mb-4">Additional Information</h3>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="submission-contact" class="form-label">Contact Person</label>
                            <input type="text" class="form-control" id="submission-contact" placeholder="Name of responsible person">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="submission-email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="submission-email" placeholder="Contact email address">
                        </div>
                        <div class="col-12 mb-3">
                            <label for="submission-notes" class="form-label">Additional Notes</label>
                            <textarea class="form-control" id="submission-notes" rows="3" placeholder="Any additional information or clarifications"></textarea>
                        </div>
                    </div>
                    <div class="d-flex justify-content-end mt-3">
                        <button class="btn btn-outline-secondary me-2" id="save-draft-btn">
                            <i class="fas fa-save me-2"></i>Save Draft
                        </button>
                        <button class="btn btn-primary" id="submit-btn">
                            <i class="fas fa-paper-plane me-2"></i>Submit
                        </button>
                    </div>
                </div>
            </div>

            <!-- Analytics Tab -->
            <div class="tab-pane fade" id="analytics" role="tabpanel" aria-labelledby="analytics-tab">
                <!-- Role-based alerts -->
                {% if is_admin %}
                <div class="alert alert-primary mb-4 animated fadeIn">
                    <i class="fas fa-user-shield me-2"></i> <strong>Admin View:</strong> You are viewing aggregated data across all operators.
                </div>
                {% else %}
                <div class="alert alert-info mb-4 animated fadeIn">
                    <i class="fas fa-user me-2"></i> <strong>Operator View:</strong> You are viewing data for your operator only.
                </div>
                {% endif %}

                <!-- Utilization metrics section -->
                <div class="glass-card animated">
                    <h3 class="mb-4">Utilization Metrics</h3>
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h5>Utilization Rate</h5>
                                <div class="metric-value">0.00%</div>
                                <div class="metric-trend text-muted"><i class="fas fa-equals"></i> 0.0%</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h5>Dormancy Rate</h5>
                                <div class="metric-value">0.00%</div>
                                <div class="metric-trend text-muted"><i class="fas fa-equals"></i> 0.0%</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h5>Reservation Rate</h5>
                                <div class="metric-value">0.00%</div>
                                <div class="metric-trend text-muted"><i class="fas fa-equals"></i> 0.0%</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="metric-card">
                                <h5>Growth Rate</h5>
                                <div class="metric-value">0.00%</div>
                                <div class="metric-trend text-muted"><i class="fas fa-equals"></i> 0.0%</div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" id="market-share-chart"></div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" id="status-distribution-chart"></div>
                        </div>
                    </div>
                </div>

                <!-- Admin-only section for operator comparison -->
                {% if is_admin %}
                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Operator Comparison</h3>
                    <div class="alert alert-secondary mb-3">
                        <i class="fas fa-info-circle me-2"></i> This section is only visible to administrators.
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" id="operator-comparison-chart"></div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" id="utilization-comparison-chart"></div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Porting activity section -->
                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Porting Activity</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" id="porting-chart"></div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" id="net-porting-chart"></div>
                        </div>
                    </div>
                </div>

                <!-- Admin-only porting matrix -->
                {% if is_admin %}
                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Inter-Operator Porting Matrix</h3>
                    <div class="alert alert-secondary mb-3">
                        <i class="fas fa-info-circle me-2"></i> This matrix shows porting activity between operators.
                    </div>
                    <div class="chart-container" id="porting-matrix-chart" style="height: 500px;"></div>
                </div>
                {% endif %}

                <!-- Resource planning section -->
                <div class="glass-card animated mt-4">
                    <h3 class="mb-4">Resource Planning</h3>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="chart-container" id="exhaustion-chart"></div>
                        </div>
                        <div class="col-md-6">
                            <div class="chart-container" id="block-efficiency-chart"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History Tab -->
            <div class="tab-pane fade" id="history" role="tabpanel" aria-labelledby="history-tab">
                <div class="glass-card animated">
                    <h3 class="mb-4">Submission History</h3>
                    {% if view_mode == 'detail' and submission %}
                    <!-- Detailed submission view -->
                    <div class="glass-card mb-4">
                        <h4>Submission Details</h4>
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Submission ID:</strong> {{ submission._id }}</p>
                                <p><strong>Reporting Period:</strong> {{ submission.reporting_period }}</p>
                                <p><strong>Operator:</strong> {{ submission.operator }}</p>
                                <p><strong>Contact Person:</strong> {{ submission.contact_person }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Status:</strong> <span class="badge {% if submission.status == 'approved' %}bg-success{% elif submission.status == 'rejected' %}bg-danger{% else %}bg-warning{% endif %}">{{ submission.status|title }}</span></p>
                                <p><strong>Created:</strong> {{ submission.created_at }}</p>
                                <p><strong>Updated:</strong> {{ submission.updated_at }}</p>
                                <p><strong>Contact Email:</strong> {{ submission.contact_email }}</p>
                            </div>
                        </div>

                        <div class="mt-4">
                            <h4>Metrics Summary</h4>
                            <div class="row">
                                <div class="col-md-3 mb-3">
                                    <div class="glass-card metric-card">
                                        <h5>Total Allocated</h5>
                                        <div class="metric-value">{{ metrics.total_allocated|format_number }}</div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="glass-card metric-card">
                                        <h5>Total Active</h5>
                                        <div class="metric-value">{{ metrics.total_active|format_number }}</div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="glass-card metric-card">
                                        <h5>Utilization Rate</h5>
                                        <div class="metric-value">{{ metrics.overall_utilization }}%</div>
                                    </div>
                                </div>
                                <div class="col-md-3 mb-3">
                                    <div class="glass-card metric-card">
                                        <h5>Total Ranges</h5>
                                        <div class="metric-value">{{ ranges|length }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <h4 class="mt-4">Number Ranges</h4>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>NDC</th>
                                        <th>Type</th>
                                        <th>Total Allocated</th>
                                        <th>Active Subscriber</th>
                                        <th>Active Non-Subscriber</th>
                                        <th>Inactive</th>
                                        <th>Utilization Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for range in ranges %}
                                    <tr>
                                        <td>{{ range.ndc }}</td>
                                        <td>{{ range.type }}</td>
                                        <td>{{ range.total_allocated|format_number }}</td>
                                        <td>{{ range.active_subscriber|format_number }}</td>
                                        <td>{{ range.active_non_subscriber|format_number }}</td>
                                        <td>{{ range.inactive|format_number }}</td>
                                        <td>{{ range.utilization_rate }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>


                        <!-- Add this to the submission_form.html template inside the detailed submission view section -->
                        <!-- This should go after the Number Ranges table in the history tab when viewing a submission -->

                        {% if submission %}
                        <!-- Only show this section if viewing a submission -->
                        <h4 class="mt-4">Porting Data</h4>
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>NDC</th>
                                        <th>Range Type</th>
                                        <th>Source Operator</th>
                                        <th>Target Operator</th>
                                        <th>Number of Ports</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% set porting_records = get_porting_records(submission._id) %}
                                    {% if porting_records %}
                                        {% for record in porting_records %}
                                        <tr>
                                            <td>{{ record.ndc }}</td>
                                            <td>{{ record.range_type }}</td>
                                            <td>{{ record.source_operator }}</td>
                                            <td>{{ record.target_operator }}</td>
                                            <td>{{ record.count|format_number }}</td>
                                        </tr>
                                        {% endfor %}
                                    {% else %}
                                        <tr>
                                            <td colspan="5" class="text-center">No porting data available for this submission</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                        {% endif %}














                        {% if submission.notes %}
                        <div class="mt-4">
                            <h4>Notes</h4>
                            <p>{{ submission.notes }}</p>
                        </div>
                        {% endif %}

                        <div class="mt-4">
                            <a href="{{ url_for('e164.dashboard') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
                            </a>
                            {% if current_user.is_admin and submission.status == 'pending' %}
                            <button class="btn btn-success ms-2 approve-btn" data-id="{{ submission._id }}">
                                <i class="fas fa-check me-2"></i>Approve
                            </button>
                            <button class="btn btn-danger ms-2 reject-btn" data-id="{{ submission._id }}">
                                <i class="fas fa-times me-2"></i>Reject
                            </button>
                            {% endif %}
                            {% if submission.status == 'approved' %}
                            <a href="{{ url_for('e164.download_report', submission_id=submission._id) }}" class="btn btn-primary ms-2">
                                <i class="fas fa-download me-2"></i>Download Report
                            </a>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Period</th>
                                    <th>Submission Date</th>
                                    <th>Status</th>
                                    <th>Total Ranges</th>
                                    <th>Overall Utilization</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for sub in submissions %}
                                <tr>
                                    <td>{{ sub.reporting_period }}</td>
                                    <td>{{ sub.created_at }}</td>
                                    <td><span class="badge {% if sub.status == 'approved' %}bg-success{% elif sub.status == 'rejected' %}bg-danger{% else %}bg-warning{% endif %}">{{ sub.status|title }}</span></td>
                                    <td>{{ sub.ranges_count if sub.ranges_count else 'N/A' }}</td>
                                    <td>{{ sub.metrics.utilization_rate|round(2) if sub.metrics else 'N/A' }}%</td>
                                    <td>
                                        <a href="{{ url_for('e164.view_submission', submission_id=sub._id) }}" class="btn btn-sm btn-outline-secondary me-1">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if sub.status == 'approved' %}
                                        <a href="{{ url_for('e164.download_report', submission_id=sub._id) }}" class="btn btn-sm btn-outline-primary me-1">
                                            <i class="fas fa-download"></i>
                                        </a>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Add Number Range Modal -->
    <div class="modal fade" id="addRangeModal" tabindex="-1" aria-labelledby="addRangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                <div class="modal-header">
                    <h5 class="modal-title" id="addRangeModalLabel">Add Number Range</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="rangeForm">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="ndc" class="form-label">NDC</label>
                                <select class="form-select" id="ndc" required>
                                    <option value="" selected disabled>Select NDC</option>
                                    <!-- Will be populated based on operator selection -->
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label for="ndc-type" class="form-label">Type of NDC</label>
                                <input type="text" class="form-control locked-field" id="ndc-type" readonly>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="starting-block" class="form-label">Starting Block</label>
                                <input type="text" class="form-control locked-field" id="starting-block" readonly>
                            </div>
                            <div class="col-md-6">
                                <label for="ending-block" class="form-label">Ending Block</label>
                                <input type="text" class="form-control locked-field" id="ending-block" readonly>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="total-allocated" class="form-label">Total Allocated Numbers (TAN)</label>
                                <input type="number" class="form-control locked-field" id="total-allocated" readonly>
                                <small class="help-text">Automatically calculated based on number range</small>
                            </div>
                            <div class="col-md-6">
                                <label for="active-subscriber" class="form-label">Active Subscriber Numbers (TAcN)</label>
                                <input type="number" class="form-control" id="active-subscriber" required>
                                <small class="help-text">Currently active subscriber numbers</small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="active-non-subscriber" class="form-label">Active Non-Subscriber Numbers</label>
                                <input type="number" class="form-control" id="active-non-subscriber">
                                <small class="help-text">Service numbers, short codes, etc.</small>
                            </div>
                            <div class="col-md-6">
                                <label for="inactive-numbers" class="form-label">Inactive Numbers (TIN)</label>
                                <input type="number" class="form-control" id="inactive-numbers" required>
                                <small class="help-text">Previously active but currently inactive</small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="reserved-numbers" class="form-label">Reserved Numbers (TRN)</label>
                                <input type="number" class="form-control" id="reserved-numbers">
                                <small class="help-text">Numbers reserved for future use</small>
                            </div>
                            <div class="col-md-6">
                                <label for="gross-addition" class="form-label">Gross Addition</label>
                                <input type="number" class="form-control" id="gross-addition">
                                <small class="help-text">New numbers activated in this period</small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="net-addition" class="form-label">Net Addition</label>
                                <input type="number" class="form-control" id="net-addition">
                                <small class="help-text">Net change in active numbers</small>
                            </div>
                            <div class="col-md-6">
                                <label for="ported-in" class="form-label">Numbers Ported In</label>
                                <input type="number" class="form-control" id="ported-in">
                                <small class="help-text">Numbers ported from other operators</small>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="ported-out" class="form-label">Numbers Ported Out</label>
                                <input type="number" class="form-control" id="ported-out">
                                <small class="help-text">Numbers ported to other operators</small>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="save-range-btn">Save Range</button>
                </div>
            </div>
        </div>
    </div>

                    <!-- Add this modal for adding porting data -->
                    <div class="modal fade" id="addPortingModal" tabindex="-1" aria-labelledby="addPortingModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="addPortingModalLabel">Add Porting Record</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <form id="portingForm">
                                        <div class="mb-3">
                                            <label for="porting-ndc" class="form-label">NDC</label>
                                            <input type="text" class="form-control" id="porting-ndc" required>
                                            <small class="help-text">Network Destination Code (e.g., 023, 020, 050)</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="porting-range-type" class="form-label">Range Type</label>
                                            <select class="form-select" id="porting-range-type" required>
                                                <option value="" selected disabled>Select Range Type</option>
                                                <option value="WIRELESS DEDICATED">WIRELESS DEDICATED</option>
                                                <option value="WIRELESS SHARED">WIRELESS SHARED</option>
                                                <option value="FIXED">FIXED</option>
                                                <option value="OTHER">OTHER</option>
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="porting-source" class="form-label">Source Operator</label>
                                            <select class="form-select" id="porting-source" required>
                                                <option value="" selected disabled>Select Source Operator</option>
                                                <!-- Will be populated dynamically with all operators -->
                                            </select>
                                            <small class="help-text">Operator numbers are ported FROM</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="porting-target" class="form-label">Target Operator</label>
                                            <select class="form-select" id="porting-target" required>
                                                <option value="" selected disabled>Select Target Operator</option>
                                                <!-- Will be populated dynamically with all operators -->
                                            </select>
                                            <small class="help-text">Operator numbers are ported TO</small>
                                        </div>
                                        <div class="mb-3">
                                            <label for="porting-count" class="form-label">Number of Ports</label>
                                            <input type="number" class="form-control" id="porting-count" min="1" required>
                                            <small class="help-text">How many numbers were ported</small>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="save-porting-btn">Save</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Add this modal for importing CSV porting data -->
                    <div class="modal fade" id="importPortingModal" tabindex="-1" aria-labelledby="importPortingModalLabel" aria-hidden="true">
                        <div class="modal-dialog">
                            <div class="modal-content" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px);">
                                <div class="modal-header">
                                    <h5 class="modal-title" id="importPortingModalLabel">Import Porting Data</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="alert alert-info">
                                        <i class="fas fa-info-circle me-2"></i>
                                        Upload a CSV file with porting data. The file should have headers and the following columns:
                                        <ul class="mb-0 mt-2">
                                            <li>NDC - Network Destination Code</li>
                                            <li>RangeType - Type of range (e.g., "WIRELESS DEDICATED")</li>
                                            <li>SourceOperator - Operator numbers are ported FROM</li>
                                            <li>TargetOperator - Operator numbers are ported TO</li>
                                            <li>Count - Number of ported numbers</li>
                                        </ul>
                                    </div>
                                    <form id="importPortingForm">
                                        <div class="mb-3">
                                            <label for="porting-csv" class="form-label">CSV File</label>
                                            <input type="file" class="form-control" id="porting-csv" accept=".csv" required>
                                        </div>
                                    </form>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="import-porting-csv-btn">Import</button>
                                </div>
                            </div>
                        </div>
                    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.0/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.0/chart.min.js"></script>
    <script>
// E.164 Application JavaScript
const E164App = {
    // Application state
    data: {
        latestData: null,
        historicalData: [],
        chartData: {"labels":[],"utilization":[],"dormancy":[],"reservation":[]},
        isAdmin: false,
        activeTab: ""
    },

    // Initialization function
    init: function() {
        // Import data from window namespace
        if (window.E164Data) {
            this.data.latestData = window.E164Data.latestData;
            this.data.historicalData = window.E164Data.historicalData;
            this.data.chartData = window.E164Data.chartData || {"labels":[],"utilization":[],"dormancy":[],"reservation":[]};
            this.data.isAdmin = window.E164Data.isAdmin;
            this.data.activeTab = window.E164Data.activeTab;

            // Ensure chartData has all required properties
            if (!this.data.chartData.porting_sankey) {
                console.warn("No porting_sankey data found, charts may not display correctly");
            }

            console.log("App initialized with data:", this.data);

            // Add explicit message about admin status and Sankey diagram
            if (this.data.isAdmin) {
                console.log("Admin user detected - Sankey diagram will be rendered if data is available");
            } else {
                console.log("Non-admin user detected - Sankey diagram will NOT be rendered (admin only feature)");
            }
        } else {
            console.error("E164Data not found in global scope");
        }

        // Listen for tab changes
        this.setupTabListeners();

        // Initialize form validation
        this.initFormValidation();

        // Initialize porting data handling
        this.initPortingDataHandling();

        // Initialize charts if we're on the analytics tab
        if (document.getElementById('analytics-tab') &&
            document.getElementById('analytics-tab').classList.contains('active')) {
            this.initializeCharts();
        }
    },

// Add these functions to the E164App object in submission_form.html

// Initialize porting data handling
initPortingDataHandling: function() {
    // Porting data array to store submissions
    this.portingData = [];

    // Reference to the app for event handlers
    const self = this;

    // Get operator list for dropdown
    this.populateOperatorDropdowns();

    // Add porting record button
    document.getElementById('add-porting-btn').addEventListener('click', function() {
        // Clear the form
        document.getElementById('portingForm').reset();

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('addPortingModal'));
        modal.show();
    });

    // Save porting record button
    document.getElementById('save-porting-btn').addEventListener('click', function() {
        self.savePortingRecord();
    });

    // Import button
    document.getElementById('import-porting-btn').addEventListener('click', function() {
        // Clear the form
        document.getElementById('importPortingForm').reset();

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('importPortingModal'));
        modal.show();
    });

    // CSV import button
    document.getElementById('import-porting-csv-btn').addEventListener('click', function() {
        self.importPortingCsv();
    });
},


// Handle form submission
submitForm: function() {
    // Validate required fields
    const operatorId = document.getElementById('operator').value;
    const operatorName = document.getElementById('operator').options[document.getElementById('operator').selectedIndex].text;
    const reportingPeriod = document.getElementById('reporting-period').value;
    
    if (!operatorId || !reportingPeriod) {
        alert('Please select an operator and reporting period.');
        return;
    }
    
    // Check if we have number ranges
    if (this.ranges.length === 0) {
        alert('Please add at least one number range.');
        return;
    }
    
    // Collect additional information
    const contactPerson = document.getElementById('submission-contact').value || '';
    const contactEmail = document.getElementById('submission-email').value || '';
    const notes = document.getElementById('submission-notes').value || '';
    
    // Build submission object
    const submission = {
        operator_id: operatorId,
        operator: operatorName,
        reporting_period: reportingPeriod,
        contact_person: contactPerson,
        contact_email: contactEmail,
        notes: notes,
        ranges: this.ranges
    };
    
    // Add porting data if we have any
    if (this.portingData && this.portingData.length > 0) {
        // Enrich porting data with submission metadata
        const enrichedPortingData = this.portingData.map(record => ({
            ...record,
            reporting_period: reportingPeriod,
            // Note: submission_id will be added after the submission is created
        }));
        
        submission.porting_data = enrichedPortingData;
    }
    
    // Show loading indicator
    const submitBtn = document.getElementById('submit-btn');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';
    submitBtn.disabled = true;
    
    // Send to server
    fetch('/e164/submission/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(submission)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('Submission created successfully!');
            
            // Redirect to view page if provided
            if (data.redirect_url) {
                window.location.href = data.redirect_url;
            }
        } else {
            // Show error message
            alert('Error creating submission: ' + (data.error || 'Unknown error'));
            
            // Reset button
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error creating submission:', error);
        alert('Error creating submission. Please try again.');
        
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
},

// Populate operator dropdowns for source and target
populateOperatorDropdowns: function() {
    const sourceSelect = document.getElementById('porting-source');
    const targetSelect = document.getElementById('porting-target');

    // Clear existing options (keep the placeholder)
    while (sourceSelect.options.length > 1) {
        sourceSelect.remove(1);
    }

    while (targetSelect.options.length > 1) {
        targetSelect.remove(1);
    }

    // Add operators from our data
    if (window.E164Data && window.E164Data.operators) {
        window.E164Data.operators.forEach(function(operator) {
            const sourceOption = new Option(operator.name, operator.name);
            const targetOption = new Option(operator.name, operator.name);

            sourceSelect.add(sourceOption);
            targetSelect.add(targetOption);
        });
    } else {
        // Fallback to add some sample operators
        const sampleOperators = ['MTN', 'AT', 'TELECEL', 'Other'];

        sampleOperators.forEach(function(operator) {
            const sourceOption = new Option(operator, operator);
            const targetOption = new Option(operator, operator);

            sourceSelect.add(sourceOption);
            targetSelect.add(targetOption);
        });
    }
},

// Save a porting record from the form
savePortingRecord: function() {
    // Get form values
    const ndc = document.getElementById('porting-ndc').value.trim();
    const rangeType = document.getElementById('porting-range-type').value;
    const sourceOperator = document.getElementById('porting-source').value;
    const targetOperator = document.getElementById('porting-target').value;
    const count = parseInt(document.getElementById('porting-count').value, 10);

    // Validate form
    if (!ndc || !rangeType || !sourceOperator || !targetOperator || isNaN(count) || count <= 0) {
        alert('Please fill in all fields correctly.');
        return;
    }

    // Create porting record
    const record = {
        ndc: ndc,
        range_type: rangeType,
        source_operator: sourceOperator,
        target_operator: targetOperator,
        count: count
    };

    // Add to our array
    this.portingData.push(record);

    // Update the table
    this.updatePortingTable();

    // Close the modal
    const modal = bootstrap.Modal.getInstance(document.getElementById('addPortingModal'));
    modal.hide();
},

// Update the porting data table
updatePortingTable: function() {
    const tableBody = document.querySelector('#porting-data-table tbody');

    // Clear existing rows
    tableBody.innerHTML = '';

    // Add rows for each record
    this.portingData.forEach((record, index) => {
        const row = document.createElement('tr');

        // Add cells
        row.innerHTML = `
            <td>${record.ndc}</td>
            <td>${record.range_type}</td>
            <td>${record.source_operator}</td>
            <td>${record.target_operator}</td>
            <td>${record.count.toLocaleString()}</td>
            <td>
                <button class="btn btn-sm btn-outline-danger delete-porting-btn" data-index="${index}">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        // Add to table
        tableBody.appendChild(row);
    });

    // Add event listeners to delete buttons
    document.querySelectorAll('.delete-porting-btn').forEach(button => {
        button.addEventListener('click', event => {
            const index = parseInt(event.currentTarget.getAttribute('data-index'), 10);
            this.deletePortingRecord(index);
        });
    });
},

// Delete a porting record
deletePortingRecord: function(index) {
    if (confirm('Are you sure you want to delete this porting record?')) {
        // Remove from array
        this.portingData.splice(index, 1);

        // Update table
        this.updatePortingTable();
    }
},

// Import porting data from CSV
importPortingCsv: function() {
    const fileInput = document.getElementById('porting-csv');
    const file = fileInput.files[0];

    if (!file) {
        alert('Please select a CSV file to import.');
        return;
    }

    // Check file type
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
        alert('Please select a CSV file.');
        return;
    }

    // Create file reader
    const reader = new FileReader();

    // Reference to the app
    const self = this;

    // Handle file load
    reader.onload = function(event) {
        try {
            // Get file content
            const csv = event.target.result;

            // Parse CSV
            const lines = csv.split('\n');
            const headers = lines[0].split(',').map(h => h.trim());

            // Validate headers
            const requiredHeaders = ['NDC', 'RangeType', 'SourceOperator', 'TargetOperator', 'Count'];
            const hasAllHeaders = requiredHeaders.every(h =>
                headers.some(header => header.toLowerCase() === h.toLowerCase())
            );

            if (!hasAllHeaders) {
                throw new Error('CSV file is missing required headers. Please ensure your file has: ' +
                    requiredHeaders.join(', '));
            }

            // Get indexes of each field
            const ndcIndex = headers.findIndex(h => h.toLowerCase() === 'ndc');
            const rangeTypeIndex = headers.findIndex(h => h.toLowerCase() === 'rangetype');
            const sourceIndex = headers.findIndex(h => h.toLowerCase() === 'sourceoperator');
            const targetIndex = headers.findIndex(h => h.toLowerCase() === 'targetoperator');
            const countIndex = headers.findIndex(h => h.toLowerCase() === 'count');

            // Parse data rows
            const importedData = [];

            for (let i = 1; i < lines.length; i++) {
                // Skip empty lines
                if (!lines[i].trim()) continue;

                const fields = lines[i].split(',').map(f => f.trim());

                // Parse count as number
                const count = parseInt(fields[countIndex], 10);

                // Validate count
                if (isNaN(count) || count <= 0) {
                    console.warn(`Skipping row ${i}: Invalid count value`);
                    continue;
                }

                // Create record
                const record = {
                    ndc: fields[ndcIndex],
                    range_type: fields[rangeTypeIndex],
                    source_operator: fields[sourceIndex],
                    target_operator: fields[targetIndex],
                    count: count
                };

                // Add to imported data
                importedData.push(record);
            }

            // Confirm import
            if (importedData.length === 0) {
                alert('No valid porting records found in the CSV file.');
                return;
            }

            const confirmImport = confirm(`Found ${importedData.length} porting records. Import them?`);

            if (confirmImport) {
                // Add to existing data
                self.portingData = self.portingData.concat(importedData);

                // Update table
                self.updatePortingTable();

                // Close the modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('importPortingModal'));
                modal.hide();
            }
        } catch (error) {
            alert('Error importing CSV: ' + error.message);
            console.error('CSV import error:', error);
        }
    };

    // Handle errors
    reader.onerror = function() {
        alert('Error reading the CSV file.');
    };

    // Read the file as text
    reader.readAsText(file);
},

// Get all porting data for form submission
getPortingData: function() {
    return this.portingData;
},

// Populate operator dropdowns for porting data
populateOperatorDropdowns: function() {
    // Get the source and target dropdowns
    const sourceDropdown = document.getElementById('porting-source');
    const targetDropdown = document.getElementById('porting-target');

    // Clear existing options except the first one
    while (sourceDropdown.options.length > 1) {
        sourceDropdown.remove(1);
    }

    while (targetDropdown.options.length > 1) {
        targetDropdown.remove(1);
    }

    // Get operator dropdown to copy options from
    const operatorDropdown = document.getElementById('operator');

    // If we have the operator dropdown, copy its options
    if (operatorDropdown) {
        // Skip the first option (the placeholder)
        for (let i = 1; i < operatorDropdown.options.length; i++) {
            const option = operatorDropdown.options[i];

            // Add to source dropdown
            const sourceOption = document.createElement('option');
            sourceOption.value = option.text; // Use the operator name as the value
            sourceOption.text = option.text;
            sourceDropdown.add(sourceOption);

            // Add to target dropdown
            const targetOption = document.createElement('option');
            targetOption.value = option.text; // Use the operator name as the value
            targetOption.text = option.text;
            targetDropdown.add(targetOption);
        }
    } else {
        console.warn('Operator dropdown not found, cannot populate porting operator dropdowns');
    }
},

    // Setup tab listeners
    setupTabListeners: function() {
        // Find all tab buttons
        const tabButtons = document.querySelectorAll('button.nav-link');

        // Add click event listeners to each tab button
        tabButtons.forEach(tab => {
            tab.addEventListener('click', (event) => {
                event.preventDefault();

                // Get the target content id from the aria-controls attribute
                const targetId = tab.getAttribute('aria-controls');
                if (!targetId) return;

                // Hide all tab contents
                document.querySelectorAll('.tab-pane').forEach(content => {
                    content.classList.remove('active', 'show');
                });

                // Show the target content
                const targetContent = document.getElementById(targetId);
                if (targetContent) {
                    targetContent.classList.add('active', 'show');
                }

                // Update active tab styling
                tabButtons.forEach(t => {
                    t.classList.remove('active');
                    t.setAttribute('aria-selected', 'false');
                });
                tab.classList.add('active');
                tab.setAttribute('aria-selected', 'true');

                // If the analytics tab was clicked, initialize charts
                if (targetId === 'analytics') {
                    this.initializeCharts();
                }

                // Update URL with the active tab (without refreshing the page)
                if (history.pushState) {
                    const newUrl = new URL(window.location);
                    newUrl.searchParams.set('tab', targetId);
                    window.history.pushState({path: newUrl.toString()}, '', newUrl.toString());
                }

                // Update the body data-active-tab attribute
                document.body.setAttribute('data-active-tab', targetId);

                console.log(`Tab changed to: ${targetId}`);
            });
        });

        console.log("Tab listeners setup complete");
    },

    // Initialize form validation
    initFormValidation: function() {
        // Implementation will go here
        console.log("Initializing form validation");
    },

    // Initialize charts
    initializeCharts: function() {
        console.log("Initializing charts with data:", this.data);

        // Render market share chart
        this.renderMarketShareChart();

        // Render status distribution chart
        this.renderStatusDistributionChart();

        // Render porting charts
        this.renderPortingCharts();

        // Render admin-specific charts if admin
        if (this.data.isAdmin) {
            this.renderOperatorComparisonChart();

            // Only render porting matrix chart for admin users
            console.log("Admin user detected, checking for porting_sankey data:", this.data.chartData.porting_sankey);
            if (this.data.chartData.porting_sankey) {
                this.renderPortingMatrixChart();
            }
        }

        // Render resource planning charts
        this.renderResourcePlanningCharts();
    },

    // Prepare chart container
    prepareChartContainer: function(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`Chart container not found for: ${containerId}`);
            return null;
        }

        // Clear the container and create a new canvas
        container.innerHTML = '';
        const canvas = document.createElement('canvas');
        container.appendChild(canvas);

        return canvas;
    },

    // Render market share chart
    renderMarketShareChart: function() {
        const canvas = this.prepareChartContainer('market-share-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'pie',
                data: {
                    labels: ['Operator A', 'Operator B', 'Operator C'],
                    datasets: [{
                        data: [30, 40, 30],
                        backgroundColor: [
                            'rgba(26, 188, 156, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(52, 152, 219, 0.7)'
                        ],
                        borderColor: [
                            'rgba(26, 188, 156, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        } catch (err) {
            console.error("Error creating market share chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render status distribution chart
    renderStatusDistributionChart: function() {
        const canvas = this.prepareChartContainer('status-distribution-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Active', 'Inactive', 'Reserved'],
                    datasets: [{
                        data: [65, 25, 10],
                        backgroundColor: [
                            'rgba(26, 188, 156, 0.7)',
                            'rgba(231, 76, 60, 0.7)',
                            'rgba(241, 196, 15, 0.7)'
                        ],
                        borderColor: [
                            'rgba(26, 188, 156, 1)',
                            'rgba(231, 76, 60, 1)',
                            'rgba(241, 196, 15, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        } catch (err) {
            console.error("Error creating status distribution chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render porting charts
    renderPortingCharts: function() {
        this.renderPortingActivityChart();
        this.renderNetPortingChart();
    },

    // Render porting activity chart
    renderPortingActivityChart: function() {
        const canvas = this.prepareChartContainer('porting-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Ported In',
                        data: [12, 19, 3, 5, 2, 3],
                        backgroundColor: 'rgba(26, 188, 156, 0.7)',
                        borderColor: 'rgba(26, 188, 156, 1)',
                        borderWidth: 1
                    },
                    {
                        label: 'Ported Out',
                        data: [5, 15, 10, 8, 7, 4],
                        backgroundColor: 'rgba(231, 76, 60, 0.7)',
                        borderColor: 'rgba(231, 76, 60, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } catch (err) {
            console.error("Error creating porting activity chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render net porting chart
    renderNetPortingChart: function() {
        const canvas = this.prepareChartContainer('net-porting-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Operator A', 'Operator B', 'Operator C'],
                    datasets: [{
                        label: 'Net Porting Position',
                        data: [25, -15, 10],
                        backgroundColor: [
                            'rgba(26, 188, 156, 0.7)',
                            'rgba(231, 76, 60, 0.7)',
                            'rgba(26, 188, 156, 0.7)'
                        ],
                        borderColor: [
                            'rgba(26, 188, 156, 1)',
                            'rgba(231, 76, 60, 1)',
                            'rgba(26, 188, 156, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } catch (err) {
            console.error("Error creating net porting chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render operator comparison chart
    renderOperatorComparisonChart: function() {
        const canvas = this.prepareChartContainer('operator-comparison-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Operator A', 'Operator B', 'Operator C'],
                    datasets: [{
                        label: 'Utilization Rate (%)',
                        data: [65, 45, 80],
                        backgroundColor: [
                            'rgba(26, 188, 156, 0.7)',
                            'rgba(46, 204, 113, 0.7)',
                            'rgba(52, 152, 219, 0.7)'
                        ],
                        borderColor: [
                            'rgba(26, 188, 156, 1)',
                            'rgba(46, 204, 113, 1)',
                            'rgba(52, 152, 219, 1)'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        } catch (err) {
            console.error("Error creating operator comparison chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render porting matrix chart
// Replace the renderPortingMatrixChart and renderSankeyDiagram functions in the E164App object

// Render porting matrix chart - for both admin and operator views
renderPortingMatrixChart: function() {
    const container = document.getElementById('porting-matrix-chart');
    if (!container) {
        console.error("Porting matrix chart container not found");
        return;
    }

    try {
        // Check if we have Sankey data
        const portingSankey = this.data.chartData.porting_sankey;

        if (!portingSankey || !portingSankey.nodes || !portingSankey.links ||
            !Array.isArray(portingSankey.nodes) || !Array.isArray(portingSankey.links)) {
            console.warn("No valid Sankey data available:", portingSankey);
            container.innerHTML = '<div class="alert alert-warning">' +
                '<i class="fas fa-exclamation-triangle me-2"></i>' +
                'No valid wireless porting data available. Please ensure data has been submitted with WIRELESS number ranges.' +
                '</div>';
            return;
        }

        // Check if we need to load d3-sankey
        if (typeof d3 === 'undefined' || typeof d3.sankey === 'undefined') {
            console.log("Loading D3 and D3-Sankey libraries");

            // Create placeholder while loading
            container.innerHTML = '<div class="alert alert-info text-center">' +
                '<i class="fas fa-spinner fa-spin me-2"></i>' +
                'Loading porting visualization...' +
                '</div>';

            // Function to load script with Promise
            const loadScript = (src) => {
                return new Promise((resolve, reject) => {
                    const script = document.createElement('script');
                    script.src = src;
                    script.onload = resolve;
                    script.onerror = reject;
                    document.head.appendChild(script);
                });
            };

            // Load D3 first, then D3-Sankey, then render
            loadScript('https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js')
                .then(() => loadScript('https://cdn.jsdelivr.net/npm/d3-sankey@0.12.3/dist/d3-sankey.min.js'))
                .then(() => this.renderSankeyDiagram(portingSankey))
                .catch(err => {
                    console.error("Error loading D3 libraries:", err);
                    container.innerHTML = '<div class="alert alert-danger">' +
                        '<i class="fas fa-exclamation-circle me-2"></i>' +
                        'Error loading visualization libraries: ' + err.message +
                        '</div>';
                });
        } else {
            // We already have D3 and D3-Sankey, render directly
            this.renderSankeyDiagram(portingSankey);
        }
    } catch (e) {
        console.error("Error initializing Sankey diagram:", e);
        container.innerHTML = '<div class="alert alert-danger">' +
            '<i class="fas fa-exclamation-circle me-2"></i>' +
            'Error initializing diagram: ' + e.message +
            '</div>';
    }
},

// Render the actual Sankey diagram
renderSankeyDiagram: function(data) {
    const container = document.getElementById('porting-matrix-chart');
    if (!container) return;

    // Clear previous content
    container.innerHTML = '';

    try {
        // Set dimensions
        const margin = {top: 30, right: 50, bottom: 30, left: 50};
        const width = container.clientWidth - margin.left - margin.right;
        const height = container.clientHeight - margin.top - margin.bottom;

        // Create SVG
        const svg = d3.select(container).append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        // Add title
        const title = this.data.isAdmin ?
            "Inter-Operator Wireless Number Porting Activity" :
            "Wireless Number Porting Activity";

        svg.append("text")
            .attr("x", width / 2)
            .attr("y", -10)
            .attr("text-anchor", "middle")
            .style("font-size", "14px")
            .style("font-weight", "bold")
            .text(title);

        // Format nodes for d3-sankey
        const nodes = data.nodes.map((d, i) => ({
            name: d.name,
            id: i
        }));

        // Format links for d3-sankey - ensure values are positive
        const links = data.links.map(d => ({
            source: d.source,
            target: d.target,
            value: Math.max(1, d.value) // Ensure minimum value for visibility
        }));

        // Create sankey generator
        const sankey = d3.sankey()
            .nodeId(d => d.id)
            .nodeWidth(25)
            .nodePadding(15)
            .extent([[0, 0], [width, height]]);

        // Generate layout
        const { nodes: sankeyNodes, links: sankeyLinks } = sankey({
            nodes: nodes,
            links: links
        });

        // Create color scale - use a consistent color palette
        const colorScale = d3.scaleOrdinal()
            .domain(Array.from({ length: nodes.length }, (_, i) => i))
            .range(d3.schemeCategory10);

        // Add links with gradient
        const link = svg.append("g")
            .selectAll(".link")
            .data(sankeyLinks)
            .enter()
            .append("path")
            .attr("class", "link")
            .attr("d", d3.sankeyLinkHorizontal())
            .attr("stroke-width", d => Math.max(1, d.width))
            .style("fill", "none")
            .style("stroke-opacity", 0.5)
            .style("stroke", (d, i) => {
                // Generate a unique gradient ID
                const gradientId = `link-gradient-${i}`;

                // Define gradient
                const gradient = svg.append("defs")
                    .append("linearGradient")
                    .attr("id", gradientId)
                    .attr("gradientUnits", "userSpaceOnUse")
                    .attr("x1", d.source.x1)
                    .attr("x2", d.target.x0);

                // Set gradient colors
                gradient.append("stop")
                    .attr("offset", "0%")
                    .attr("stop-color", colorScale(d.source.id));

                gradient.append("stop")
                    .attr("offset", "100%")
                    .attr("stop-color", colorScale(d.target.id));

                return `url(#${gradientId})`;
            });

        // Add hover effects and tooltips to links
        link.append("title")
            .text(d => {
                const value = d.value.toLocaleString();
                return `${d.source.name} → ${d.target.name}\n${value} ported numbers`;
            });

        // Add nodes
        const node = svg.append("g")
            .selectAll(".node")
            .data(sankeyNodes)
            .enter()
            .append("rect")
            .attr("class", "node")
            .attr("x", d => d.x0)
            .attr("y", d => d.y0)
            .attr("height", d => Math.max(1, d.y1 - d.y0))
            .attr("width", d => d.x1 - d.x0)
            .style("fill", d => colorScale(d.id))
            .style("stroke", d => d3.rgb(colorScale(d.id)).darker())
            .style("opacity", 0.8);

        // Add hover effects and tooltips to nodes
        node.append("title")
            .text(d => {
                const total = d.value.toLocaleString();
                return `${d.name}\n${total} ported numbers`;
            });

        // Add labels
        svg.append("g")
            .selectAll(".label")
            .data(sankeyNodes)
            .enter()
            .append("text")
            .attr("class", "label")
            .attr("x", d => {
                // Position labels based on position in the diagram
                return d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6;
            })
            .attr("y", d => (d.y1 + d.y0) / 2)
            .attr("dy", "0.35em")
            .attr("text-anchor", d => d.x0 < width / 2 ? "start" : "end")
            .text(d => d.name)
            .style("font-size", "12px")
            .style("font-weight", "500")
            .style("fill", "#333")
            .style("pointer-events", "none");

        // Add a source note at the bottom
        const noteText = this.data.isAdmin ?
            "Showing wireless porting data across all operators" :
            "Showing wireless porting data for your operator";

        svg.append("text")
            .attr("x", width)
            .attr("y", height + 15)
            .attr("text-anchor", "end")
            .style("font-size", "10px")
            .style("font-style", "italic")
            .style("fill", "#666")
            .text(noteText);

        console.log("Sankey diagram rendered successfully");
    } catch (e) {
        console.error("Error rendering Sankey diagram:", e);
        container.innerHTML = '<div class="alert alert-danger">' +
            '<i class="fas fa-exclamation-circle me-2"></i>' +
            'Error rendering diagram: ' + e.message +
            '</div>';
    }
},
    








// Render resource planning charts
    renderResourcePlanningCharts: function() {
        this.renderExhaustionChart();
        this.renderBlockEfficiencyChart();
    },

    // Render exhaustion chart
    renderExhaustionChart: function() {
        const canvas = this.prepareChartContainer('exhaustion-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['NDC 1', 'NDC 2', 'NDC 3', 'NDC 4'],
                    datasets: [{
                        label: 'Years Until Exhaustion',
                        data: [15, 8, 20, 5],
                        backgroundColor: 'rgba(142, 68, 173, 0.7)',
                        borderColor: 'rgba(142, 68, 173, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        } catch (err) {
            console.error("Error creating exhaustion chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    },

    // Render block efficiency chart
    renderBlockEfficiencyChart: function() {
        const canvas = this.prepareChartContainer('block-efficiency-chart');
        if (!canvas) return;

        try {
            const ctx = canvas.getContext('2d');
            new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['NDC 1', 'NDC 2', 'NDC 3', 'NDC 4'],
                    datasets: [{
                        label: 'Block Efficiency (%)',
                        data: [75, 60, 85, 55],
                        backgroundColor: 'rgba(52, 73, 94, 0.7)',
                        borderColor: 'rgba(52, 73, 94, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        } catch (err) {
            console.error("Error creating block efficiency chart:", err);
            canvas.parentElement.innerHTML = '<div class="alert alert-danger">Error creating chart: ' + err.message + '</div>';
        }
    }
};

// Initialize the app when the DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    // Initialize the application
    E164App.init();
});
</script>