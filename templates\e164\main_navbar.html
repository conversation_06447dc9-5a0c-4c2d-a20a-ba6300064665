{# holding/templates/e164/main_navbar.html - Bootstrap 5 Version #}
<nav class="navbar navbar-expand-lg navbar-dark bg-dark sticky-top">
    <div class="container-fluid">
        <a class="navbar-brand" href="{{ url_for('search') }}">
            <i class="fas fa-phone-alt me-2"></i>NCA Numbering {# BS5 uses me-2 for margin-end #}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#mainNavbarAltMarkupBs5" aria-controls="mainNavbarAltMarkupBs5" aria-expanded="false" aria-label="Toggle navigation"> {# BS5 data-bs-* attributes #}
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="mainNavbarAltMarkupBs5">
            <ul class="navbar-nav me-auto mb-2 mb-lg-0"> {# BS5: me-auto and mb-2 mb-lg-0 for spacing #}
                {% if current_user.is_authenticated %}
                    {% if current_user.is_admin %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('search') or request.endpoint == 'dashboard' and not request.endpoint.startswith('e164') %}active{% endif %}" href="{{ url_for('search') }}">SNR Management</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('e164.') %}active{% endif %}" href="{{ url_for('e164.dashboard') }}">E.164 Audit</a>
                        </li>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle {% if request.endpoint in ['review_applications', 'generate_report', 'e164.list_users', 'e164.review_pending_submissions', 'e164.analytics'] %}active{% endif %}" href="#" id="adminToolsDropdownBs5" role="button" data-bs-toggle="dropdown" aria-expanded="false"> {# BS5 data-bs-toggle #}
                                Admin Tools
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark" aria-labelledby="adminToolsDropdownBs5"> {# BS5 dropdown-menu-dark #}
                                <li><a class="dropdown-item {% if request.endpoint == 'review_applications' %}active{% endif %}" href="{{ url_for('review_applications') }}">SNR Application Review</a></li>
                                <li><a class="dropdown-item {% if request.endpoint == 'generate_report' %}active{% endif %}" href="{{ url_for('generate_report') }}">SNR Reports</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item {% if request.endpoint == 'e164.list_users' %}active{% endif %}" href="{{ url_for('e164.list_users') }}">User Management</a></li>
                                <li><a class="dropdown-item {% if request.endpoint == 'e164.review_pending_submissions' %}active{% endif %}" href="{{ url_for('e164.review_pending_submissions') }}">Review E.164 Submissions</a></li>
                                <li><a class="dropdown-item {% if request.endpoint == 'e164.analytics' %}active{% endif %}" href="{{ url_for('e164.analytics') }}">E.164 Analytics</a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'search' %}active{% endif %}" href="{{ url_for('search') }}">Find Numbers</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint == 'dashboard' %}active{% endif %}" href="{{ url_for('dashboard') }}">My SNR Applications</a>
                        </li>
                        {% if current_user.operator_id %}
                        <li class="nav-item">
                            <a class="nav-link {% if request.endpoint.startswith('e164.') and 'submission' in request.base_url %}active{% endif %}" href="{{ url_for('e164.dashboard', tab='submission') }}">E.164 Submissions</a>
                        </li>
                        {% endif %}
                    {% endif %}
                {% endif %}
            </ul>

            <ul class="navbar-nav ms-auto"> {# BS5: ms-auto for right alignment. Or remove ms-auto if me-auto on the left list is enough #}
                {% if current_user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="mainNotificationsDropdownBs5" role="button" data-bs-toggle="dropdown" aria-expanded="false"> {# BS5 data-bs-toggle #}
                            <i class="fas fa-bell"></i>
                            {% if unread_notifications_count > 0 %}
                                <span class="badge rounded-pill bg-danger">{{ unread_notifications_count }}</span> {# BS5 rounded-pill #}
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end" aria-labelledby="mainNotificationsDropdownBs5" style="min-width: 300px; max-height: 400px; overflow-y: auto;"> {# BS5 dropdown-menu-end & dropdown-menu-dark #}
                            {% if notifications %}
                                {% for notification in notifications %}
                                    <li><a class="dropdown-item" href="{{ url_for('view_notification', notification_id=notification._id) }}">
                                        <small>{{ notification.message }}</small><br>
                                        <small class="text-muted">{{ notification.timestamp.strftime('%Y-%m-%d %H:%M') }}</small>
                                    </a></li>
                                {% endfor %}
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item text-center" href="{{ url_for('view_notifications') }}">View all notifications</a></li>
                            {% else %}
                                <li><a class="dropdown-item text-center text-muted" href="#">No new notifications</a></li>
                            {% endif %}
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="mainUserDropdownBs5" role="button" data-bs-toggle="dropdown" aria-expanded="false"> {# BS5 data-bs-toggle #}
                            <i class="fas fa-user me-1"></i> {# BS5 me-1 #}
                            {{ current_user.contact if current_user.contact else current_user.email }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-dark dropdown-menu-end" aria-labelledby="mainUserDropdownBs5"> {# BS5 dropdown-menu-end & dropdown-menu-dark #}
                            <li><a class="dropdown-item" href="{{ url_for('update_account') }}">My Account</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="fas fa-sign-out-alt me-2"></i>Logout</a></li>
                        </ul>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('login') }}">Login</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('register') }}">Register</a>
                    </li>
                {% endif %}
            </ul>
        </div>
    </div>
</nav>