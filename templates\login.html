<!-- templates/login.html -->
{% extends "base.html" %}
{% block content %}
<div class="d-flex justify-content-center align-items-center min-vh-100">
    <div class="card glassmorphic-card p-4" style="width: 100%; max-width: 400px;">
        <div class="card-body">
            <h2 class="card-title text-center">Login</h2>
            <form method="POST" novalidate>
                {{ form.hidden_tag() }}
                <div class="form-group">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="form-control", placeholder="Enter your Username") }}
                    {% for error in form.username.errors %}
                    <div class="text-danger">{{ error }}</div>
                 
                    {% endfor %}
                </div>
                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    {{ form.password(class="form-control", placeholder="Enter your Password") }}
                    {% for error in form.password.errors %}
                    <div class="text-danger">{{ error }}</div>
                    {% endfor %}
                </div>
                <button type="submit" class="btn btn-primary btn-block mt-4">Login</button>
            </form>
            <p class="mt-3 text-center">Don't have an account? <a href="{{ url_for('register') }}">Register here</a></p>
        </div>
    </div>
</div>
{% endblock %}
