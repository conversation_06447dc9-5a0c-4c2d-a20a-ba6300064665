<!-- templates/edit_application.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Edit Application Details</h2>

                    <form method="POST" action="{{ url_for('edit_application', application_id=application.application_id) }}">
                        <div class="form-group">
                            <label for="applicant_name">Applicant Name</label>
                            <input type="text" name="applicant_name" id="applicant_name" class="form-control" value="{{ application.applicant_name }}" required>
                        </div>

                        {% for snr in snrs %}
                        <div class="form-group">
                            <label for="purpose_{{ loop.index }}">Purpose of SNR {{ snr.snr }}</label>
                            <input type="text" name="purpose_{{ loop.index }}" id="purpose_{{ loop.index }}" class="form-control" value="{{ snr.purpose }}" required>
                        </div>
                        {% endfor %}

                        <div class="form-group">
                            <label for="expiry_date">Expiry Date</label>
                            <input type="date" name="expiry_date" id="expiry_date" class="form-control"
                                   value="{% if application.certificate_expiry_date %}{{ application.certificate_expiry_date }}{% else %}{{ (datetime.now() + timedelta(days=365)).strftime('%Y-%m-%d') }}{% endif %}">
                        </div>

                        <div class="form-group">
                            <label for="remarks">Remarks</label>
                            <input type="text" name="remarks" id="remarks" class="form-control" value="{{ application.remarks }}">
                        </div>

                        <div class="form-group">
                            <label for="status">Application Status</label>
                            <select name="status" id="status" class="form-control">
                                <option value="Pending Review" {% if application.status == 'Pending Review' %}selected{% endif %}>Pending Review</option>
                                <option value="Approved" {% if application.status == 'Approved' %}selected{% endif %}>Approved</option>
                                <option value="Revoked" {% if application.status == 'Revoked' %}selected{% endif %}>Revoked</option>
                                <option value="Expired" {% if application.status == 'Expired' %}selected{% endif %}>Expired</option>
                            </select>
                        </div>

                        <button type="submit" class="btn btn-primary btn-block mt-4">Save Changes</button>
                    </form>

                    <a href="{{ url_for('review_applications') }}" class="btn btn-secondary btn-block mt-2">Cancel</a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.querySelector('form');

        // Adding a submit listener to handle form submission
        form.addEventListener('submit', function (event) {
            // Perform some validations on required fields
            const applicantName = document.getElementById('applicant_name').value.trim();
            let purposeFilled = true;
            const expiryDate = document.getElementById('expiry_date').value.trim();
            const status = document.getElementById('status').value;

            // Check if all required fields are filled
            {% for snr in snrs %}
            const purpose_{{ loop.index }} = document.getElementById('purpose_{{ loop.index }}').value.trim();
            if (!purpose_{{ loop.index }}) {
                purposeFilled = false;
            }
            {% endfor %}

            if (!applicantName || !purposeFilled || !expiryDate) {
                alert('Please fill in all required fields: Applicant Name, Purpose, and Expiry Date.');
                event.preventDefault();  // Prevent form submission if any required field is empty
                return;
            }

            // Confirmation for critical status changes
            if (status === 'Revoked' || status === 'Expired') {
                const confirmChange = confirm(`Are you sure you want to change the status to ${status}? This action might be irreversible.`);
                if (!confirmChange) {
                    event.preventDefault();  // Prevent form submission if the user cancels
                    return;
                }
            }
        });

        // Optional: Additional listeners to handle change in values (e.g., updating UI dynamically)
        const statusElement = document.getElementById('status');
        statusElement.addEventListener('change', function () {
            if (this.value === 'Revoked' || this.value === 'Expired') {
                alert('You selected a critical status. Please make sure this action is intended.');
            }
        });
    });
</script>

{% endblock %}
