{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <h2>Notifications</h2>
    <form method="POST" action="{{ url_for('view_notifications') }}">
        <div class="list-group">
            {% for notification in notifications %}
            <div class="list-group-item list-group-item-action flex-column align-items-start">
                <div class="d-flex w-100 justify-content-between">
                    <div>
                        <input type="checkbox" name="notification_ids" value="{{ notification._id }}" />
                        <strong>{{ notification.message }}</strong>
                    </div>
                    <small class="text-muted">{{ notification.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</small>
                </div>
                {% if notification.read %}
                    <span class="badge badge-secondary">Read</span>
                {% else %}
                    <span class="badge badge-primary">Unread</span>
                {% endif %}
            </div>
            {% endfor %}
        </div>
        <button type="submit" class="btn btn-primary mt-3">Mark as Read</button>
    </form>
</div>
{% endblock %}
