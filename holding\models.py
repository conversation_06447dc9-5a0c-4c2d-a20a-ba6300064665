# models.py
from flask_login import User<PERSON>ixin, AnonymousUserMixin
from bson.objectid import ObjectId

class Anonymous(AnonymousUserMixin):
    def __init__(self):
        self.id = None
        self.contact = 'Guest'
        self.email = ''
        self.company = ''

class User(UserMixin):
    def __init__(self, user_data):
        self.id = str(user_data['_id'])
        self.is_admin = user_data.get('is_admin', False)
        self.email = user_data.get('email', '')
        
        # Add support for operators
        self.operator_id = user_data.get('operator_id')
        
        if not self.is_admin and not self.operator_id:  # Regular VAS user
            self.vas_id = user_data.get('vas_id')
            if self.vas_id is None:
                raise ValueError("vas_id is required for non-admin, non-operator users.")
            self.contact = user_data.get('contact', '')
            self.phone = user_data.get('phone', '')
            self.national_id = user_data.get('national_id', None)
            self.photo = user_data.get('photo', None)
            self.company = user_data.get('vas_company', '')
        elif self.operator_id:  # Operator user
            self.contact = user_data.get('contact', '')
            self.phone = user_data.get('phone', '')
            self.company = user_data.get('operator_name', '')
        else:  # Admin user
            self.contact = user_data.get('contact', 'Admin')