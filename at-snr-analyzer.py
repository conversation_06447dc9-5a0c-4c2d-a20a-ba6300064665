# Install these dependencies before running the script:
# pip install pymongo pandas numpy matplotlib seaborn scikit-learn fuzzywuzzy python-Levenshtein openpyxl

import pandas as pd
import numpy as np
from pymongo import MongoClient
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
from openpyxl import Workbook, load_workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import re
import json
from sklearn.feature_extraction.text import TfidfVectorizer
from fuzzywuzzy import fuzz

# MongoDB connection
def connect_to_mongodb():
    uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
    client = MongoClient(uri)
    return client

# Function to retrieve app data from MongoDB
def get_app_data():
    client = connect_to_mongodb()
    db = client.get_database("captains_log")
    collection = db.get_collection(name="app")
    data = list(collection.find())
    app_df = pd.DataFrame(data)
    return app_df

# Load the predefined harmonized code list with categories
def load_harmonized_codes():
    harmonized_codes = {
        # Common emergency codes (generally 3 digits)
        'emergency': [
            {'code': '110', 'description': 'Police'},
            {'code': '111', 'description': 'Fire Service'},
            {'code': '112', 'description': 'Emergency Services (Combined)'},
            {'code': '191', 'description': 'Police'},
            {'code': '192', 'description': 'Fire'},
            {'code': '193', 'description': 'Ambulance'},
            {'code': '194', 'description': 'Disaster Management'},
            {'code': '198', 'description': 'National Security'},
            {'code': '199', 'description': 'National Emergency'}
        ],
        
        # Customer service codes
        'customer_service': [
            {'code': '100', 'description': 'Operator Assistance'},
            {'code': '101', 'description': 'Customer Service'},
            {'code': '102', 'description': 'Directory Enquiries'},
            {'code': '103', 'description': 'International Access'},
            {'code': '104', 'description': 'Operator Assistance'},
            {'code': '123', 'description': 'Voicemail'}
        ],
        
        # Common service codes
        'service_codes': [
            {'code': '120', 'description': 'Time Information'},
            {'code': '121', 'description': 'Weather Information'},
            {'code': '122', 'description': 'Traffic Information'},
            {'code': '124', 'description': 'Pre-selection Service'},
            {'code': '125', 'description': 'Service Activation'},
            {'code': '126', 'description': 'Service Information'},
            {'code': '127', 'description': 'Network Information'},
            {'code': '128', 'description': 'Service Check'},
            {'code': '129', 'description': 'Prepaid Balance'}
        ],
        
        # Toll-free numbers
        'toll_free': [
            {'code': '800', 'description': 'Toll-Free Prefix'},
            {'code': '0800', 'description': 'Toll-Free Prefix'}
        ],
        
        # Premium rate services
        'premium': [
            {'code': '900', 'description': 'Premium Rate Prefix'},
            {'code': '0900', 'description': 'Premium Rate Prefix'}
        ],
        
        # Mobile money codes
        'mobile_money': [
            {'code': '222', 'description': 'Mobile Money Service'},
            {'code': '200', 'description': 'Money Service'}
        ]
    }
    
    # Flatten the dictionary into a DataFrame
    flat_codes = []
    for category, codes in harmonized_codes.items():
        for code_info in codes:
            flat_codes.append({
                'code': code_info['code'],
                'description': code_info['description'],
                'category': category,
                'is_harmonized': True
            })
    
    return pd.DataFrame(flat_codes)

def preprocess_text(text):
    """Clean and preprocess text for better matching"""
    if text is None:
        return ""
    
    # Convert to string and lowercase
    text = str(text).lower()
    
    # Remove special characters, keeping alphanumeric and spaces
    text = re.sub(r'[^\w\s]', ' ', text)
    
    # Replace multiple spaces with single space
    text = re.sub(r'\s+', ' ', text).strip()
    
    return text

def quick_fuzzy_match(text1, text2):
    """Fast fuzzy matching using fuzzywuzzy"""
    # Preprocess both texts
    clean_text1 = preprocess_text(text1)
    clean_text2 = preprocess_text(text2)
    
    # If either text is empty after preprocessing
    if not clean_text1 or not clean_text2:
        return 0
    
    # Use token_set_ratio which is good for partial matches regardless of word order
    ratio = fuzz.token_set_ratio(clean_text1, clean_text2)
    
    # Normalize to 0-1 scale (from fuzzywuzzy's 0-100 scale)
    return ratio / 100.0

def fast_text_similarity(query_text, corpus_texts, min_threshold=0.6):
    """
    Much faster text similarity function that doesn't use API calls
    
    Args:
        query_text (str): The text to compare against the corpus
        corpus_texts (list): List of texts to compare with
        min_threshold (float): Minimum similarity threshold
        
    Returns:
        list: Sorted list of (similarity_score, index) tuples
    """
    # Clean inputs
    query_text = preprocess_text(query_text)
    corpus_texts = [preprocess_text(text) for text in corpus_texts]
    
    # Filter out empty texts
    valid_corpus = [(text, i) for i, text in enumerate(corpus_texts) if text]
    
    if not query_text or not valid_corpus:
        return []
    
    # First pass: Use fast fuzzy matching to filter candidates
    candidates = []
    for text, idx in valid_corpus:
        score = quick_fuzzy_match(query_text, text)
        if score >= min_threshold:
            candidates.append((text, idx, score))
    
    # If we have very few candidates or none at all, return fuzzy results
    if len(candidates) <= 5:
        return [(score, idx) for _, idx, score in sorted(candidates, key=lambda x: x[2], reverse=True)]
    
    # For more precision on a smaller set of candidates, use TF-IDF with cosine similarity
    texts = [query_text] + [text for text, _, _ in candidates]
    indices = [idx for _, idx, _ in candidates]
    
    # Create TF-IDF matrix
    vectorizer = TfidfVectorizer(min_df=1, stop_words='english')
    tfidf_matrix = vectorizer.fit_transform(texts)
    
    # Convert to array for easier manipulation
    feature_matrix = tfidf_matrix.toarray()
    
    # Extract query vector (first row) and corpus vectors
    query_vector = feature_matrix[0]
    corpus_vectors = feature_matrix[1:]
    
    # Compute cosine similarity
    similarities = []
    for i, vector in enumerate(corpus_vectors):
        # Cosine similarity calculation
        dot_product = np.dot(query_vector, vector)
        query_norm = np.linalg.norm(query_vector)
        vector_norm = np.linalg.norm(vector)
        
        # Avoid division by zero
        if query_norm == 0 or vector_norm == 0:
            similarity = 0
        else:
            similarity = dot_product / (query_norm * vector_norm)
        
        # Store original index and similarity
        similarities.append((similarity, indices[i]))
    
    # Sort by similarity (highest first)
    similarities.sort(reverse=True)
    
    return similarities

# Function to standardize SNR format
def standardize_snr(snr):
    if pd.isna(snr) or snr is None:
        return ''
    
    snr_str = str(snr).strip()
    
    # Remove spaces, hyphens, etc.
    snr_str = re.sub(r'[\s\-\.]', '', snr_str)
    
    # For toll-free numbers, keep the format
    if snr_str.startswith('0800'):
        return snr_str
    
    # Otherwise, remove leading zeros
    return snr_str.lstrip('0')

# Function to categorize SNRs based on characteristics
def categorize_snr(snr, snr_type):
    snr_str = str(snr)
    length = len(standardize_snr(snr_str))
    
    # Emergency and service codes (usually 3 digits)
    if length == 3:
        if snr_str in ['110', '111', '112', '191', '192', '193', '194', '198', '199']:
            return 'Emergency'
        elif snr_str in ['100', '101', '102', '103', '104', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']:
            return 'Service'
        else:
            return '3-Digit'
    
    # 4-digit codes
    elif length == 4:
        if snr_str.startswith('2'):
            return 'MNO Service'
        else:
            return '4-Digit'
    
    # 5-digit codes
    elif length == 5:
        return '5-Digit'
    
    # Toll-free numbers
    elif 'toll free' in str(snr_type).lower() or str(snr_str).startswith('0800') or str(snr_str).startswith('800'):
        return 'Toll-Free'
    
    # Default category
    return 'Other'

# Function to determine if a number is likely a harmonized code
def is_harmonized_code(snr, harmonized_df):
    std_snr = standardize_snr(snr)
    
    # Check exact matches
    exact_match = harmonized_df[harmonized_df['code'].apply(standardize_snr) == std_snr]
    if not exact_match.empty:
        return True
    
    # Check for toll-free numbers
    if std_snr.startswith('800') or std_snr.startswith('0800'):
        return True
    
    # Check for very short codes (3 digits)
    if len(std_snr) == 3:
        # Most 3-digit codes are harmonized/special purpose
        return True
    
    return False

# Function to check SNR against NCA database
def check_snr_in_database(snr, app_df):
    std_snr = standardize_snr(snr)
    
    # Check if this SNR exists in the app database
    matches = app_df[app_df['snr'].apply(lambda x: standardize_snr(x) == std_snr)]
    
    if matches.empty:
        return {
            'found': False,
            'applicant': 'N/A',
            'vasp': 'N/A',
            'auth_date': None,
            'at_check': False,
            'applications': []
        }
    
    # Get the most recent record
    latest = matches.iloc[-1]
    
    applications = []
    if latest.get('ussdcheck') == 'on':
        applications.append('USSD')
    if latest.get('smscheck') == 'on':
        applications.append('SMS')
    if latest.get('odacheck') == 'on':
        applications.append('Other Data Services')
    
    return {
        'found': True,
        'applicant': latest.get('applicant', 'N/A'),
        'vasp': latest.get('vasp', 'N/A'),
        'auth_date': latest.get('cert_date', None),
        'at_check': latest.get('atcheck') == 'on',  # Assuming atcheck field exists
        'applications': applications
    }

# Replace the compare_text_similarity function with this much faster version
def compare_text_similarity(user_text, db_texts, *args, **kwargs):
    """Drop-in replacement for the OpenAI-based similarity function"""
    # Ignore the openai_client parameter (represented by *args, **kwargs)
    return fast_text_similarity(user_text, db_texts)

# Add this function right before your analyze function
def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization"""
    import numpy as np
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_types(i) for i in obj]
    else:
        return obj
