{% extends "base.html" %}
{% block content %}

<div class="container mt-5">
    <h2>User and Admin Activity Timeline</h2>
    <div id="activity-visualization"></div>
</div>

<script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
<script>
    // Fetch activity logs from the server and visualize them
    document.addEventListener('DOMContentLoaded', function() {
        fetch('/activity_log_summary')
            .then(response => response.json())
            .then(data => {
                let trace = {
                    x: data.map(d => d.last_action),
                    y: data.map(d => `${d.user_id}: ${d.action}`),
                    mode: 'markers',
                    marker: { size: 10 },
                    type: 'scatter'
                };

                let layout = {
                    title: 'User and Admin Activities Over Time',
                    xaxis: {
                        title: 'Timestamp',
                        type: 'date'
                    },
                    yaxis: {
                        title: 'User and Action',
                        automargin: true
                    }
                };

                Plotly.newPlot('activity-visualization', [trace], layout);
            })
            .catch(error => console.error('Error fetching activity data:', error));
    });
</script>
{% endblock %}