import json
import re
import pandas as pd
from pymongo import MongoClient
import numpy as np
import os
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
from fuzzywuzzy import fuzz

# MongoDB connection with your credentials
def connect_to_mongodb():
    uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
    client = MongoClient(uri)
    return client

# Function to get data from MongoDB
def get_app_database():
    client = connect_to_mongodb()
    pull = client.get_database("captains_log")
    collection = pull.get_collection(name="app").find()
    app_df = pd.DataFrame(collection)
    print(f"Retrieved {len(app_df)} records from app database")
    return app_df


# Function to get application details for an SNR from app database
def appDbLookup(snr, app_df):
    try:
        # Standardize the SNR format
        std_snr = standardize_snr(snr)
        
        # Lookup in the DataFrame
        sub_dfound = app_df[app_df["snr"].apply(lambda x: standardize_snr(x) == std_snr)]
        if not sub_dfound.empty:
            latest_record = sub_dfound.iloc[-1]
            applicant_name = latest_record.get("applicant", "N/A")
            auth_date = pd.to_datetime(latest_record.get("cert_date", None)) if "cert_date" in latest_record else None
            odacheck = latest_record.get("odacheck", "")
            smscheck = latest_record.get("smscheck", "")
            ussdcheck = latest_record.get("ussdcheck", "")
            mtncheck = latest_record.get("mtncheck", "")
            vasp = latest_record.get("vasp", "")

            applications = []
            if ussdcheck == "on":
                applications.append("USSD")
            if smscheck == "on":
                applications.append("SMS")
            if odacheck == "on":
                applications.append("Other Data Services")
            if mtncheck == "on":
                applications.append("MTN")

            applications_str = ", ".join(applications)
            return {"applicant": applicant_name, "vasp": vasp, "auth_date": auth_date, "applications": applications_str}
        else:
            return {"applicant": "N/A", "vasp": "N/A", "auth_date": None, "applications": "N/A"}
    except Exception as e:
        print(f"Error looking up SNR {snr}: {e}")
        return {"applicant": "N/A", "vasp": "N/A", "auth_date": None, "applications": "N/A"}

# Define protected numbers and harmonized codes
def get_protected_numbers():
    # This is a sample list of protected numbers and harmonized codes
    # In practice, you should load this from a database or a curated list
    protected_numbers = [
        # Emergency services
        {"code": "112", "description": "Emergency Services", "category": "Emergency", "protected": True},
        {"code": "191", "description": "Police Emergency", "category": "Emergency", "protected": True},
        {"code": "192", "description": "Fire Service", "category": "Emergency", "protected": True},
        {"code": "193", "description": "Ambulance", "category": "Emergency", "protected": True},
        {"code": "194", "description": "Roadside Assistance", "category": "Emergency", "protected": True},
        {"code": "198", "description": "National Security", "category": "Emergency", "protected": True},
        {"code": "199", "description": "Emergency Services", "category": "Emergency", "protected": True},
        
        # Service codes (100-199)
        {"code": "100", "description": "Operator Services", "category": "Service", "protected": True},
        {"code": "101", "description": "Customer Service", "category": "Service", "protected": True},
        {"code": "102", "description": "Information", "category": "Service", "protected": True},
        {"code": "110", "description": "Time Announcement", "category": "Service", "protected": True},
        {"code": "111", "description": "Directory Enquiries", "category": "Service", "protected": True},
        {"code": "123", "description": "Voice Mail", "category": "Service", "protected": True},
        {"code": "124", "description": "Service Provider Pre-selection", "category": "Service", "protected": True},
        
        # Value Added Service Codes (200-899)
        {"code": "222", "description": "Mobile Money", "category": "Value Added", "protected": False},
        {"code": "300", "description": "Value Added", "category": "Value Added", "protected": False},
        {"code": "400", "description": "Value Added", "category": "Value Added", "protected": False},
        {"code": "500", "description": "Value Added", "category": "Value Added", "protected": False},
        
        # Short Message Service Codes
        {"code": "1000", "description": "SMS Service", "category": "SMS", "protected": False},
        {"code": "2000", "description": "SMS Service", "category": "SMS", "protected": False},
        
        # USSD Codes
        {"code": "*100#", "description": "Balance Enquiry", "category": "USSD", "protected": False},
        {"code": "*124#", "description": "Mobile Money", "category": "USSD", "protected": False},
        
        # Add all your protected numbers and harmonized codes here
    ]
    
    return pd.DataFrame(protected_numbers)

# Function to analyze the MTN SNR submission file
def analyze_mtn_snr_submission(file_path):
    # Read the Excel file
    mtn_df = pd.read_excel(file_path)
    
    # Rename columns to simpler names
    mtn_df.columns = [col.strip() for col in mtn_df.columns]
    
    # Basic statistics
    total_snrs = len(mtn_df)
    mtn_operated = len(mtn_df[mtn_df['USER(OPERATOR OR THIRD PARTY)'] == 'MTN'])
    third_party = total_snrs - mtn_operated
    
    # SNR types
    snr_types = mtn_df['TYPE (AUTOFILL)'].value_counts().to_dict()
    
    # Service types
    service_types = mtn_df['SERVICE DESCRIPTION'].value_counts().to_dict()
    
    # Status distribution
    status_dist = mtn_df['STATUS'].value_counts().to_dict()
    
    print(f"MTN SNR Analysis:")
    print(f"Total SNRs: {total_snrs}")
    print(f"MTN Operated: {mtn_operated} ({mtn_operated/total_snrs*100:.2f}%)")
    print(f"Third Party: {third_party} ({third_party/total_snrs*100:.2f}%)")
    print(f"SNR Types: {snr_types}")
    print(f"Service Types: {service_types}")
    print(f"Status Distribution: {status_dist}")
    
    return mtn_df



def is_name_similar(name1, name2, threshold=70):
    """
    Check if two organization names are similar using fuzzy matching
    
    Args:
        name1: First organization name
        name2: Second organization name
        threshold: Similarity threshold (0-100)
        
    Returns:
        bool: True if names are similar, False otherwise
    """
    if not name1 or not name2:
        return False
    
    # Convert to strings
    name1 = str(name1).lower().strip()
    name2 = str(name2).lower().strip()
    
    # Remove common words and characters that don't add meaning
    stopwords = ['limited', 'ltd', 'inc', 'incorporated', 'corporation', 'corp', 
                'company', 'co', 'llc', 'plc', 'group', 'holdings', 'ghana', 'services']
    
    for word in stopwords:
        name1 = re.sub(r'\b' + word + r'\b', '', name1)
        name2 = re.sub(r'\b' + word + r'\b', '', name2)
    
    # Clean up punctuation and extra spaces
    name1 = re.sub(r'[^\w\s]', ' ', name1)
    name2 = re.sub(r'[^\w\s]', ' ', name2)
    name1 = re.sub(r'\s+', ' ', name1).strip()
    name2 = re.sub(r'\s+', ' ', name2).strip()
    
    # If either name is now empty after cleaning
    if not name1 or not name2:
        return False
    
    # Special case for MTN
    if 'mtn' in name1 and 'mtn' in name2:
        return True
    
    # Use token set ratio for best partial matching regardless of word order
    ratio = fuzz.token_set_ratio(name1, name2)
    
    return ratio >= threshold

def check_name_match(mtn_user, db_applicant, db_vasp):
    """
    Check if MTN's user matches either the applicant or VASP in the database
    
    Args:
        mtn_user: User name from MTN submission
        db_applicant: Applicant name from database
        db_vasp: VASP name from database
        
    Returns:
        bool: True if there's a match, False otherwise
    """
    if not mtn_user:
        return False
    
    # Convert to strings and handle None values
    mtn_user = str(mtn_user) if mtn_user else ""
    db_applicant = str(db_applicant) if db_applicant and db_applicant != "N/A" else ""
    db_vasp = str(db_vasp) if db_vasp and db_vasp != "N/A" else ""
    
    # Check if MTN is the user in MTN's submission
    is_mtn_user = is_name_similar(mtn_user, "MTN")
    
    # Check if MTN is the applicant or VASP in the database
    is_mtn_in_db = is_name_similar(db_applicant, "MTN") or is_name_similar(db_vasp, "MTN")
    
    # If MTN is claiming it's theirs but database doesn't show MTN
    if is_mtn_user and not is_mtn_in_db and (db_applicant or db_vasp):
        return False
    
    # If MTN says it's for a third party but database shows MTN
    if not is_mtn_user and is_mtn_in_db:
        return False
    
    # If it's a third party, check if the names match
    if not is_mtn_user and not is_mtn_in_db:
        return is_name_similar(mtn_user, db_applicant) or is_name_similar(mtn_user, db_vasp)
    
    # Otherwise, no discrepancy
    return True






# Define fee structure
fees = {'3': 2100, '4': 500, '5': 200, '6': 150}

# Fee and description mappings
fee_description_mapping = [
    (range(100, 141), 2100, "{} Digit Number [Network Based Services]"),
    ([190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 116, 112, 100, 134, 124, 108, 109, 600, 400, 419], 0, "{} Digit Number [Harmonized Short Code]"),
    (range(180, 190), 2100, "{} Digit Number [Emergency and Security Based Services]"),
    (range(600, 700), 2100, "{} Digit Number [Reserved-MNP]"),
    (range(5000, 6001), 100, "{} Digit Number [Transactional Electronic Communications]"),
    (range(450, 601), 200, "{} Digit Number [Transactional Electronic Communications]"),
    (range(300, 371), 2100, "{} Digit Number [Reserved - 3GPP/TS22.030]")
]



def preprocess_snr(snr):
    """
    Clean and standardize SNR format.
    
    Args:
        snr: The SNR to preprocess
        
    Returns:
        str: Standardized SNR or None if invalid
    """
    if snr is None:
        return None
    
    # Convert to string and strip whitespace
    snr_str = str(snr).strip()
    
    # Remove all non-alphanumeric characters (spaces, dashes, dots, etc.)
    snr_str = re.sub(r'[^0-9a-zA-Z]', '', snr_str)
    
    # Convert to lowercase
    snr_str = snr_str.lower()
    
    # Handle toll-free and premium numbers
    if snr_str.startswith("0800") or snr_str.startswith("800"):
        return snr_str  # Keep leading zeros for toll-free numbers
    elif snr_str.startswith("0900") or snr_str.startswith("900"):
        return snr_str  # Keep leading zeros for premium numbers
    else:
        # For normal SNRs, remove leading zeros
        return snr_str.lstrip('0')
    
    return snr_str

def snr_length_finder(snr):
    """
    Determine SNR type based on length and format.
    
    Args:
        snr: The SNR to categorize
        
    Returns:
        str: SNR type category
    """
    snr = preprocess_snr(snr)  # Preprocess the SNR

    if snr is None or snr == "":
        return "Undefined"

    # Handle toll-free and premium numbers
    if (snr.startswith("0800") or snr.startswith("800")) and len(snr) >= 7:
        return "Tollfree"
    elif (snr.startswith("0900") or snr.startswith("900")) and len(snr) >= 7:
        return "Premium"
    
    # Try to convert to integer for regular SNRs
    try:
        snr_int = int(snr)
        
        # Categorize by length
        if len(snr) == 3 and 100 <= snr_int <= 999:
            return "3 Digit"
        elif len(snr) == 4 and 1000 <= snr_int <= 9999:
            return "4 Digit"
        elif len(snr) == 5 and 10000 <= snr_int <= 99999:
            return "5 Digit"
        elif len(snr) == 6 and 100000 <= snr_int <= 999999:
            return "6 Digit"
    except ValueError:
        # If conversion to int fails (e.g., contains non-numeric characters)
        pass
    
    return "Undefined"

# Updated standardize_snr function that uses preprocess_snr
def standardize_snr(snr):
    """
    Standardize SNR format for consistent comparison.
    
    Args:
        snr: The SNR to standardize
        
    Returns:
        str: Standardized SNR or empty string if invalid
    """
    return preprocess_snr(snr) or ""

# Updated determine_fee_description function 
def determine_fee_description(snr, fees, fee_description_mapping):
    """
    Determine the fee and description for an SNR.
    
    Args:
        snr: The SNR to process
        fees: Dictionary mapping SNR length to standard fee
        fee_description_mapping: List of mappings for special ranges and fees
        
    Returns:
        tuple: (fee amount, description)
    """
    # Preprocess the SNR
    clean_snr = preprocess_snr(snr)
    if not clean_snr:
        return 0, "Invalid SNR"
    
    # Get the SNR type
    snr_type = snr_length_finder(snr)
    
    # Try to convert to integer for comparison with ranges
    try:
        snr_int = int(clean_snr.lstrip("0"))  # Remove leading zeros
        
        # Check special ranges first
        for ranges, fee, description in fee_description_mapping:
            if isinstance(ranges, list):
                if snr_int in ranges:
                    return fee, description.format(len(clean_snr))
            elif snr_int in ranges:  # For range objects
                return fee, description.format(len(clean_snr))
    except ValueError:
        # If conversion to int fails, it might be a toll-free or premium number
        pass
    
    # Handle special cases based on SNR type
    if snr_type == "Tollfree":
        return 40, "Toll-Free Number"
    elif snr_type == "Premium":
        return 100, "Premium Rate Number"
    elif snr_type.endswith("Digit"):
        # Extract the digit number for the fee lookup
        digit = snr_type.split()[0]
        return fees.get(digit, 0), f"{digit} Digit Number"
    
    # Default case
    return 0, "Undefined Number"
# Updated function to match SNRs with protected numbers and check NCA database

def match_snrs_with_protected(mtn_df, app_df, protected_df):
    # Standardize SNRs in all dataframes
    mtn_df['SNR_Std'] = mtn_df['SNR'].apply(standardize_snr)
    protected_df['code_std'] = protected_df['code'].apply(standardize_snr)
    
    # Add SNR type to the MTN dataframe
    mtn_df['SNR_Type'] = mtn_df['SNR'].apply(snr_length_finder)
    
    # Create results list
    results = []
    
    # Process each SNR in MTN submission
    for _, row in mtn_df.iterrows():
        snr = row['SNR']
        snr_std = row['SNR_Std']
        
        # Use our determined SNR type if MTN's type is missing or undefined
        mtn_type = row['TYPE (AUTOFILL)'] if pd.notna(row['TYPE (AUTOFILL)']) else None
        detected_type = row['SNR_Type']
        
        # Choose the final SNR type to use
        if not mtn_type or mtn_type == "Undefined":
            snr_type = detected_type
        else:
            snr_type = mtn_type
            
        user = row['USER(OPERATOR OR THIRD PARTY)']
        service = row['SERVICE DESCRIPTION']
        status = row['STATUS']
        
        # Check if this is a protected/harmonized number
        protected_match = protected_df[protected_df['code_std'] == snr_std]
        is_protected = not protected_match.empty
        
        if is_protected:
            protected_info = protected_match.iloc[0]
            description = protected_info['description']
            category = protected_info['category']
            strictly_protected = protected_info['protected']
        else:
            # Determine description based on the SNR
            _, description = determine_fee_description(snr, fees, fee_description_mapping)
            category = "Regular"
            strictly_protected = False
            
            # Check if it's a harmonized code based on the description
            if "Harmonized Short Code" in description:
                category = "Harmonized"
                strictly_protected = True
        
        # Get data from NCA database
        db_info = appDbLookup(snr, app_df)
        
        # Determine if this is exclusive MTN or third party
        is_mtn = user == 'MTN'
        usage_type = "Exclusive" if is_mtn else "Third Party"
        
        # Get the base fee using the determine_fee_description function
        base_fee, _ = determine_fee_description(snr, fees, fee_description_mapping)
        
        # Use the base fee directly without adjustments for status
        price = base_fee
        
        # Special case for harmonized codes that are being used by third parties
        # If a harmonized code (base fee 0) is being used by anyone other than MTN,
        # charge the full 3-digit fee since this would be a violation
        if base_fee == 0 and not is_mtn and strictly_protected:
            price = fees.get('3', 2100)  # Use the 3-digit fee (typically 2100)
        
        # For harmonized codes used by MTN properly, keep the fee at 0
        if base_fee == 0 and is_mtn and strictly_protected:
            price = 0
        
        # Get database info
        db_applicant = db_info['applicant']
        db_vasp = db_info['vasp']
        db_auth_date = db_info['auth_date']
        db_applications = db_info['applications']
        
        # Simple discrepancy check
        has_discrepancy = False
        discrepancy_note = ""
        
        if db_applicant != "N/A" or db_vasp != "N/A":
            # Check if the MTN user matches the database records
            name_match = check_name_match(user, db_applicant, db_vasp)
            
            if not name_match:
                has_discrepancy = True
                if is_name_similar(user, "MTN"):
                    discrepancy_note = "MTN claims ownership but database shows different entity"
                else:
                    discrepancy_note = f"Listed as '{user}' but database shows different entity"
        
        # Add to results
        results.append({
            "SNR": snr,
            "SNR_Std": snr_std,
            "SNR_Type": detected_type,
            "Type": snr_type,
            "User": user,
            "Service": service,
            "Status": status,
            "Is_Protected": strictly_protected,
            "Description": description,
            "Category": category,
            "Usage_Type": usage_type,
            "DB_Applicant": db_applicant,
            "DB_VASP": db_vasp,
            "DB_Auth_Date": db_auth_date,
            "DB_Applications": db_applications,
            "Has_Discrepancy": has_discrepancy,
            "Discrepancy_Note": discrepancy_note,
            "Annual_Fee": price
        })
    
    return pd.DataFrame(results)

# Function to generate the SNR invoice Excel file
def generate_snr_invoice(results_df, output_file="MTN_SNR_Invoice_2024.xlsx"):
    # Create workbook
    wb = Workbook()
    
    # Remove default sheet
    default_sheet = wb.active
    wb.remove(default_sheet)
    
    # Define styles
    header_fill = PatternFill(start_color="1F4E78", end_color="1F4E78", fill_type="solid")
    subheader_fill = PatternFill(start_color="DCE6F1", end_color="DCE6F1", fill_type="solid")
    total_fill = PatternFill(start_color="BDD7EE", end_color="BDD7EE", fill_type="solid")
    discrepancy_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
    
    header_font = Font(name="Arial", size=11, bold=True, color="FFFFFF")
    subheader_font = Font(name="Arial", size=11, bold=True)
    data_font = Font(name="Arial", size=10)
    total_font = Font(name="Arial", size=10, bold=True)
    
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # Create Invoice Summary sheet
    ws_summary = wb.create_sheet("Invoice Summary")
    
    # Add title
    ws_summary['A1'] = "MTN SPECIAL NUMBERING RESOURCES (SNR) INVOICE 2024"
    ws_summary['A2'] = f"Generated on: {datetime.now().strftime('%Y-%m-%d')}"
    
    for cell in ['A1', 'A2']:
        ws_summary[cell].font = Font(size=14, bold=True)
    
    # Create summary table
    ws_summary['A4'] = "SUMMARY"
    ws_summary['A4'].font = subheader_font
    
    # Add summary headers
    summary_headers = ["Category", "Count", "Total Fee (GHC)"]
    for i, header in enumerate(summary_headers):
        cell = ws_summary.cell(row=5, column=i+1)
        cell.value = header
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center')
        cell.border = border
    
    # Calculate summary data
    protected_count = len(results_df[results_df['Is_Protected'] == True])
    protected_fee = results_df[results_df['Is_Protected'] == True]['Annual_Fee'].sum()
    
    regular_count = len(results_df[results_df['Is_Protected'] == False])
    regular_fee = results_df[results_df['Is_Protected'] == False]['Annual_Fee'].sum()
    
    active_count = len(results_df[results_df['Status'] == 'ACTIVE'])
    active_fee = results_df[results_df['Status'] == 'ACTIVE']['Annual_Fee'].sum()
    
    inactive_count = len(results_df[results_df['Status'] == 'INACTIVE'])
    inactive_fee = results_df[results_df['Status'] == 'INACTIVE']['Annual_Fee'].sum()
    
    mtn_count = len(results_df[results_df['Usage_Type'] == 'Exclusive'])
    mtn_fee = results_df[results_df['Usage_Type'] == 'Exclusive']['Annual_Fee'].sum()
    
    third_party_count = len(results_df[results_df['Usage_Type'] == 'Third Party'])
    third_party_fee = results_df[results_df['Usage_Type'] == 'Third Party']['Annual_Fee'].sum()
    
    total_count = len(results_df)
    total_fee = results_df['Annual_Fee'].sum()
    
    # Add summary data
    summary_data = [
        ["Protected Numbers", protected_count, protected_fee],
        ["Regular Numbers", regular_count, regular_fee],
        ["Status: Active", active_count, active_fee],
        ["Status: Inactive", inactive_count, inactive_fee],
        ["Usage: MTN Exclusive", mtn_count, mtn_fee],
        ["Usage: Third Party", third_party_count, third_party_fee],
        ["TOTAL", total_count, total_fee]
    ]

    # When writing JSON files
    summary_data_converted = convert_numpy_types(summary_data)
    with open(output_file, 'w') as f:
        json.dump(summary_data_converted, f, indent=4)

        
    for i, row_data in enumerate(summary_data):
        row_num = i + 6
        is_total = i == len(summary_data) - 1
        
        for j, value in enumerate(row_data):
            cell = ws_summary.cell(row=row_num, column=j+1)
            
            if j == 0:  # Text column
                cell.value = value
                cell.alignment = Alignment(horizontal='left')
            elif j == 1:  # Count column
                cell.value = value
                cell.number_format = '#,##0'
                cell.alignment = Alignment(horizontal='right')
            else:  # Money column
                cell.value = value
                cell.number_format = 'GHC#,##0.00'
                cell.alignment = Alignment(horizontal='right')
            
            if is_total:
                cell.fill = total_fill
                cell.font = total_font
            else:
                cell.font = data_font
            
            cell.border = border
    
    # Add breakdown by type
    ws_summary['A10'] = "BREAKDOWN BY SNR TYPE"
    ws_summary['A10'].font = subheader_font
    
    # Add type breakdown headers
    type_headers = ["SNR Type", "Count", "Total Fee (GHC)"]
    for i, header in enumerate(type_headers):
        cell = ws_summary.cell(row=11, column=i+1)
        cell.value = header
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center')
        cell.border = border
    
    # Calculate type breakdown
    type_groups = results_df.groupby('Type')
    type_summary = []
    
    for snr_type, group in type_groups:
        if pd.notna(snr_type):  # Handle NaN types
            type_summary.append([
                snr_type,
                len(group),
                group['Annual_Fee'].sum()
            ])
    
    # Add type totals
    type_summary.append([
        "TOTAL",
        sum(row[1] for row in type_summary),
        sum(row[2] for row in type_summary)
    ])
    
    # Add type data
    for i, row_data in enumerate(type_summary):
        row_num = i + 12
        is_total = i == len(type_summary) - 1
        
        for j, value in enumerate(row_data):
            cell = ws_summary.cell(row=row_num, column=j+1)
            
            if j == 0:  # Text column
                cell.value = value
                cell.alignment = Alignment(horizontal='left')
            elif j == 1:  # Count column
                cell.value = value
                cell.number_format = '#,##0'
                cell.alignment = Alignment(horizontal='right')
            else:  # Money column
                cell.value = value
                cell.number_format = 'GHC#,##0.00'
                cell.alignment = Alignment(horizontal='right')
            
            if is_total:
                cell.fill = total_fill
                cell.font = total_font
            else:
                cell.font = data_font
            
            cell.border = border
    
    # Add note about discrepancies
    discrepancy_count = len(results_df[results_df['Has_Discrepancy'] == True])
    row_num = len(type_summary) + 14
    
    ws_summary[f'A{row_num}'] = "NOTES:"
    ws_summary[f'A{row_num}'].font = subheader_font
    
    ws_summary[f'A{row_num+1}'] = f"* {discrepancy_count} SNRs have discrepancies between MTN submission and NCA database."
    ws_summary[f'A{row_num+2}'] = "* See 'Discrepancies' sheet for details."
    ws_summary[f'A{row_num+3}'] = "* Protected numbers are charged at a premium rate."
    ws_summary[f'A{row_num+4}'] = "* Inactive numbers are charged at 50% of the active rate."
    
    # Set column widths
    column_widths = [30, 15, 20]
    for i, width in enumerate(column_widths):
        ws_summary.column_dimensions[get_column_letter(i+1)].width = width
    
    # Create Detailed Invoice sheet
    ws_detail = wb.create_sheet("Detailed Invoice")
    
    # Add title
    ws_detail['A1'] = "MTN SPECIAL NUMBERING RESOURCES (SNR) DETAILED INVOICE 2024"
    ws_detail['A1'].font = Font(size=14, bold=True)
    
    # Create detailed table headers
    detail_headers = ["SNR", "Type", "User", "Service", "Status", "Category", "Protected", "Annual Fee (GHC)"]
    for i, header in enumerate(detail_headers):
        cell = ws_detail.cell(row=3, column=i+1)
        cell.value = header
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center')
        cell.border = border
    
    # Add detailed data
    for i, (_, row) in enumerate(results_df.iterrows()):
        row_num = i + 4
        
        # Columns to display
        columns = [
            'SNR', 'Type', 'User', 'Service', 'Status', 'Category', 'Is_Protected', 'Annual_Fee'
        ]
        
        for j, col in enumerate(columns):
            cell = ws_detail.cell(row=row_num, column=j+1)
            
            # Format based on column type
            if col == 'Annual_Fee':
                cell.value = row[col]
                cell.number_format = 'GHC#,##0.00'
                cell.alignment = Alignment(horizontal='right')
            elif col == 'Is_Protected':
                cell.value = "Yes" if row[col] else "No"
                cell.alignment = Alignment(horizontal='center')
            else:
                cell.value = row[col]
                cell.alignment = Alignment(horizontal='left')
            
            # Apply styling
            cell.font = data_font
            cell.border = border
            
            # Highlight discrepancies
            if row['Has_Discrepancy']:
                cell.fill = discrepancy_fill
    
    # Add total row
    total_row = i + 5
    
    ws_detail.cell(row=total_row, column=1).value = "TOTAL"
    ws_detail.cell(row=total_row, column=1).font = total_font
    ws_detail.cell(row=total_row, column=1).fill = total_fill
    ws_detail.cell(row=total_row, column=1).border = border
    
    # Span total across multiple columns
    for j in range(2, 8):
        cell = ws_detail.cell(row=total_row, column=j)
        cell.value = ""
        cell.fill = total_fill
        cell.border = border
    
    # Add total fee
    cell = ws_detail.cell(row=total_row, column=8)
    cell.value = results_df['Annual_Fee'].sum()
    cell.number_format = 'GHC#,##0.00'
    cell.alignment = Alignment(horizontal='right')
    cell.font = total_font
    cell.fill = total_fill
    cell.border = border
    
    # Set column widths
    column_widths = [15, 20, 25, 20, 15, 20, 15, 20]
    for i, width in enumerate(column_widths):
        ws_detail.column_dimensions[get_column_letter(i+1)].width = width
    
    # Create Discrepancies sheet if needed
    if discrepancy_count > 0:
        ws_disc = wb.create_sheet("Discrepancies")
        
        # Add title
        ws_disc['A1'] = "MTN SNR SUBMISSION DISCREPANCIES"
        ws_disc['A1'].font = Font(size=14, bold=True)
        
        # Add description
        ws_disc['A2'] = "The following SNRs have discrepancies between MTN's submission and NCA database records."
        
        # Create discrepancies table headers
        disc_headers = ["SNR", "Type", "MTN User", "NCA Applicant", "NCA VASP", "Status", "Discrepancy Note"]
        for i, header in enumerate(disc_headers):
            cell = ws_disc.cell(row=4, column=i+1)
            cell.value = header
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal='center')
            cell.border = border
        
        # Filter for discrepancies
        disc_df = results_df[results_df['Has_Discrepancy'] == True]
        
        # Add discrepancy data
        for i, (_, row) in enumerate(disc_df.iterrows()):
            row_num = i + 5
            
            # Columns to display
            columns = [
                'SNR', 'Type', 'User', 'DB_Applicant', 'DB_VASP', 'Status', 'Discrepancy_Note'
            ]
            
            for j, col in enumerate(columns):
                cell = ws_disc.cell(row=row_num, column=j+1)
                cell.value = row[col]
                cell.alignment = Alignment(horizontal='left')
                cell.font = data_font
                cell.border = border
                cell.fill = discrepancy_fill
        
        # Set column widths
        column_widths = [15, 20, 25, 25, 25, 15, 40]
        for i, width in enumerate(column_widths):
            ws_disc.column_dimensions[get_column_letter(i+1)].width = width
    
    # Save workbook
    wb.save(output_file)
    print(f"SNR Invoice saved to {output_file}")
    
    return output_file

# Main function to process MTN SNR submission and generate invoice
def process_mtn_snr_invoice(mtn_submission_file):
    print(f"Processing MTN SNR submission: {mtn_submission_file}")
    
    # Get data from NCA database
    app_df = get_app_database()
    
    # Get list of protected numbers
    protected_df = get_protected_numbers()
    
    # Analyze MTN submission
    mtn_df = analyze_mtn_snr_submission(mtn_submission_file)
    
    # Match SNRs with protected numbers and NCA database
    results_df = match_snrs_with_protected(mtn_df, app_df, protected_df)
    
    # Generate invoice
    output_file = generate_snr_invoice(results_df)
    
    print(f"Processing complete. Invoice saved to {output_file}")
    return output_file


def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization"""
    import numpy as np
    
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_types(i) for i in obj]
    else:
        return obj


if __name__ == "__main__":
    print("MTN Special Numbering Resources (SNR) Invoice Generator")
    print("------------------------------------------------------")
    
    # You can hard-code the file path or take it as input
    mtn_submission_file = input("Enter path to MTN SNR 2024 EXTRACT.xlsx: ")
    
    if not mtn_submission_file:
        mtn_submission_file = "MTN SNR 2024 EXTRACT.xlsx"
    
    output_file = process_mtn_snr_invoice(mtn_submission_file)
    print(f"Done! Invoice saved to {output_file}")
