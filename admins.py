from werkzeug.security import generate_password_hash
from pymongo import MongoClient
import datetime


# Connect to MongoDB
client = MongoClient("mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0")
db = client['new_numbering']

# Create admin user
username = input("Enter admin username: ")
email = input("Enter admin email: ")
password = input("Enter admin password: ")

hashed_password = generate_password_hash(password, method='scrypt')
new_admin = {
    "contact": username,
    "email": email,
    "password": hashed_password,
    "is_admin": True,
    "created_at": datetime.datetime.utcnow()
}

# Insert into the database
db.users.insert_one(new_admin)
print(f"Admin user '{username}' created successfully.")
