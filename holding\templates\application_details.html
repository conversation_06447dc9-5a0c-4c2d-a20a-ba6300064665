<!-- templates/application_details.html -->
{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-10 col-lg-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Application Details</h2>
                    <p class="text-muted text-center">
                        Detailed information for application {{ application.application_id }}
                    </p>

                    <!-- Application General Information -->
                    <div class="mb-4">
                        <h4>Applicant Information</h4>
                        <p><strong>Applicant Name:</strong> {{ application.applicant_name | default('N/A') }}</p>
                        <p><strong>Email:</strong> {{ application.email | default('N/A') }}</p>
                        <p><strong>Phone:</strong> {{ application.phone | default('N/A') }}</p>
                        <p><strong>Company:</strong> {{ application.company | default('N/A') }}</p>
                        <p><strong>Application Date:</strong> {{ application.application_date | default('N/A') }}</p>
                        <p><strong>Status:</strong> 
                            <span class="badge 
                                {% if application.status == 'Approved' %}badge-success
                                {% elif application.status == 'Pending Review' %}badge-warning
                                {% else %}badge-secondary{% endif %}">
                                {{ application.status | default('N/A') }}
                            </span>
                        </p>
                    </div>



                    <!-- SNR Details -->
                    <h4>Number Details</h4>
                    {% for snr in application.snr_details %}
                    <div class="card mb-4">
                        <div class="card-body">
                            <h5>Number {{ loop.index }}</h5>
                            <p><strong>SNR:</strong> {{ snr.snr | default('N/A') }}</p>
                            <p><strong>Type:</strong> {{ snr.type | default('N/A') }}</p>
                            <p><strong>Purpose:</strong> {{ snr.purpose | default('N/A') }}</p>
                            <p><strong>Status:</strong> 
                                <span class="badge 
                                    {% if snr.status == 'Approved' %}badge-success
                                    {% elif snr.status == 'Pending Review' %}badge-warning
                                    {% else %}badge-secondary{% endif %}">
                                    {{ snr.status | default('N/A') }}
                                </span>
                            </p>
                            <p><strong>Certificate ID:</strong> {{ snr.certificate_id | default('N/A') }}</p>
                        </div>
                    </div>
                    {% endfor %}

                    <!-- Remarks Section -->
                    <div class="mt-4">
                        <h5>Remarks</h5>
                        <div class="card">
                            <div class="card-body">
                                {{ application.remarks | default('No remarks available') }}
                            </div>
                        </div>
                    </div>

                    <div class="mt-4 text-center">
                        {% if current_user.is_admin %}
                        <a href="{{ url_for('review_applications') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Admin Dashboard
                        </a>
                        {% else %}
                        <a href="{{ url_for('dashboard') }}" class="btn btn-primary">
                            <i class="fas fa-arrow-left"></i> Back to Dashboard
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}