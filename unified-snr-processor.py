#!/usr/bin/env python3
"""
Unified SNR Processor for AT and Telecel
Repurposed from harmonized-code-analyzer.py and mtn-snr-invoice-generator.py
Analyzes SNR submissions and generates invoices for AT and Telecel operators for 2024
"""

import pandas as pd
import numpy as np
from pymongo import MongoClient
import os
import re
from datetime import datetime
from openpyxl import Workbook
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter
import json

def convert_numpy_types(obj):
    """Convert numpy types to native Python types for JSON serialization"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {k: convert_numpy_types(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [convert_numpy_types(i) for i in obj]
    else:
        return obj

# MongoDB connection
def connect_to_mongodb():
    uri = "mongodb+srv://GaudKing_Chapper:<EMAIL>/captains_log?retryWrites=true&w=majority"
    client = MongoClient(uri)
    return client

# Function to retrieve app data from MongoDB
def get_app_data():
    client = connect_to_mongodb()
    db = client.get_database("captains_log")
    collection = db.get_collection(name="app")
    data = list(collection.find())
    app_df = pd.DataFrame(data)
    return app_df

# Fee structure for SNR audit
fees = {
    '3': 2100,  # 3-digit codes
    '4': 500,   # 4-digit codes
    '5': 200,   # 5-digit codes
    '6': 150    # 6-digit codes
}

def standardize_snr(snr):
    """Standardize SNR format for consistent processing"""
    if pd.isna(snr) or snr is None:
        return ''

    snr_str = str(snr).strip()
    snr_str = re.sub(r'[\s\-\.]', '', snr_str)

    if snr_str.startswith('0800'):
        return snr_str

    return snr_str.lstrip('0')

def categorize_snr(snr, snr_type):
    """Categorize SNRs based on characteristics"""
    snr_str = str(snr)
    length = len(standardize_snr(snr_str))

    if length == 3:
        if snr_str in ['110', '111', '112', '191', '192', '193', '194', '198', '199']:
            return 'Emergency'
        elif snr_str in ['100', '101', '102', '103', '104', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']:
            return 'Service'
        else:
            return '3-Digit'
    elif length == 4:
        if snr_str.startswith('2'):
            return 'MNO Service'
        else:
            return '4-Digit'
    elif length == 5:
        return '5-Digit'
    elif 'toll free' in str(snr_type).lower() or str(snr_str).startswith('0800') or str(snr_str).startswith('800'):
        return 'Toll-Free'

    return 'Other'

def appDbLookup(snr, app_df, operator_name):
    """Look up SNR in the application database"""
    std_snr = standardize_snr(snr)

    matches = app_df[app_df['snr'].apply(lambda x: standardize_snr(x) == std_snr)]

    if matches.empty:
        return {
            'applicant': 'Unknown',
            'vasp': 'Unknown',
            'auth_date': None,
            'operator_check': False,
            'applications': []
        }

    latest = matches.iloc[-1]

    applications = []
    if latest.get('ussdcheck') == 'on':
        applications.append('USSD')
    if latest.get('smscheck') == 'on':
        applications.append('SMS')
    if latest.get('odacheck') == 'on':
        applications.append('Other Data Services')

    # Check operator-specific authorization
    operator_check = False
    if operator_name.lower() == 'at':
        operator_check = latest.get('aircheckcheck') == 'on'
    elif operator_name.lower() == 'telecel':
        operator_check = latest.get('vodacheck') == 'on'

    return {
        'applicant': latest.get('applicant', 'Unknown'),
        'vasp': latest.get('vasp', 'Unknown'),
        'auth_date': latest.get('cert_date', None),
        'operator_check': operator_check,
        'applications': applications
    }

def determine_fee_and_description(snr, snr_type, db_info):
    """Determine the appropriate fee and description for an SNR"""
    std_snr = standardize_snr(snr)
    length = len(std_snr)

    # Special cases for toll-free and premium numbers
    if 'toll free' in str(snr_type).lower() or std_snr.startswith('0800') or std_snr.startswith('800'):
        return 0, "Toll-Free Number", "Toll-Free"

    if 'premium' in str(snr_type).lower() or std_snr.startswith('0900') or std_snr.startswith('900'):
        return fees.get('4', 500), "Premium Rate Service", "Premium"

    # Harmonized short codes (usually 3 digits) - free
    if length == 3:
        if std_snr in ['110', '111', '112', '191', '192', '193', '194', '198', '199']:
            return 0, "Emergency Service (Harmonized)", "Emergency"
        elif std_snr in ['100', '101', '102', '103', '104', '120', '121', '122', '123', '124', '125', '126', '127', '128', '129']:
            return 0, "Service Code (Harmonized)", "Service"
        else:
            return fees.get('3', 2100), "3-Digit Short Code", "3-Digit"
    elif length == 4:
        if std_snr.startswith('2'):
            return fees.get('4', 500), "Mobile Network Operator Service", "MNO Service"
        else:
            return fees.get('4', 500), "4-Digit Short Code", "4-Digit"
    elif length == 5:
        return fees.get('5', 200), "5-Digit Short Code", "5-Digit"
    elif length == 6:
        return fees.get('6', 150), "6-Digit Short Code", "6-Digit"
    else:
        return fees.get('4', 500), f"{length}-Digit Code", "Other"

def auto_detect_columns(df):
    """Auto-detect important columns in the DataFrame"""
    columns = {
        'snr': 'SNR',
        'type': None,
        'user': None,
        'service': None,
        'status': None
    }

    # Find type column
    for col in df.columns:
        if 'TYPE' in col.upper():
            columns['type'] = col
            break

    # Find user column
    possible_user_columns = [
        'USER(OPERATOR OR THIRD PARTY)',
        'USER',
        'OPERATOR',
        'THIRD PARTY',
        'USER/OPERATOR',
        'OPERATOR/THIRD PARTY'
    ]

    for col in possible_user_columns:
        if col in df.columns:
            columns['user'] = col
            break

    # Find service column
    for col in df.columns:
        if 'SERVICE' in col.upper() or 'DESCRIPTION' in col.upper():
            columns['service'] = col
            break

    # Find status column
    for col in df.columns:
        if 'STATUS' in col.upper():
            columns['status'] = col
            break

    return columns

def process_operator_snr_data(file_path, operator_name, app_df):
    """Process operator SNR data and calculate fees"""
    print(f"Processing {operator_name} SNR data from: {file_path}")

    # Read the Excel file
    operator_df = pd.read_excel(file_path)

    # Clean column names
    operator_df.columns = [col.strip() for col in operator_df.columns]

    # Auto-detect columns
    columns = auto_detect_columns(operator_df)

    print(f"Detected columns for {operator_name}:")
    for key, value in columns.items():
        print(f"  {key}: {value}")

    # Process each SNR
    processed_data = []

    for _, row in operator_df.iterrows():
        snr = row[columns['snr']]
        snr_type = row[columns['type']] if columns['type'] else None
        user = row[columns['user']] if columns['user'] else operator_name
        service = row[columns['service']] if columns['service'] else 'Not specified'
        status = row[columns['status']] if columns['status'] else 'ACTIVE'

        # Look up in database
        db_info = appDbLookup(snr, app_df, operator_name)

        # Determine fee and description
        fee, description, category = determine_fee_and_description(snr, snr_type, db_info)

        processed_data.append({
            'SNR': snr,
            'Type': snr_type,
            'User': user,
            'Service': service,
            'Status': status,
            'Fee_GHS': fee,
            'Description': description,
            'Category': category,
            'DB_Applicant': db_info['applicant'],
            'DB_VASP': db_info['vasp'],
            'DB_Applications': ', '.join(db_info['applications']) if db_info['applications'] else 'None',
            'Operator_Authorized': db_info['operator_check']
        })

    return pd.DataFrame(processed_data)

def generate_operator_invoice(processed_df, operator_name, output_path):
    """Generate invoice Excel file for operator SNR audit"""
    print(f"Generating {operator_name} invoice...")

    # Create output directory if it doesn't exist
    os.makedirs(output_path, exist_ok=True)

    # Calculate totals
    total_snrs = len(processed_df)
    total_fee = processed_df['Fee_GHS'].sum()

    # Group by category for summary
    category_summary = processed_df.groupby('Category').agg({
        'SNR': 'count',
        'Fee_GHS': 'sum'
    }).reset_index()
    category_summary.columns = ['Category', 'Count', 'Total_Fee_GHS']

    # Create Excel workbook
    wb = Workbook()

    # Invoice Summary Sheet
    ws_summary = wb.active
    ws_summary.title = "Invoice Summary"

    # Add header information
    ws_summary['A1'] = f"{operator_name.upper()} SNR AUDIT INVOICE - 2024"
    ws_summary['A1'].font = Font(bold=True, size=16)
    ws_summary['A3'] = f"Invoice Date: {datetime.now().strftime('%Y-%m-%d')}"
    ws_summary['A4'] = f"Total SNRs: {total_snrs}"
    ws_summary['A5'] = f"Total Fee: GHS {total_fee:,.2f}"

    # Add category breakdown
    ws_summary['A7'] = "Category Breakdown:"
    ws_summary['A7'].font = Font(bold=True)

    # Headers for category table
    headers = ['Category', 'Count', 'Fee per Unit (GHS)', 'Total Fee (GHS)']
    for col, header in enumerate(headers, 1):
        cell = ws_summary.cell(row=8, column=col, value=header)
        cell.font = Font(bold=True)

    # Add category data
    for idx, row in category_summary.iterrows():
        ws_summary.cell(row=9+idx, column=1, value=row['Category'])
        ws_summary.cell(row=9+idx, column=2, value=row['Count'])

        # Calculate fee per unit
        if row['Count'] > 0:
            fee_per_unit = row['Total_Fee_GHS'] / row['Count']
        else:
            fee_per_unit = 0

        ws_summary.cell(row=9+idx, column=3, value=f"{fee_per_unit:.2f}")
        ws_summary.cell(row=9+idx, column=4, value=f"{row['Total_Fee_GHS']:.2f}")

    # Detailed SNR List Sheet
    ws_detail = wb.create_sheet("Detailed SNR List")

    # Add headers
    detail_headers = ['SNR', 'Type', 'User', 'Service', 'Status', 'Category', 'Fee (GHS)', 'Description', 'DB_Applicant', 'DB_VASP', 'Operator_Authorized']
    for col, header in enumerate(detail_headers, 1):
        cell = ws_detail.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True)

    # Add data
    for idx, row in processed_df.iterrows():
        ws_detail.cell(row=idx+2, column=1, value=row['SNR'])
        ws_detail.cell(row=idx+2, column=2, value=row['Type'])
        ws_detail.cell(row=idx+2, column=3, value=row['User'])
        ws_detail.cell(row=idx+2, column=4, value=row['Service'])
        ws_detail.cell(row=idx+2, column=5, value=row['Status'])
        ws_detail.cell(row=idx+2, column=6, value=row['Category'])
        ws_detail.cell(row=idx+2, column=7, value=row['Fee_GHS'])
        ws_detail.cell(row=idx+2, column=8, value=row['Description'])
        ws_detail.cell(row=idx+2, column=9, value=row['DB_Applicant'])
        ws_detail.cell(row=idx+2, column=10, value=row['DB_VASP'])
        ws_detail.cell(row=idx+2, column=11, value=row['Operator_Authorized'])

    # Auto-adjust column widths
    for ws in [ws_summary, ws_detail]:
        for column in ws.columns:
            max_length = 0
            column_letter = get_column_letter(column[0].column)
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook
    output_file = os.path.join(output_path, f"{operator_name}_SNR_Invoice_2024.xlsx")
    wb.save(output_file)

    print(f"Invoice generated successfully: {output_file}")
    print(f"Total SNRs: {total_snrs}")
    print(f"Total Fee: GHS {total_fee:,.2f}")

    return output_file, total_fee, category_summary

def generate_analysis_report(processed_df, operator_name, output_path):
    """Generate detailed analysis report"""

    # Calculate statistics
    total_snrs = len(processed_df)
    total_fee = processed_df['Fee_GHS'].sum()
    harmonized_count = len(processed_df[processed_df['Category'].isin(['Emergency', 'Service'])])
    db_matches = len(processed_df[processed_df['DB_Applicant'] != 'Unknown'])
    operator_authorized = len(processed_df[processed_df['Operator_Authorized'] == True])

    # Create analysis summary
    analysis_summary = {
        'operator': operator_name,
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'total_snrs': total_snrs,
        'total_fee_ghs': float(total_fee),
        'harmonized_codes': harmonized_count,
        'harmonized_percentage': (harmonized_count / total_snrs * 100) if total_snrs > 0 else 0,
        'database_matches': db_matches,
        'database_match_percentage': (db_matches / total_snrs * 100) if total_snrs > 0 else 0,
        'operator_authorized': operator_authorized,
        'operator_authorized_percentage': (operator_authorized / total_snrs * 100) if total_snrs > 0 else 0,
        'category_breakdown': processed_df['Category'].value_counts().to_dict(),
        'fee_breakdown': processed_df.groupby('Category')['Fee_GHS'].sum().to_dict()
    }

    # Save analysis as JSON
    analysis_file = os.path.join(output_path, f"{operator_name}_SNR_Analysis_2024.json")
    with open(analysis_file, 'w') as f:
        json.dump(convert_numpy_types(analysis_summary), f, indent=2)

    # Save detailed data as CSV
    csv_file = os.path.join(output_path, f"{operator_name}_SNR_Detailed_Data_2024.csv")
    processed_df.to_csv(csv_file, index=False)

    return analysis_file, csv_file, analysis_summary

def process_operator(operator_name, file_path, app_df):
    """Process a single operator's SNR data"""
    print(f"\n{'='*50}")
    print(f"PROCESSING {operator_name.upper()} SNR DATA")
    print(f"{'='*50}")

    if not os.path.exists(file_path):
        print(f"Error: File '{file_path}' not found!")
        return None

    try:
        # Process SNR data
        processed_df = process_operator_snr_data(file_path, operator_name, app_df)

        # Create output directory
        output_dir = f"{operator_name}_SNR_Output_2024"

        # Generate invoice
        invoice_file, total_fee, category_summary = generate_operator_invoice(processed_df, operator_name, output_dir)

        # Generate analysis report
        analysis_file, csv_file, analysis_summary = generate_analysis_report(processed_df, operator_name, output_dir)

        # Print summary
        print(f"\n{operator_name.upper()} PROCESSING COMPLETE:")
        print(f"  Total SNRs: {len(processed_df)}")
        print(f"  Total Fee: GHS {total_fee:,.2f}")
        print(f"  Invoice: {invoice_file}")
        print(f"  Analysis: {analysis_file}")
        print(f"  Detailed Data: {csv_file}")

        print(f"\n{operator_name.upper()} Category Breakdown:")
        for _, row in category_summary.iterrows():
            print(f"  {row['Category']}: {row['Count']} SNRs, GHS {row['Total_Fee_GHS']:,.2f}")

        return {
            'operator': operator_name,
            'processed_df': processed_df,
            'total_fee': total_fee,
            'invoice_file': invoice_file,
            'analysis_file': analysis_file,
            'csv_file': csv_file,
            'analysis_summary': analysis_summary
        }

    except Exception as e:
        print(f"Error processing {operator_name} data: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """Main function to process both AT and Telecel SNR data"""
    print("="*60)
    print("UNIFIED SNR PROCESSOR FOR AT AND TELECEL - 2024")
    print("="*60)

    # File paths
    operators = {
        'AT': 'AT_SNR_Invoice_2024.xlsx',
        'Telecel': 'Telecel_SNR_Invoice_2024.xlsx'
    }

    # Load application database
    print("Loading NCA application database...")
    try:
        app_df = get_app_data()
        print(f"Loaded {len(app_df)} records from database")
    except Exception as e:
        print(f"Error loading database: {str(e)}")
        return

    # Process each operator
    results = {}
    total_combined_fee = 0

    for operator_name, file_path in operators.items():
        result = process_operator(operator_name, file_path, app_df)
        if result:
            results[operator_name] = result
            total_combined_fee += result['total_fee']

    # Generate combined summary
    if results:
        print(f"\n{'='*60}")
        print("COMBINED SUMMARY")
        print(f"{'='*60}")

        combined_summary = {
            'processing_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'operators_processed': list(results.keys()),
            'total_combined_fee_ghs': total_combined_fee,
            'operator_details': {}
        }

        for operator_name, result in results.items():
            combined_summary['operator_details'][operator_name] = {
                'total_snrs': len(result['processed_df']),
                'total_fee_ghs': result['total_fee'],
                'files_generated': {
                    'invoice': result['invoice_file'],
                    'analysis': result['analysis_file'],
                    'detailed_data': result['csv_file']
                }
            }

            print(f"{operator_name}:")
            print(f"  Total SNRs: {len(result['processed_df'])}")
            print(f"  Total Fee: GHS {result['total_fee']:,.2f}")

        print(f"\nCOMBINED TOTAL FEE: GHS {total_combined_fee:,.2f}")

        # Save combined summary
        with open('Combined_SNR_Processing_Summary_2024.json', 'w') as f:
            json.dump(convert_numpy_types(combined_summary), f, indent=2)

        print(f"\nCombined summary saved to: Combined_SNR_Processing_Summary_2024.json")

    else:
        print("No operators were successfully processed.")

if __name__ == "__main__":
    main()
