<!-- <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Report Generation</title>
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap4.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
</head>
<body> -->
{% extends "base.html" %}
{% block content %}

    <div class="container mt-5">
        <h2>Generate Reports</h2>

        <div class="card mt-4 p-4">
            <h4>Natural Language Report Request</h4>
            <textarea id="report-description" class="form-control mb-3" rows="4" placeholder="Describe the report you need, e.g., 'Monthly shortcode usage report for Q1 2023'"></textarea>
            <button id="generate-nl-report" class="btn btn-primary">Generate Report</button>
        </div>

        <div class="card mt-4 p-4">
            <h4>Structured Report Request</h4>
            <form id="static-report-form">
                <div class="form-row">
                    <div class="form-group col-md-4">
                        <label for="report-type">Report Type</label>
                        <select id="report-type" class="form-control">
                            <option value="monthly">Monthly</option>
                            <option value="quarterly">Quarterly</option>
                            <option value="half-year">Half-Year</option>
                            <option value="yearly">Yearly</option>
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="number-type">Number Type</label>
                        <select id="number-type" class="form-control">
                            <option value="applications">All applications</option>
                            <option value="snr_status">Shortcodes</option>
                            <option value="tf_status">Toll-Free</option>
                            <option value="pr_status">Premium Rate</option>
                        </select>
                    </div>
                    <div class="form-group col-md-4">
                        <label for="date-range">Date Range</label>
                        <input type="text" class="form-control" id="date-range" placeholder="Select date range">
                    </div>
                </div>
                <button type="submit" class="btn btn-secondary">Generate Structured Report</button>
            </form>
        </div>

        <div id="report-output" class="mt-5">
            <div id="report-text" class="mt-4"></div>
            <div id="report-visualizations-container" class="mt-4"></div> <div id="reduced-dataframe-visualization" class="mt-4"></div>
            <div id="reduced-dataframe-summary-visualization" class="mt-4"></div>
        </div>
    </div>

    <script>
        // Initialize the flatpickr for date range selection
        document.addEventListener('DOMContentLoaded', function() {
            flatpickr("#date-range", {
                mode: "range",
                dateFormat: "Y-m-d"
            });
        });

        // Event listener for Natural Language Report Request (No changes needed here if natural report route is not updated)
        document.getElementById('generate-nl-report').addEventListener('click', function() {
            const description = document.getElementById('report-description').value;
            if (description.trim() === '') {
                alert('Please enter a description for the report.');
                return;
            }

            fetch('/generate_natural_report', { // Assuming this route exists and works
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({description: description})
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                } else {
                    displayReport(data);
                }
            })
            .catch(error => console.error('Error:', error));
        });

        // Event listener for Structured Report Request
        document.getElementById('static-report-form').addEventListener('submit', function(event) {
            event.preventDefault();

            const formData = {
                report_type: document.getElementById('report-type').value,
                number_type: document.getElementById('number-type').value,
                date_range: document.getElementById('date-range').value
            };

            fetch('/generate_static_report', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert(data.error);
                } else {
                    displayReport(data);
                }
            })
            .catch(error => console.error('Error:', error));
        });

        // Function to display the report
        function displayReport(data) {
            // Ensure that the summary is available
            if (data.summary) {
                const summary = Array.isArray(data.summary) ? data.summary[0] : data.summary;

                // Construct HTML for the report summary (same as before)
                let summaryHtml = `<h4>Report Summary</h4><p>${summary.report}</p>`;

                if (summary.statistics) {
                    summaryHtml += `
                        <h5>Statistics</h5>
                        <ul>
                            <li>Total Applications: ${summary.statistics.total_applications}</li>
                            <li>Assigned SNR: ${summary.statistics.assigned_snr}</li>
                            <li>Available SNR: ${summary.statistics.available_snr}</li>
                            <li>Total Fees Collected: ${summary.statistics.total_fees_collected}</li>
                            <li>Applications by Category:</li>
                            <ul>
                                <li>Tollfree: ${summary.statistics.applications_by_category.Tollfree}</li>
                                <li>Shortcode: ${summary.statistics.applications_by_category.Shortcode}</li>
                            </ul>
                        </ul>
                    `;
                }

                if (summary.observations && summary.observations.length > 0) {
                    summaryHtml += `<h5>Observations</h5><ul>`;
                    summary.observations.forEach(observation => {
                        summaryHtml += `<li>${observation}</li>`;
                    });
                    summaryHtml += `</ul>`;
                }
                document.getElementById('report-text').innerHTML = summaryHtml;
            } else {
                document.getElementById('report-text').innerHTML = "No report summary available.";
            }

            // Display visualizations - now handles a list
            const visualizationsContainer = document.getElementById('report-visualizations-container');
            visualizationsContainer.innerHTML = ''; // Clear previous visualizations

            if (data.visualizations && Array.isArray(data.visualizations) && data.visualizations.length > 0) {
                data.visualizations.forEach(vizData => {
                    const vizDiv = document.createElement('div');
                    vizDiv.className = 'mt-4'; // Add some margin

                    const titleElement = document.createElement('h5');
                    titleElement.textContent = vizData.title || 'Visualization'; // Use title from GPT or default
                    vizDiv.appendChild(titleElement);

                    if (vizData.description) {
                        const descriptionElement = document.createElement('p');
                        descriptionElement.textContent = vizData.description;
                        vizDiv.appendChild(descriptionElement);
                    }

                    const plotDiv = document.createElement('div');
                    plotDiv.id = `visualization-${Math.random().toString(36).substring(7)}`; // Unique ID for Plotly
                    vizDiv.appendChild(plotDiv);
                    visualizationsContainer.appendChild(vizDiv);

                    Plotly.newPlot(plotDiv.id, vizData.visualization.data, vizData.visualization.layout);
                });
            } else {
                visualizationsContainer.innerHTML = "<p>No visualizations available for this report.</p>";
            }


            // Display visualization for reduced dataframe table (same as before)
            if (data.dataframe_visualization) {
                Plotly.newPlot('reduced-dataframe-visualization', data.dataframe_visualization.data, data.dataframe_visualization.layout);
            }

            // Display visualization for reduced dataframe summary table (same as before)
            if (data.dataframe_visualization2) {
                Plotly.newPlot('reduced-dataframe-summary-visualization', data.dataframe_visualization2.data, data.dataframe_visualization2.layout);
            }
        }
    </script>
<!-- </body>
</html> -->

{% endblock %}
