{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <h2>Application Status</h2>
    
    {% if application %}
    <div class="card">
        <div class="card-body">
            <h4 class="card-title">Application ID: {{ application.application_id }}</h4>
            <h5 class="card-subtitle mb-2 text-muted">Status: {{ application.status }}</h5>

            <ul class="list-group list-group-flush mt-3">
                <li class="list-group-item">
                    <strong>Applicant Name:</strong> {{ application.applicant_name }}
                </li>
                <li class="list-group-item">
                    <strong>Application Date:</strong> {{ application.application_date }}
                </li>
                <li class="list-group-item">
                    <strong>Certificate Expiry Date:</strong> {{ application.certificate_expiry_date }}
                </li>
                <li class="list-group-item">
                    <strong>Processed By:</strong> {{ application.processed_by }}
                </li>
                <li class="list-group-item">
                    <strong>Remarks:</strong> {{ application.remarks }}
                </li>
            </ul>

            {% if application.certificate_generated %}
            <div class="alert alert-success mt-3">
                Certificate generated and is printable.
            </div>
            {% else %}
            <div class="alert alert-warning mt-3">
                Certificate not yet generated.
            </div>
            {% endif %}
        </div>
    </div>

    {% else %}
    <div class="alert alert-danger mt-4">
        Application not found or you are not authorized to view this application.
    </div>
    {% endif %}
    
    <div class="mt-4">
        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">Back to Dashboard</a>
    </div>
</div>
{% endblock %}
