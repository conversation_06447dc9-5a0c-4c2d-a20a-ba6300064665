{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card glassmorphic-card p-4">
                <div class="card-body">
                    <h2 class="card-title text-center">Generate Certificate</h2>
                    <p class="text-muted text-center">
                        Please review the details below and modify the start and expiry dates if necessary.
                    </p>
                    
                    <!-- Application details -->
                    <div class="mb-4">
                        <h4>Applicant Information</h4>
                        <p><strong>Applicant Name:</strong> {{ application.applicant_name }}</p>
                        <p><strong>Application ID:</strong> {{ application.application_id }}</p>
                    </div>

                    <!-- Date selection form -->
                    <form method="POST" action="{{ url_for('generate_certificate', application_id=application.application_id) }}">
                        <div class="form-group">
                            <label for="start_date">Start Date:</label>
                            <input type="date" id="start_date" name="start_date" class="form-control"
                                value="{{ default_start_date }}" required>
                        </div>
                        <div class="form-group">
                            <label for="expiry_date">Expiry Date:</label>
                            <input type="date" id="expiry_date" name="expiry_date" class="form-control"
                                value="{{ default_expiry_date }}" required>
                        </div>
                        <button type="submit" class="btn btn-success btn-block mt-4">Generate Certificate</button>
                        

                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- <script>
function generateCertificate(applicationId, event) { // Add 'event' parameter
    event.preventDefault(); // Prevent default form submission

    const startDate = document.getElementById('start_date').value;
    const expiryDate = document.getElementById('expiry_date').value;

    fetch(`/generate_certificate/${applicationId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json', // Keep sending as JSON
        },
        body: JSON.stringify({ start_date: startDate, expiry_date: expiryDate })
    })
    .then(response => {
        if (response.ok) {
            window.location.href = '/review_applications';
        } else {
            throw new Error('Failed to generate the certificate');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while generating the certificate.');
    });
}
    </script>
     -->

{% endblock %}
