#!/usr/bin/env python
"""
Import script for TELECEL's E.164 data for 2024-H2 to MongoDB.
Usage: python import_telecel_data_2024h2.py
"""

import pymongo
from pymongo import MongoClient
from datetime import datetime, timezone
import json
from bson import ObjectId

# MongoDB connection details - update as needed
MONGO_URI = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
DB_NAME = "new_numbering"

# The JSON data for E.164 submissions
e164_submission = {
    "operator_id": "TELECEL_OP_ID",  # Will be updated if we find the real operator ID
    "operator": "TELECEL",
    "reporting_period": "2024-h2",
    "contact_person": "Benjamin Fio",
    "contact_email": "<EMAIL>",
    "notes": "Second half 2024 E.164 number audit",
    "status": "approved"
}

# The JSON data for E.164 ranges - extracted from the Excel file
e164_ranges = [
    {
        "ndc": "020",
        "type": "WIRELESS DEDICATED",
        "start_block": "0200000000",
        "end_block": "0209999999",
        "total_allocated": 10000000,
        "active_subscriber": 2085514,
        "active_non_subscriber": 0,
        "inactive": 7352143,
        "reserved": 41547,
        "gross_addition": 403018,
        "net_addition": 117778,
        "ported_in": 3,
        "ported_out": 0,
        "utilization_rate": 20.86,
        "dormancy_rate": 73.52,
        "reservation_rate": 0.42
    },
    {
        "ndc": "050",
        "type": "WIRELESS DEDICATED",
        "start_block": "0500000000",
        "end_block": "0509999999",
        "total_allocated": 10000000,
        "active_subscriber": 2831408,
        "active_non_subscriber": 0,
        "inactive": 6194230,
        "reserved": 481143,
        "gross_addition": 420501,
        "net_addition": 72718,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 28.31,
        "dormancy_rate": 61.94,
        "reservation_rate": 4.81
    },
    {
        "ndc": "0322",
        "type": "FIXED SHARED",
        "start_block": "0322000000",
        "end_block": "0322599999",
        "total_allocated": 600000,
        "active_subscriber": 978,
        "active_non_subscriber": 0,
        "inactive": 599022,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.16,
        "dormancy_rate": 99.84,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0352",
        "type": "FIXED SHARED",
        "start_block": "0352000000",
        "end_block": "0352799999",
        "total_allocated": 800000,
        "active_subscriber": 110,
        "active_non_subscriber": 0,
        "inactive": 799890,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.01,
        "dormancy_rate": 99.99,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0332",
        "type": "FIXED SHARED",
        "start_block": "0332000000",
        "end_block": "0332399999",
        "total_allocated": 400000,
        "active_subscriber": 225,
        "active_non_subscriber": 0,
        "inactive": 399775,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.06,
        "dormancy_rate": 99.94,
        "reservation_rate": 0.0
    },
    {
        "ndc": "034",
        "type": "FIXED SHARED",
        "start_block": "0342000000",
        "end_block": "0343099999",
        "total_allocated": 1100000,
        "active_subscriber": 261,
        "active_non_subscriber": 0,
        "inactive": 1099739,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0.0
    },
    {
        "ndc": "030",
        "type": "FIXED SHARED",
        "start_block": "0302000000",
        "end_block": "0303599999",
        "total_allocated": 1600000,
        "active_subscriber": 18695,
        "active_non_subscriber": 0,
        "inactive": 1581305,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 1.17,
        "dormancy_rate": 98.83,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0372",
        "type": "FIXED SHARED",
        "start_block": "0372000000",
        "end_block": "0372699999",
        "total_allocated": 700000,
        "active_subscriber": 109,
        "active_non_subscriber": 0,
        "inactive": 699891,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.02,
        "dormancy_rate": 99.98,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0382",
        "type": "FIXED SHARED",
        "start_block": "0382000000",
        "end_block": "0382299999",
        "total_allocated": 300000,
        "active_subscriber": 34,
        "active_non_subscriber": 0,
        "inactive": 299966,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.01,
        "dormancy_rate": 99.99,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0392",
        "type": "FIXED SHARED",
        "start_block": "0392000000",
        "end_block": "0392099999",
        "total_allocated": 100000,
        "active_subscriber": 76,
        "active_non_subscriber": 0,
        "inactive": 99924,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.08,
        "dormancy_rate": 99.92,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0362",
        "type": "FIXED SHARED",
        "start_block": "0362000000",
        "end_block": "0362699999",
        "total_allocated": 700000,
        "active_subscriber": 242,
        "active_non_subscriber": 0,
        "inactive": 699758,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.03,
        "dormancy_rate": 99.97,
        "reservation_rate": 0.0
    },
    {
        "ndc": "0312",
        "type": "FIXED SHARED",
        "start_block": "0312000000",
        "end_block": "0312699999",
        "total_allocated": 700000,
        "active_subscriber": 391,
        "active_non_subscriber": 0,
        "inactive": 699609,
        "reserved": 0,
        "gross_addition": 0,
        "net_addition": 0,
        "ported_in": 0,
        "ported_out": 0,
        "utilization_rate": 0.06,
        "dormancy_rate": 99.94,
        "reservation_rate": 0.0
    }
]

# No porting details data in the 2024-H2 Excel file
e164_porting_details = []

def import_data():
    """Import the data into MongoDB"""
    # Connect to MongoDB
    client = MongoClient(MONGO_URI)
    db = client[DB_NAME]
    
    # Add timestamps and generate unique ID
    submission_id = str(ObjectId())
    now = datetime.now(timezone.utc)
    
    # Create the submission
    e164_submission.update({
        "_id": ObjectId(submission_id),
        "created_at": now,
        "updated_at": now,
        "created_by": "import_script",
        "updated_by": "import_script"
    })
    
    try:
        # Get the TELECEL operator ID from the database if possible
        telecel_operator = db.operators.find_one({"name": "TELECEL"})
        if telecel_operator:
            e164_submission["operator_id"] = str(telecel_operator["_id"])
            print(f"Found TELECEL operator ID: {e164_submission['operator_id']}")
    except Exception as e:
        print(f"Warning: Could not find TELECEL operator in database: {e}")
        print("Using placeholder operator_id")
    
    # Insert submission
    print("Inserting submission...")
    db.e164_submissions.insert_one(e164_submission)
    
    # Update ranges with submission_id and timestamps
    print(f"Processing {len(e164_ranges)} ranges...")
    for r in e164_ranges:
        r.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert ranges
    if e164_ranges:
        db.e164_ranges.insert_many(e164_ranges)
        print(f"Inserted {len(e164_ranges)} ranges")
    
    # Update porting data with submission_id and timestamps (empty for this dataset)
    print(f"Processing {len(e164_porting_details)} porting records...")
    for p in e164_porting_details:
        p.update({
            "submission_id": submission_id,
            "created_at": now,
            "updated_at": now
        })
    
    # Insert porting data (will be empty for this dataset)
    if e164_porting_details:
        db.e164_porting_details.insert_many(e164_porting_details)
        print(f"Inserted {len(e164_porting_details)} porting records")
    else:
        print("No porting records to insert for 2024-H2 dataset")
    
    print(f"Import complete. Submission ID: {submission_id}")
    return submission_id

def print_summary():
    """Print a summary of the data to be imported"""
    print("=" * 60)
    print("TELECEL 2024-H2 E.164 AUDIT DATA IMPORT SUMMARY")
    print("=" * 60)
    print(f"Operator: {e164_submission['operator']}")
    print(f"Reporting Period: {e164_submission['reporting_period']}")
    print(f"Contact Person: {e164_submission['contact_person']}")
    print(f"Contact Email: {e164_submission['contact_email']}")
    print(f"Total Number Ranges: {len(e164_ranges)}")
    print(f"Total Porting Records: {len(e164_porting_details)}")
    
    # Calculate totals
    total_allocated = sum(r['total_allocated'] for r in e164_ranges)
    total_active = sum(r['active_subscriber'] for r in e164_ranges)
    total_inactive = sum(r['inactive'] for r in e164_ranges)
    total_reserved = sum(r['reserved'] for r in e164_ranges)
    
    print(f"\nOverall Statistics:")
    print(f"  Total Allocated Numbers: {total_allocated:,}")
    print(f"  Total Active Numbers: {total_active:,}")
    print(f"  Total Inactive Numbers: {total_inactive:,}")
    print(f"  Total Reserved Numbers: {total_reserved:,}")
    print(f"  Overall Utilization Rate: {(total_active / total_allocated * 100):.2f}%")
    print(f"  Overall Dormancy Rate: {(total_inactive / total_allocated * 100):.2f}%")
    
    print(f"\nRange Breakdown:")
    wireless_ranges = [r for r in e164_ranges if "WIRELESS" in r['type']]
    fixed_ranges = [r for r in e164_ranges if "FIXED" in r['type']]
    
    print(f"  Wireless Dedicated Ranges: {len(wireless_ranges)}")
    print(f"  Fixed Shared Ranges: {len(fixed_ranges)}")
    
    print("=" * 60)

if __name__ == "__main__":
    print_summary()
    
    # Ask for confirmation before proceeding
    confirm = input("\nProceed with import? (y/N): ").strip().lower()
    if confirm in ['y', 'yes']:
        submission_id = import_data()
        print("Data successfully imported!")
        print(f"Submission ID: {submission_id}")
    else:
        print("Import cancelled.")
