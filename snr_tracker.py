"""
snr_tracker.py - Helper module for detailed SNR tracking functionality
This module provides utility functions for tracking SNRs in the MongoDB database
and synchronizing SNR data between operational records and audit data.
"""

import json
import pandas as pd
from datetime import datetime, timezone
from bson.objectid import ObjectId

class SNRTracker:
    """
    Class to handle SNR tracking operations
    """
    def __init__(self, mongo_client):
        """
        Initialize with MongoDB client
        """
        self.mongo = mongo_client
    
    def get_operator_snrs(self, operator_name):
        """
        Get all SNRs associated with an operator
        
        Args:
            operator_name: Name of the operator
            
        Returns:
            dict: Dictionary containing active and inactive SNRs
        """
        # Get active SNRs from operational records
        active_snrs = list(self.mongo.db.snr_operations.find(
            {
                "assigned_operator": operator_name,
                "implementation_status": "active"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "application_id": 1,
                "certificate_id": 1,
                "activation_date": 1
            }
        ))
        
        # Get inactive/deactivated SNRs
        inactive_snrs = list(self.mongo.db.snr_operations.find(
            {
                "assigned_operator": operator_name,
                "implementation_status": "deactivated"
            },
            {
                "_id": 0,
                "snr": 1,
                "type": 1,
                "application_id": 1,
                "certificate_id": 1,
                "deactivation_date": 1,
                "deactivation_reason": 1
            }
        ))
        
        # Enhance with purpose information from applications
        self._enhance_snrs_with_purpose(active_snrs)
        self._enhance_snrs_with_purpose(inactive_snrs)
        
        return {
            "active": active_snrs,
            "inactive": inactive_snrs
        }
    
    def _enhance_snrs_with_purpose(self, snr_list):
        """
        Enhance SNR records with purpose information from applications
        
        Args:
            snr_list: List of SNR records to enhance
        """
        for snr in snr_list:
            app = self.mongo.db.applications.find_one(
                {"application_id": snr.get("application_id")},
                {"_id": 0, "snrs": {"$elemMatch": {"snr": snr.get("snr")}}}
            )
            
            if app and "snrs" in app and app["snrs"]:
                snr["purpose"] = app["snrs"][0].get("purpose", "")
    
    def get_current_audit_period(self):
        """
        Get the current audit period (year and quarter)
        
        Returns:
            tuple: (year, quarter) for the current audit period
        """
        now = datetime.now()
        year = now.year
        # Calculate the quarter (1-4)
        quarter = (now.month - 1) // 3 + 1
        return (year, f"Q{quarter}")
    
    def sync_audit_with_operations(self, operator_name, year=None, quarter=None):
        """
        Synchronize audit records with operational SNR data
        
        Args:
            operator_name: Name of the operator
            year: Year to synchronize (default: current year)
            quarter: Quarter to synchronize (default: current quarter)
            
        Returns:
            dict: Result of the synchronization operation
        """
        # Use current period if not specified
        if year is None or quarter is None:
            year, quarter = self.get_current_audit_period()
        else:
            # Ensure year is an integer
            year = int(year)
        # Get operator SNRs
        snr_data = self.get_operator_snrs(operator_name)
        active_snrs = snr_data["active"]
        inactive_snrs = snr_data["inactive"]
        
        # Start a session for transaction
        with self.mongo.cx.start_session() as session:
            with session.start_transaction():
                # Get the audit record for this period
                audit_record = self.mongo.db.audits.find_one(
                    {
                        "operator": operator_name,
                        "year": year,
                        "quarter": quarter
                    },
                    session=session
                )
                if not audit_record:
                    return {
                        "success": False,
                        "message": f"No audit record found for {operator_name} in {year} {quarter}"
                    }
                
                # Format active SNRs for the audit record
                active_snrs_data = []
                for snr in active_snrs:
                    active_snrs_data.append({
                        "snr": snr.get("snr"),
                        "status": "active",
                        "activated_date": snr.get("activation_date", datetime.now(timezone.utc)),
                        "purpose": snr.get("purpose", ""),
                        "certificate_id": snr.get("certificate_id", ""),
                        "type": snr.get("type", self._determine_snr_type(snr.get("snr", "")))
                    })
                
                # Format inactive SNRs for the audit record
                inactive_snrs_data = []
                for snr in inactive_snrs:
                    inactive_snrs_data.append({
                        "snr": snr.get("snr"),
                        "status": "deactivated",
                        "deactivation_date": snr.get("deactivation_date", datetime.now(timezone.utc)),
                        "reason": snr.get("deactivation_reason", "Not specified"),
                        "purpose": snr.get("purpose", ""),
                        "certificate_id": snr.get("certificate_id", ""),
                        "type": snr.get("type", self._determine_snr_type(snr.get("snr", "")))
                    })
                
                # Update the audit record
                self.mongo.db.audits.update_one(
                    {"_id": audit_record["_id"]},
                    {
                        "$set": {
                            "active_snrs": active_snrs_data,
                            "deactivated_snrs": inactive_snrs_data,
                            "active_snrs_count": len(active_snrs_data),
                            "deactivated_snrs_count": len(inactive_snrs_data),
                            "last_synchronized": datetime.now(timezone.utc)
                        }
                    },
                    session=session
                )
                
                # Log the activity
                activity_log = {
                    "user_id": "System",
                    "action": "Audit SNR Sync",
                    "details": f"Synchronized SNR data for operator {operator_name} ({year} {quarter})",
                    "timestamp": datetime.now(timezone.utc)
                }
                
                self.mongo.db.activity_log.insert_one(activity_log, session=session)
        
        # Calculate SNR statistics
        snr_type_counts = {}
        for snr in active_snrs:
            snr_type = snr.get("type", self._determine_snr_type(snr.get("snr", "")))
            if snr_type not in snr_type_counts:
                snr_type_counts[snr_type] = 0
            snr_type_counts[snr_type] += 1
        
        return {
            "success": True,
            "message": f"Successfully synchronized SNR data for {operator_name}",
            "active_count": len(active_snrs_data),
            "inactive_count": len(inactive_snrs_data),
            "snr_types": snr_type_counts
        }
    
    def update_snr_status(self, snr, new_status, operator_name, reason=None):
        """
        Update the status of an SNR and reflect in audit data
        
        Args:
            snr: The SNR to update
            new_status: New status (active, deactivated)
            operator_name: Name of the operator
            reason: Reason for status change (for deactivation)
            
        Returns:
            dict: Result of the update operation
        """
        # Validate parameters
        if not snr or not operator_name:
            return {
                "success": False,
                "message": "SNR and operator_name are required"
            }
        
        if new_status not in ["active", "deactivated"]:
            return {
                "success": False,
                "message": "Status must be either 'active' or 'deactivated'"
            }
        
        # Map to implementation status
        implementation_status = "active" if new_status == "active" else "deactivated"
        
        # Start a session for transaction
        with self.mongo.cx.start_session() as session:
            with session.start_transaction():
                # Update SNR operations record
                update_data = {
                    "implementation_status": implementation_status,
                    "last_updated": datetime.now(timezone.utc)
                }
                
                if new_status == "active":
                    update_data["activation_date"] = datetime.now(timezone.utc)
                elif new_status == "deactivated":
                    update_data["deactivation_date"] = datetime.now(timezone.utc)
                    update_data["deactivation_reason"] = reason or "Not specified"
                
                result = self.mongo.db.snr_operations.update_one(
                    {
                        "snr": snr,
                        "assigned_operator": operator_name
                    },
                    {"$set": update_data},
                    session=session
                )
                
                if result.matched_count == 0:
                    # SNR record not found, create one if activating
                    if new_status == "active":
                        # Get application details if available
                        application = self.mongo.db.applications.find_one(
                            {"snrs.snr": snr},
                            {"_id": 0, "application_id": 1, "snrs.$": 1},
                            session=session
                        )
                        
                        # Create new SNR operations record
                        snr_op_record = {
                            "snr": snr,
                            "type": self._determine_snr_type(snr),
                            "assigned_operator": operator_name,
                            "implementation_status": "active",
                            "claim_date": datetime.now(timezone.utc),
                            "activation_date": datetime.now(timezone.utc),
                            "last_updated": datetime.now(timezone.utc)
                        }
                        
                        if application:
                            snr_op_record["application_id"] = application["application_id"]
                            if "snrs" in application and application["snrs"]:
                                snr_op_record["certificate_id"] = application["snrs"][0].get("certificate_id", "")
                        
                        self.mongo.db.snr_operations.insert_one(snr_op_record, session=session)
                    else:
                        return {
                            "success": False,
                            "message": f"Cannot deactivate SNR {snr} that is not active for operator {operator_name}"
                        }
                
                # Get current audit period
                year, quarter = self.get_current_audit_period()
                
                # Update audit record if it exists
                if new_status == "active":
                    # Get application details
                    application = self.mongo.db.applications.find_one(
                        {"snrs.snr": snr},
                        {"_id": 0, "application_id": 1, "snrs.$": 1},
                        session=session
                    )
                    
                    # Prepare SNR record for audit
                    snr_record = {
                        "snr": snr,
                        "status": "active",
                        "activated_date": datetime.now(timezone.utc),
                        "type": self._determine_snr_type(snr)
                    }
                    
                    if application and "snrs" in application and application["snrs"]:
                        snr_record["purpose"] = application["snrs"][0].get("purpose", "")
                        snr_record["certificate_id"] = application["snrs"][0].get("certificate_id", "")
                    
                    # Add to active_snrs array and increment count
                    self.mongo.db.audits.update_one(
                        {
                            "operator": operator_name,
                            "year": year,
                            "quarter": quarter
                        },
                        {
                            "$push": {"active_snrs": snr_record},
                            "$inc": {"active_snrs_count": 1}
                        },
                        session=session
                    )
                    
                elif new_status == "deactivated":
                    # Move from active to deactivated in audit
                    # First, get the SNR record from active_snrs
                    audit = self.mongo.db.audits.find_one(
                        {
                            "operator": operator_name,
                            "year": year,
                            "quarter": quarter,
                            "active_snrs.snr": snr
                        },
                        {"active_snrs.$": 1},
                        session=session
                    )
                    
                    if audit and "active_snrs" in audit and audit["active_snrs"]:
                        # Create deactivated record based on active one
                        active_record = audit["active_snrs"][0]
                        deactivated_record = {
                            "snr": active_record["snr"],
                            "status": "deactivated",
                            "deactivation_date": datetime.now(timezone.utc),
                            "reason": reason or "Not specified",
                            "purpose": active_record.get("purpose", ""),
                            "certificate_id": active_record.get("certificate_id", ""),
                            "type": active_record.get("type", self._determine_snr_type(snr))
                        }
                        
                        # Remove from active_snrs
                        self.mongo.db.audits.update_one(
                            {
                                "operator": operator_name,
                                "year": year,
                                "quarter": quarter
                            },
                            {
                                "$pull": {"active_snrs": {"snr": snr}},
                                "$push": {"deactivated_snrs": deactivated_record},
                                "$inc": {
                                    "active_snrs_count": -1,
                                    "deactivated_snrs_count": 1
                                }
                            },
                            session=session
                        )
                    else:
                        # SNR not found in active_snrs, just add to deactivated_snrs
                        # Get application details
                        application = self.mongo.db.applications.find_one(
                            {"snrs.snr": snr},
                            {"_id": 0, "application_id": 1, "snrs.$": 1},
                            session=session
                        )
                        
                        # Prepare SNR record for audit
                        snr_record = {
                            "snr": snr,
                            "status": "deactivated",
                            "deactivation_date": datetime.now(timezone.utc),
                            "reason": reason or "Not specified",
                            "type": self._determine_snr_type(snr)
                        }
                        
                        if application and "snrs" in application and application["snrs"]:
                            snr_record["purpose"] = application["snrs"][0].get("purpose", "")
                            snr_record["certificate_id"] = application["snrs"][0].get("certificate_id", "")
                        
                        # Add to deactivated_snrs array and increment count
                        self.mongo.db.audits.update_one(
                            {
                                "operator": operator_name,
                                "year": year,
                                "quarter": quarter
                            },
                            {
                                "$push": {"deactivated_snrs": snr_record},
                                "$inc": {"deactivated_snrs_count": 1}
                            },
                            session=session
                        )
                
                # Log the activity
                activity_log = {
                    "user_id": "API",
                    "action": f"SNR {new_status.title()}",
                    "details": f"SNR {snr} {new_status} for operator {operator_name}",
                    "timestamp": datetime.now(timezone.utc),
                    "snr": snr,
                    "operator": operator_name
                }
                
                if reason:
                    activity_log["reason"] = reason
                
                self.mongo.db.activity_log.insert_one(activity_log, session=session)
        
        return {
            "success": True,
            "message": f"SNR {snr} {new_status} successfully",
            "operator": operator_name,
            "snr": snr,
            "status": new_status
        }
    
    def export_operator_snrs(self, operator_name, format="json"):
        """
        Export operator SNRs to JSON or CSV format
        
        Args:
            operator_name: Name of the operator
            format: Export format ("json" or "csv")
            
        Returns:
            str or dict: Exported data in requested format
        """
        # Get operator SNRs
        snr_data = self.get_operator_snrs(operator_name)
        
        # Combine active and inactive with status field
        all_snrs = []
        for snr in snr_data["active"]:
            snr["status"] = "active"
            all_snrs.append(snr)
        
        for snr in snr_data["inactive"]:
            snr["status"] = "inactive"
            all_snrs.append(snr)
        
        # Format dates for JSON serialization
        for snr in all_snrs:
            for key in snr:
                if isinstance(snr[key], datetime):
                    snr[key] = snr[key].isoformat()
        
        if format.lower() == "csv":
            # Convert to DataFrame and then to CSV
            df = pd.DataFrame(all_snrs)
            return df.to_csv(index=False)
        else:
            # Return JSON
            return json.dumps(all_snrs, indent=2)
    
    def import_operator_snrs(self, operator_name, data, format="json"):
        """
        Import operator SNRs from JSON or CSV format
        
        Args:
            operator_name: Name of the operator
            data: Data to import
            format: Import format ("json" or "csv")
            
        Returns:
            dict: Result of the import operation
        """
        try:
            # Parse data based on format
            if format.lower() == "csv":
                # Parse CSV to DataFrame
                if isinstance(data, str):
                    # Parse string data
                    df = pd.read_csv(pd.StringIO(data))
                else:
                    # Assume file-like object
                    df = pd.read_csv(data)
                
                # Convert DataFrame to list of dicts
                snrs = df.to_dict(orient="records")
            else:
                # Parse JSON
                if isinstance(data, str):
                    snrs = json.loads(data)
                else:
                    # Assume already parsed JSON
                    snrs = data
            
            # Process SNRs
            processed = 0
            active_count = 0
            inactive_count = 0
            errors = []
            
            for snr in snrs:
                snr_number = snr.get("snr")
                status = snr.get("status", "active").lower()
                reason = snr.get("reason") if status == "inactive" else None
                
                if not snr_number:
                    errors.append(f"Missing SNR number in record: {snr}")
                    continue
                
                # Update SNR status
                result = self.update_snr_status(
                    snr_number, 
                    "active" if status == "active" else "deactivated", 
                    operator_name,
                    reason
                )
                
                if result["success"]:
                    processed += 1
                    if status == "active":
                        active_count += 1
                    else:
                        inactive_count += 1
                else:
                    errors.append(f"Error updating SNR {snr_number}: {result['message']}")
            
            return {
                "success": True,
                "message": f"Imported {processed} SNRs for operator {operator_name}",
                "processed": processed,
                "active_count": active_count,
                "inactive_count": inactive_count,
                "errors": errors
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Error importing SNRs: {str(e)}",
                "errors": [str(e)]
            }
    
    def _determine_snr_type(self, snr):
        """
        Determine the type of SNR based on its format
        
        Args:
            snr: SNR to determine type for
            
        Returns:
            str: Type of SNR ("shortcode", "tollfree", "premium", or "unknown")
        """
        if not snr:
            return "unknown"
            
        snr = str(snr).strip()
        
        if snr.startswith('0800'):
            return "tollfree"
        elif snr.startswith('0900'):
            return "premium"
        elif len(snr) <= 6:
            return "shortcode"
        else:
            return "unknown"
