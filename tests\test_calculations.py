import pytest
from e164.routes import calculate_exhaustion_for_ndc, calculate_block_efficiency

@pytest.mark.usefixtures("mock_mongo")
def test_calculate_exhaustion_for_ndc():
    # Test with growth rate = 0 (should return 99.9)
    range_doc_no_growth = {
        'total_allocated': 1000000,
        'active_subscriber': 700000,
        'active_non_subscriber': 50000,
        'gross_addition': 0,
        'net_addition': 0
    }
    
    result = calculate_exhaustion_for_ndc(range_doc_no_growth)
    assert result == 99.9  # When no growth, returns 99.9 (effectively "never")
    
    # Test with positive growth rate
    range_doc_with_growth = {
        'total_allocated': 1000000,
        'active_subscriber': 700000,
        'active_non_subscriber': 50000,
        'gross_addition': 50000,  # Adding explicit growth rate
        'net_addition': 40000
    }
    
    result = calculate_exhaustion_for_ndc(range_doc_with_growth)
    assert result > 0 and result < 99.9  # Should return a finite number of years

@pytest.mark.usefixtures("mock_mongo")
def test_calculate_block_efficiency():
    # Test with explicit utilization_rate in the range_doc
    range_doc = {
        'total_allocated': 1000000,
        'active_subscriber': 700000,
        'active_non_subscriber': 50000,
        'utilization_rate': 75.0  # This is what the function actually uses
    }
    
    result = calculate_block_efficiency(range_doc)
    assert result == 75.0