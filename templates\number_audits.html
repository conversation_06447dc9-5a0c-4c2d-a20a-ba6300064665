<!-- templates/number_audits.html -->

{% extends "base.html" %}
{% block content %}
<div class="container mt-5">
    <h2>Number Range Audit Analysis</h2>
    
    <!-- Operator Selection -->
    <div class="form-group">
        <label>Select Operator:</label>
        <select id="operatorSelect" class="form-control">
            {% for op in operators %}
            <option value="{{ op }}">{{ op }}</option>
            {% endfor %}
        </select>
    </div>

    <!-- Editable Audit Table -->
    <table class="table table-bordered" id="auditTable">
        <thead>
            <tr>
                <th>NDC</th>
                <th>Start Block</th>
                <th>End Block</th>
                <th>TASN</th>
                <th>Active Subscribers</th>
                <th>Reserved Numbers</th>
            </tr>
        </thead>
        <tbody>
            <!-- Initial sample row -->
            <tr>
                <td><input type="text" class="form-control ndc" required></td>
                <td><input type="number" class="form-control start" required></td>
                <td><input type="number" class="form-control end" required></td>
                <td><input type="number" class="form-control tasn" readonly></td>
                <td><input type="number" class="form-control active" required></td>
                <td><input type="number" class="form-control reserved" required></td>
            </tr>
        </tbody>
    </table>
    <button class="btn btn-secondary" onclick="addRow()">Add Range</button>
    <button class="btn btn-primary" onclick="submitAudit()">Submit Analysis</button>

    <!-- Analysis Dashboard -->
    <div class="row mt-4">
        <!-- Summary Metrics -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Key Metrics</h5>
                    <div id="metricsContainer">
                        <p>Total Numbers: <span id="totalNumbers">0</span></p>
                        <p>Average Utilization: <span id="avgUtilization">0%</span></p>
                        <p>Active Subscribers: <span id="totalActive">0</span></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visualizations -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Utilization by NDC</h5>
                    <div id="utilizationChart"></div>
                </div>
            </div>
            <div class="card mt-4">
                <div class="card-body">
                    <h5 class="card-title">Subscriber Distribution</h5>
                    <div id="distributionChart"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Predefined TASN values by operator
const operatorTASN = {
    'MTN': 10000000,
    'Vodafone': 8500000,
    'AirtelTigo': 7500000,
    'Glo': 5000000
};

// Update TASN when operator changes
$('#operatorSelect').change(function() {
    const tasn = operatorTASN[$(this).val()];
    $('.tasn').val(tasn);
});

function addRow() {
    const newRow = `
    <tr>
        <td><input type="text" class="form-control ndc" required></td>
        <td><input type="number" class="form-control start" required></td>
        <td><input type="number" class="form-control end" required></td>
        <td><input type="number" class="form-control tasn" readonly></td>
        <td><input type="number" class="form-control active" required></td>
        <td><input type="number" class="form-control reserved" required></td>
    </tr>`;
    $('#auditTable tbody').append(newRow);
    $('#operatorSelect').trigger('change');
}

function submitAudit() {
    const auditData = [];
    $('#auditTable tbody tr').each(function() {
        auditData.push({
            operator: $('#operatorSelect').val(),
            ndc: $(this).find('.ndc').val(),
            start_block: parseInt($(this).find('.start').val()),
            end_block: parseInt($(this).find('.end').val()),
            tasn: parseInt($(this).find('.tasn').val()),
            active: parseInt($(this).find('.active').val()),
            reserved: parseInt($(this).find('.reserved').val())
        });
    });

    fetch("{{ url_for('number_audits') }}", {
        method: 'POST',
        headers: {'Content-Type': 'application/json'},
        body: JSON.stringify(auditData)
    })
    .then(response => response.json())
    .then(data => {
        if(data.error) throw new Error(data.error);
        updateAnalysisUI(data.analysis);
    })
    .catch(error => alert(error.message));
}

function updateAnalysisUI(analysis) {
    // Update metrics
    $('#totalNumbers').text(analysis.metrics.total_numbers.toLocaleString());
    $('#avgUtilization').text((analysis.metrics.avg_utilization * 100).toFixed(1) + '%');
    $('#totalActive').text(analysis.metrics.total_active.toLocaleString());

    // Update charts
    Plotly.newPlot('utilizationChart', [{
        x: analysis.charts.utilization_by_range.map(i => i.x),
        y: analysis.charts.utilization_by_range.map(i => i.y),
        type: 'bar'
    }]);

    Plotly.newPlot('distributionChart', [{
        values: analysis.charts.subscriber_distribution.map(i => i.value),
        labels: analysis.charts.subscriber_distribution.map(i => i.name),
        type: 'pie'
    }]);
}
</script>

{% endblock %}