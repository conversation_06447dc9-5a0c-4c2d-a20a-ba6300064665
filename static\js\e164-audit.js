
/**
 * E.164 Number Range Audit Dashboard
 * Consolidated JavaScript file handling all client-side functionality
 * for submission_form.html
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log("DOM fully loaded and parsed. Initializing E164 Audit script...");

    // =========================================================================
    // Application State
    // =========================================================================
    const appState = {
        latestData: null,
        historicalData: [],
        chartData: {"labels":[],"utilization":[],"dormancy":[],"reservation":[]},
        trendChartData: null, // Will be populated from window.E164Data.trendChartData
        isAdmin: false,
        activeTab: 'default', // Default value
        allOperators: [],
        reportingPeriods: [],
        ranges: [],       // Array to store number range objects
        portingData: [],  // Array to store porting data objects
        operatorNdcCache: {}, // Cache for operator NDCs
        charts: {} // To hold chart instances for potential updates/destruction
    };

    // =========================================================================
    // Initialization Function
    // =========================================================================
    function init() {
        console.log("E164 Audit: Initializing application...");





        // --- 1. Load Data from Server ---
        if (window.E164Data) {
            appState.latestData = window.E164Data.latestData;
            appState.historicalData = window.E164Data.historicalData;
            appState.chartData = window.E164Data.chartData || {"labels":[],"utilization":[],"dormancy":[],"reservation":[]};
            appState.trendChartData = window.E164Data.trendChartData || null; // Load trend chart data
            appState.isAdmin = window.E164Data.isAdmin;
            appState.activeTab = window.E164Data.activeTab || (appState.isAdmin ? 'analytics' : 'submission'); // Simplified default
            appState.allOperators = window.E164Data.allOperators || [];
            appState.reportingPeriods = window.E164Data.reportingPeriods || [];
            // Load new data from payload
            appState.marketShareResult = window.E164Data.marketShareResult || null;
            appState.operatorComparison = window.E164Data.operatorComparison || [];


            console.log("App initialized with data:", appState);
            document.body.setAttribute('data-active-tab', appState.activeTab);
        } else {
            console.error("E164Data not found in global scope. Cannot initialize.");
            showToast("Initialization Error", "Could not load necessary data from the server.", "danger");
            return; // Stop initialization if core data is missing
        }


                // --- 1.a Initialize Pointsphere if container exists ---
        if (document.getElementById('pointsphere-container')) {
            initPointsphere();
        }
        // --- End Pointsphere Init ---








        // --- 2. Setup Event Listeners ---
        setupTabListeners();
        // Only setup form listeners if the submission tab exists (i.e., not admin)
        if (!appState.isAdmin) {
            setupFormEventListeners();
            setupRangeModalListeners();
            setupPortingModalListeners();
            setupPreviewModalListeners(); // Assuming a preview modal exists
            setupImportModalListeners(); // Assuming an import modal exists
        }
        else {
                   // *** Setup listeners for Admin controls ***
        setupAdminAnalyticsListeners();
        }
        setupHistoryTableListeners(); // Assuming history table needs listeners




        // --- 3. Initialize UI Components ---
        if (!appState.isAdmin) {
            populateOperatorDropdowns(); // Populate operator dropdowns in forms
            updateRangeTable(); // Initial render of the (empty) ranges table
            updatePortingTable(); // Initial render of the (empty) porting table
            // Potentially load draft data here if implemented
        }else {
        // *** Populate Admin selectors ***
        populateAdminOperatorSelector();
        populateAdminPeriodSelector(); // Fetches data
    }
        updateHistoryTable(); // Load initial history data

        // --- 4. Initialize Analytics (if on analytics tab initially) ---
        initializeAnalyticsIfActive();

        console.log("E164 Audit: Initialization complete.");
    }


/**
 * Populates the Admin Operator Selector dropdown.
 */
function populateAdminOperatorSelector() {
    const operatorSelect = document.getElementById('admin-analytics-operator-select');
    if (!operatorSelect || !appState.allOperators || appState.allOperators.length === 0) {
        console.warn("Admin operator selector or operator data not available.");
        if(operatorSelect) operatorSelect.innerHTML = '<option value="all" selected>Error loading operators</option>';
        return;
    }

    // Clear existing options (keeping the "All Operators" default if it's not hardcoded)
    // Assuming your HTML change *replaced* the dynamic population, we might re-add it:
    operatorSelect.innerHTML = '<option value="all" selected>All Operators</option>'; // Reset

    // Add options for each operator from appState
    appState.allOperators.forEach(op => {
        const option = new Option(op.name, op.id); // Text = op.name, Value = op.id
        operatorSelect.add(option);
    });
     console.log("Populated admin operator selector.");
}


/**
 * Fetches approved periods from the API and populates the Admin Period Selector.
 */
async function populateAdminPeriodSelector() {
    const periodSelect = document.getElementById('admin-analytics-period-select');
    if (!periodSelect) {
        console.warn("Admin period selector not found.");
        return;
    }

    try {
        const response = await fetch('/e164/api/analytics/periods'); // API endpoint from Step 1
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const data = await response.json();

        if (data.success && data.periods && data.periods.length > 0) {
            // Clear existing period-specific options (keep "Latest" and "All")
            const existingOptions = periodSelect.querySelectorAll('option');
            existingOptions.forEach(opt => {
                 if (opt.value !== 'latest' && opt.value !== 'all') {
                     opt.remove();
                 }
            });

            // Add fetched periods
            data.periods.forEach(period => {
                // Ensure period object has 'value' and 'text'
                if (period.value && period.text) {
                     const option = new Option(period.text, period.value);
                     periodSelect.add(option);
                }
            });
            console.log("Populated admin period selector with fetched periods.");
        } else {
            console.warn("API did not return successful period data:", data);
            // Optionally add an error option to the dropdown
            // const errorOption = new Option("Error loading periods", "");
            // periodSelect.add(errorOption);
        }
    } catch (error) {
        console.error("Error fetching or populating admin periods:", error);
        // Optionally add an error option to the dropdown
        // const errorOption = new Option("Error loading periods", "");
        // periodSelect.add(errorOption);
    }
}

/**
 * Sets up event listeners specific to the Admin Analytics controls.
 */
function setupAdminAnalyticsListeners() {
    const refreshButton = document.getElementById('admin-analytics-refresh-btn');
    if (refreshButton) {
        refreshButton.addEventListener('click', handleAdminAnalyticsRefresh);
    } else {
        console.warn("Admin analytics refresh button not found.");
    }
}







/**
 * Handles the click event for the Admin Analytics Refresh button.
 * Fetches filtered data and triggers chart re-rendering.
 */
async function handleAdminAnalyticsRefresh() {
    const periodSelect = document.getElementById('admin-analytics-period-select');
    const operatorSelect = document.getElementById('admin-analytics-operator-select');
    const refreshButton = document.getElementById('admin-analytics-refresh-btn');

    if (!periodSelect || !operatorSelect || !refreshButton) {
        console.error("Cannot refresh: Filter controls not found.");
        return;
    }

    const selectedPeriod = periodSelect.value;
    const selectedOperatorId = operatorSelect.value; // Assumes value is operator ID or 'all'

    console.log(`Refreshing admin analytics for Period: ${selectedPeriod}, Operator ID: ${selectedOperatorId}`);

    // Disable button and show loading state on charts
    refreshButton.disabled = true;
    refreshButton.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Refreshing...';
    showChartLoadingIndicators(); // Helper function to show spinners

    try {
        // *** IMPORTANT: Define and implement this API endpoint in routes.py ***
        const apiUrl = `/e164/api/analytics/filtered_data?period=${encodeURIComponent(selectedPeriod)}&operator_id=${encodeURIComponent(selectedOperatorId)}`;

        const response = await fetch(apiUrl);
        if (!response.ok) {
             const errorText = await response.text(); // Get error details from backend if possible
            throw new Error(`HTTP error! Status: ${response.status}, Message: ${errorText}`);
        }
        const data = await response.json();




        if (data.success && data.analyticsData) {
            console.log("Received filtered analytics data:", data.analyticsData);

            const filteredStatusData = data.analyticsData.statusDistributionData || null;
            const filteredRangeData = data.analyticsData.rangeDetails || [];
            // const filteredSankeyData = data.analyticsData.sankeyData || null;

            const filteredAggMetrics = data.analyticsData.aggregateMetrics || null;


            // --- Update appState with the new filtered data ---
            // Adjust these keys based on the actual structure returned by your API
            appState.marketShareResult = data.analyticsData.marketShareResult || null;
            appState.operatorComparison = data.analyticsData.operatorComparison || [];

            // Get Sankey data from the API response
            let filteredSankeyData = data.analyticsData.sankeyData || null;

            // Add operator and period information if available
            if (filteredSankeyData) {
                // If we have a specific operator selected, use that name
                if (selectedOperatorId !== 'all') {
                    const operatorOption = operatorSelect.options[operatorSelect.selectedIndex];
                    filteredSankeyData.operator = operatorOption ? operatorOption.text : 'Selected Operator';
                } else {
                    filteredSankeyData.operator = 'All Operators';
                }

                // Add period information
                if (selectedPeriod !== 'all') {
                    const periodOption = periodSelect.options[periodSelect.selectedIndex];
                    filteredSankeyData.period = periodOption ? periodOption.text : selectedPeriod;
                } else {
                    filteredSankeyData.period = 'All Periods';
                }
            }

            console.log("Filtered Sankey data from API with metadata:", filteredSankeyData);

            // --- Re-render charts ---
            // Note: Trend chart might need specific handling for 'all' periods
            if (selectedPeriod === 'all') {
                 console.log("Trend chart refresh for 'all' periods needs specific implementation.");
                 // Potentially call a different function or hide/show message
                //  document.getElementById('trend-chart-container')?.innerHTML = `<div class="alert alert-info">Trend view across all periods not yet implemented.</div>`; // Placeholder
            } else {
                // Re-render trend based on potentially filtered historical data if API returns it
                 renderTrendChart(); // Might need adjustment depending on API response
                //  document.getElementById('trend-chart-container')?.innerHTML = `<div class="alert alert-secondary">Trend data update based on filter not implemented.</div>`; // Placeholder
            }

            // Status distribution might also need filtered data if API provides it for specific period/op
            // renderStatusDistributionChart(); // Might need adjustment
            // document.getElementById('status-distribution-chart')?.innerHTML = `<div class="alert alert-secondary">Status distribution update based on filter not implemented.</div>`; // Placeholder


            // --- Re-render charts ---
            renderStatusDistributionChart(filteredStatusData); // Pass filtered data

            renderMarketShareChart(); // Reads from updated appState.marketShareResult
            renderOperatorComparisonChart(); // Reads from updated appState.operatorComparison
            renderUtilizationComparisonChart(); // Reads from updated appState.operatorComparison or similar
            renderSankeyChart(filteredSankeyData); // Pass filtered Sankey data
        // --- Re-render charts & metrics ---
            updateUtilizationMetrics(filteredAggMetrics);
            renderRangeUsageDetailChart(filteredRangeData); // *** Call with data ***

            showToast("Success", "Analytics refreshed.", "success", 3000);

        } else {
            throw new Error(data.error || "API request succeeded but returned no valid analytics data.");
        }

    } catch (error) {
        console.error("Error refreshing admin analytics:", error);
        showToast("Error", `Failed to refresh analytics: ${error.message}`, "danger");
        // Optionally clear charts or show error messages in their containers
        clearChartContainersOnError();
    } finally {
        // Re-enable button and reset text
        refreshButton.disabled = false;
        refreshButton.innerHTML = '<i class="fas fa-sync-alt me-1"></i> Refresh';
    }
}


// function markContentLoaded() {
    // --- NEW: Pointsphere Animation Variables ---
    let scene, camera, renderer, pointsphere, pointsphereContainer;
    let isSphereInForeground = true; // Track the state

    // --- NEW: Function to Initialize Pointsphere ---
    function initPointsphere() {
        pointsphereContainer = document.getElementById('pointsphere-container');
        if (!pointsphereContainer) {
            console.error('Pointsphere container not found.');
            return;
        }

        // Scene
        scene = new THREE.Scene();

        // Camera
        camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        camera.position.z = 2.5; // Adjusted for point sphere visibility

        // Renderer
        renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true }); // alpha: true for transparent background
        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setPixelRatio(window.devicePixelRatio);
        pointsphereContainer.appendChild(renderer.domElement);

        // Points
        const particleCount = 7000; // Number of points
        const particles = new THREE.BufferGeometry();
        const positions = new Float32Array(particleCount * 3);

        for (let i = 0; i < particleCount * 3; i += 3) {
            // Distribute points on a sphere surface
            const phi = Math.acos(-1 + (2 * Math.random()));
            const theta = Math.sqrt(particleCount * Math.PI) * phi;
            const r = 1.5; // Radius of the sphere

            positions[i] = r * Math.cos(theta) * Math.sin(phi);
            positions[i + 1] = r * Math.sin(theta) * Math.sin(phi);
            positions[i + 2] = r * Math.cos(phi);
        }
        particles.setAttribute('position', new THREE.BufferAttribute(positions, 3));

        // Material for the points
        const particleMaterial = new THREE.PointsMaterial({
            color: 0xaaaaff, // Light blue/purple color for points
            size: 0.015,      // Size of each point
            transparent: true,
            opacity: 0.7,
            blending: THREE.AdditiveBlending, // Makes points look more like lights
            // map: createParticleSprite(), // Optional: use a sprite texture for rounder points
            depthWrite: false // Important for additive blending
        });

        pointsphere = new THREE.Points(particles, particleMaterial);
        scene.add(pointsphere);
        
        // Initial animation to bring it forward and visible
        gsap.to(pointsphereContainer, { 
            duration: 1, 
            opacity: 0.5, // Initial prominent opacity
            zIndex: 0,   // Above general content, below active UI elements like navbar/modals
            delay: 0.5 
        });
        isSphereInForeground = true;


        // Handle window resize
        window.addEventListener('resize', onWindowResize, false);
        animatePointsphere();
    }

    // --- NEW: Helper for particle sprite (optional) ---
    // function createParticleSprite() {
    //     const canvas = document.createElement('canvas');
    //     canvas.width = 16;
    //     canvas.height = 16;
    //     const context = canvas.getContext('2d');
    //     const gradient = context.createRadialGradient(canvas.width / 2, canvas.height / 2, 0, canvas.width / 2, canvas.height / 2, canvas.width / 2);
    //     gradient.addColorStop(0, 'rgba(200,200,255,1)');
    //     gradient.addColorStop(0.2, 'rgba(150,150,255,0.8)');
    //     gradient.addColorStop(0.4, 'rgba(100,100,255,0.3)');
    //     gradient.addColorStop(1, 'rgba(0,0,0,0)');
    //     context.fillStyle = gradient;
    //     context.fillRect(0, 0, canvas.width, canvas.height);
    //     const texture = new THREE.CanvasTexture(canvas);
    //     return texture;
    // }

    // --- NEW: Animation Loop for Pointsphere ---
    function animatePointsphere() {
        requestAnimationFrame(animatePointsphere);
        if (pointsphere) {
            pointsphere.rotation.x += 0.0005;
            pointsphere.rotation.y += 0.001;
        }
        if (renderer && scene && camera) {
            renderer.render(scene, camera);
        }
    }

    // --- NEW: Handle Window Resize for Pointsphere ---
    function onWindowResize() {
        if (camera && renderer && pointsphereContainer) {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }
    }
    
    // --- NEW: GSAP Animations for Sphere State ---
    function animateSphereToBackground() {
        if (pointsphereContainer && isSphereInForeground) {
            gsap.to(pointsphereContainer, {
                duration: 1, // Animation duration in seconds
                opacity: 0.15, // Dim opacity
                // scale: 0.8, // Optionally scale it down
                // filter: "blur(2px)", // Optional blur effect
                zIndex: -5,   // Send to a lower z-index
                ease: "power2.inOut"
            });
            gsap.to(pointsphere.material, { duration: 1, size: 0.01, ease: "power2.inOut" }); // Make points smaller
            isSphereInForeground = false;
            console.log("Pointsphere animated to background.");
        }
    }

    function animateSphereToForeground() {
        if (pointsphereContainer && !isSphereInForeground) {
            gsap.to(pointsphereContainer, {
                duration: 1,
                opacity: 0.5, // Restore prominent opacity
                // scale: 1,
                // filter: "blur(0px)",
                zIndex: 0,
                ease: "power2.inOut"
            });
             gsap.to(pointsphere.material, { duration: 1, size: 0.015, ease: "power2.inOut" }); // Restore point size
            isSphereInForeground = true;
            console.log("Pointsphere animated to foreground.");
        }
    }



/**
 * Helper function to show loading spinners in all chart containers.
 */
function showChartLoadingIndicators() {
    document.querySelectorAll('.chart-container').forEach(container => {
        // Check if container exists and doesn't already have a spinner
        if (container && !container.querySelector('.spinner-border')) {
             container.innerHTML = `<div class="d-flex justify-content-center align-items-center h-100"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>`;
        }
    });
}

/**
 * Helper function to clear chart containers or show a generic error on fetch failure.
 */
function clearChartContainersOnError() {
    document.querySelectorAll('.chart-container').forEach(container => {
        if (container) {
            container.innerHTML = `<div class="alert alert-warning text-center">Could not load chart data.</div>`;
        }
    });
}




/**
 * Modify renderSankeyChart to accept data as an argument.
 */
async function renderSankeyChart(sankeyApiData = null) { // Accept optional data
    const containerId = 'sankey-chart'; // ID from HTML
    const container = document.getElementById(containerId);

    if (!container) {
        console.warn("Sankey chart container not found.");
        return;
    }

    // Show loading state only if data isn't passed directly
    if (!sankeyApiData) {
         container.innerHTML = `<div class="text-muted text-center p-5"><span class="spinner-border spinner-border-sm me-2"></span>Loading Sankey Diagram...</div>`;
    }

    try {
        let data;
        if (sankeyApiData) {
            // Use data passed as argument (from filtered API)
            console.log("Using provided Sankey data for rendering:", sankeyApiData);



            // Handle different data structures
            if (sankeyApiData.nodes && sankeyApiData.links) {
                // Direct structure from filtered API
                data = sankeyApiData;
                // Don't override operator and period if they exist
                if (!data.operator && sankeyApiData.operator) data.operator = sankeyApiData.operator;
                if (!data.period && sankeyApiData.period) data.period = sankeyApiData.period;
            } else if (sankeyApiData.sankeyData) {
                // Nested structure from filtered analytics API
                data = sankeyApiData.sankeyData;
                // Add operator and period if available
                if (sankeyApiData.operator) data.operator = sankeyApiData.operator;
                if (sankeyApiData.period) data.period = sankeyApiData.period;

                console.log("Extracted Sankey data:", sankeyApiData.operator, sankeyApiData.period);
            } else {
                // Unknown structure
                console.warn("Unrecognized Sankey data structure:", sankeyApiData);
                data = { nodes: [], links: [] };
            }

            // Basic validation of processed data
            if (!data || !Array.isArray(data.nodes) || !Array.isArray(data.links)) {
                console.warn("Invalid Sankey data structure after processing:", data);
                throw new Error("Provided Sankey data is invalid.");
            }

            console.log("Processed Sankey data for rendering:", data);
        } else {
            // Fetch data from the API endpoint if not provided
            console.log("Fetching Sankey data from API...");
            const response = await fetch('/e164/api/porting-activity'); // Original API call
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            const apiResponse = await response.json();
            if (!apiResponse.success || !apiResponse.nodes || !apiResponse.links) {
                 throw new Error("API returned no successful Sankey data.");
            }
             data = apiResponse; // Uses { nodes: [...], links: [...] } directly
             console.log("Sankey Data Received from API:", data);
        }


        const { nodes, links } = data;

        if (links.length === 0) {
             console.warn("Sankey: No links available to display.");
             container.innerHTML = `<div class="alert alert-info text-center">No porting activity data available to display.</div>`;
             return;
        }

        // --- D3 Sankey Rendering ---
        container.innerHTML = ''; // Clear loading/previous chart

        // --- Styling Parameters ---
        const nodeWidth = 8; // Slightly wider nodes
        const nodePadding = 9; // Increase padding between nodes vertically
        const linkOpacity = 0.3; // Adjust link transparency
        const nodeCornerRadius = 5; // Add rounded corners

        // --- Dimensions ---
        const margin = {top: 20, right: 20, bottom: 20, left: 20};
        const containerWidth = container.clientWidth || 600;
        // Adjust height calculation if needed
        const estimatedHeight = Math.max(350, nodes.length * (nodePadding + nodeWidth / 2)); // Adjust height based on padding too
        const width = containerWidth - margin.left - margin.right;
        const height = estimatedHeight - margin.top - margin.bottom;

        // --- SVG & D3 Setup ---
        const svg = d3.select(`#${containerId}`).append("svg")
            .attr("width", width + margin.left + margin.right)
            .attr("height", height + margin.top + margin.bottom)
            .attr("viewBox", `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
            .attr("style", "max-width: 100%; height: auto;");

        const g = svg.append("g")
            .attr("transform", `translate(${margin.left},${margin.top})`);

        // Create title with fallbacks for operator and period
        const operatorName = data.operator || (nodes.length > 0 ? 'Selected Operators' : 'No Operators');
        const periodLabel = data.period || 'Selected Period';

        g.append("text")
            .attr("x", width / 2)
            .attr("y", 0 - (margin.top / 2))
            .attr("text-anchor", "middle")
            .style("font-size", "16px")
            .style("font-weight", "bold")
            .style("fill", "#333")
            .text(`Porting Flow: ${operatorName} (${periodLabel})`);

        // Use a categorical color scheme
        const color = d3.scaleOrdinal(d3.schemeTableau10); // Try Tableau10 colors

        // Configure Sankey generator
        const sankey = d3.sankey()
            .nodeId(d => d.index) // Use index assigned by d3.sankey
            .nodeAlign(d3.sankeyJustify) // Justify nodes within columns
            .nodeWidth(nodeWidth)
            .nodePadding(nodePadding)
            .extent([[1, 5], [width - 1, height - 5]]); // Add small padding

        // Compute the Sankey layout
        const graph = sankey({
            // Pass copies of nodes/links for the layout engine to modify
            nodes: nodes.map(d => ({...d})),
            links: links.map(d => ({...d}))
        });

        // --- Draw Links ---
        const link = svg.append("g")
            .attr("class", "links")
            .attr("fill", "none")
            .attr("stroke-opacity", linkOpacity)
            .selectAll("path")
            .data(graph.links)
            .join("path")
            .attr("d", d3.sankeyLinkHorizontal()) // Generates the path shape
            .attr("stroke", d => color(d.source.name)) // Color link based on source node name
            .attr("stroke-width", d => Math.max(1, d.width)) // Ensure minimum width
            .sort((a, b) => b.width - a.width); // Draw thicker links first

        // Add tooltips to links
        link.append("title")
            .text(d => `${d.source.name} → ${d.target.name}\nNet Flow: ${d.value.toLocaleString()}${d.details ? `\nRanges: ${d.details}` : ''}`);

        // --- Draw Nodes ---
        const node = svg.append("g")
            .attr("class", "nodes")
            .attr("font-family", "sans-serif")
            .attr("font-size", 10)
            .selectAll("g")
            .data(graph.nodes)
            .join("g");

        // Add rectangles for nodes
        node.append("rect")
            .attr("x", d => d.x0)
            .attr("y", d => d.y0)
            .attr("height", d => Math.max(1.0, d.y1 - d.y0)) // Ensure minimum height
            .attr("width", d => d.x1 - d.x0)
            .attr("rx", nodeCornerRadius) // Rounded corners
            .attr("fill", d => color(d.name)) // Color node based on its name
            .attr("stroke", "#fff") // Add a white border for definition
            .attr("stroke-width", 0.5);

        // Add tooltips to nodes
        node.append("title")
            .text(d => `${d.name}\nTotal Flow: ${d.value.toLocaleString()}`);

        // --- Add Node Labels ---
        node.append("text")
            .attr("x", d => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6) // Position label left/right
            .attr("y", d => (d.y1 + d.y0) / 2) // Center vertically
            .attr("dy", "0.35em")
            .attr("text-anchor", d => d.x0 < width / 2 ? "start" : "end") // Align text
            .text(d => d.name)
            .attr("fill", "#333") // Darker text
            .style("font-weight", "500") // Slightly bolder
            // Add white stroke for better readability over links
            .clone(true).lower()
            .attr("stroke-linejoin", "round")
            .attr("stroke-width", 3)
            .attr("stroke", "rgba(255,255,255,0.7)"); // Semi-transparent white stroke

        console.log("Rendered Sankey Chart with styling improvements.");

    } catch (error) {
        console.error("Error rendering Sankey chart:", error);
        container.innerHTML = `<div class="alert alert-danger text-center">Error loading porting activity data: ${error.message}</div>`;
    }
}






    // =========================================================================
    // Utility Functions
    // =========================================================================

    /**
     * Displays a Bootstrap 5 Toast notification.
     * @param {string} title - The title of the toast.
     * @param {string} message - The body message of the toast.
     * @param {string} type - 'success', 'danger', 'warning', 'info' (maps to bg-* classes).
     * @param {number} delay - How long the toast should be visible (ms). Default: 5000.
     */
    function showToast(title, message, type = 'info', delay = 5000) {
        const toastContainer = document.querySelector('.toast-container');
        if (!toastContainer) {
            console.error("Toast container not found!");
            alert(`${title}: ${message}`); // Fallback to alert
            return;
        }

        const toastId = 'toast-' + Date.now();
        const bgClass = `bg-${type}`;
        // Ensure text is readable on different background colors
        const textClass = (type === 'warning' || type === 'info' || type === 'light') ? 'text-dark' : 'text-white';

        const toastHTML = `
            <div id="${toastId}" class="toast ${bgClass} ${textClass}" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="${delay}">
                <div class="toast-header ${textClass} ${bgClass}">
                    <i class="fas fa-info-circle me-2"></i> <strong class="me-auto">${title}</strong>
                    <small>Just now</small>
                    <button type="button" class="btn-close ${type === 'light' ? '' : 'btn-close-white'}" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        toastContainer.insertAdjacentHTML('beforeend', toastHTML);
        const toastElement = document.getElementById(toastId);
        if (!toastElement) return; // Guard against race conditions
        const toast = new bootstrap.Toast(toastElement);

        // Remove the toast element from DOM after it's hidden
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        toast.show();
    }

    /**
     * Prepares a container for a Chart.js chart by clearing it and adding a canvas.
     * Also destroys any existing Chart instance associated with the container.
     * @param {string} containerId - The ID of the container div.
     * @returns {HTMLCanvasElement|null} The canvas element or null if container not found.
     */
     function prepareChartContainer(containerId) {
        const container = document.getElementById(containerId);
        if (!container) {
            console.warn(`Chart container not found for: ${containerId}`);
            return null;
        }

        // Destroy previous chart instance if it exists
        if (appState.charts[containerId]) {
            try {
                appState.charts[containerId].destroy();
                console.log(`Destroyed previous chart in ${containerId}`);
            } catch (e) {
                console.warn(`Could not destroy chart in ${containerId}:`, e);
            }
            delete appState.charts[containerId]; // Remove reference
        }

        // Clear previous content (including old canvas or error messages)
        container.innerHTML = '';
        const canvas = document.createElement('canvas');
        container.appendChild(canvas);

        return canvas;
    }

    // =========================================================================
    // Tab Handling
    // =========================================================================
    // function setupTabListeners() {
    //     const tabButtons = document.querySelectorAll('#e164Tab .nav-link'); // Use button selector if they are buttons

    //     tabButtons.forEach(tab => {
    //         // Use Bootstrap's event listener for reliability if using Bootstrap's JS for tabs
    //         const tabTrigger = new bootstrap.Tab(tab); // Get or create Tab instance

    //         tab.addEventListener('shown.bs.tab', (event) => { // Listen for when tab is fully shown
    //             const targetId = event.target.getAttribute('data-bs-target')?.substring(1); // Get target ID
    //             if (!targetId) return;

    //             console.log(`Tab shown: ${targetId}`);
    //             appState.activeTab = targetId; // Update state
    //             document.body.setAttribute('data-active-tab', targetId); // Update body attribute

    //             // If the analytics tab was activated, initialize/refresh charts
    //             if (targetId === 'analytics') {
    //                 initializeAnalyticsIfActive();
    //             }

    //             // Optional: Update URL hash or query parameter without page reload
    //             try {
    //                 if (history.pushState) {
    //                     const newUrl = new URL(window.location);
    //                     newUrl.hash = targetId; // Using hash is simpler
    //                     window.history.pushState({ path: newUrl.toString() }, '', newUrl.toString());
    //                 }
    //             } catch(e) {
    //                 console.warn("Could not update browser history state:", e);
    //             }

    //             // markContentLoaded();
    //              // Hide animation when a tab is shown
    //         });
    //     });
    //     console.log("Tab listeners setup complete using Bootstrap events.");
    // }


                // --- Modify existing setupTabListeners function in e164-audit.js ---
            function setupTabListeners() {
                const tabButtons = document.querySelectorAll('#e164Tab .nav-link');
                let firstTabActivated = false; // Flag to control initial animation

                tabButtons.forEach(tab => {
                    const tabTrigger = new bootstrap.Tab(tab); 

                    tab.addEventListener('shown.bs.tab', (event) => {
                        const targetId = event.target.getAttribute('data-bs-target')?.substring(1);
                        if (!targetId) return;

                        console.log(`Tab shown: ${targetId}`);
                        appState.activeTab = targetId;
                        document.body.setAttribute('data-active-tab', targetId);

                        // --- Integrate Pointsphere Animation ---
                        // Any tab click (submission, analytics, history) sends sphere to background
                        if (targetId === 'submission' || targetId === 'analytics' || targetId === 'history') {
                            animateSphereToBackground();
                            firstTabActivated = true;
                        }
                        // --- End Pointsphere Integration ---


                        if (targetId === 'analytics') {
                            initializeAnalyticsIfActive(); // Your existing function
                        } else if (targetId === 'history') {
                            updateHistoryTable(); // Your existing function
                        }
                        // Handle other tab-specific initializations if needed
                    });
                });
                console.log("Tab listeners setup complete.");

                // If no tab is initially active by server-side class, and sphere exists, keep it prominent.
                // The Bootstrap 'shown.bs.tab' event will trigger for the initially active tab.
                // If a tab IS active on load, animateSphereToBackground will be called.
                // If NO tab is active on load (unlikely with Bootstrap), it remains prominent.
                // We also need to handle the case where a tab is *already* active on page load.
                const activeTabOnLoad = document.querySelector('#e164Tab .nav-link.active');
                if (activeTabOnLoad) {
                    console.log("Initial active tab detected on load, moving sphere to background.");
                    // Small delay to ensure animation context is ready
                    setTimeout(animateSphereToBackground, 100); 
                } else if (pointsphereContainer) {
                    console.log("No initial active tab, sphere remains in foreground.");
                    animateSphereToForeground(); // Ensure it's prominent if no tab is initially forced active
                }
            }


    // =========================================================================
    // Form Event Listeners (Only for Operators)
    // =========================================================================
    function setupFormEventListeners() {
        console.log("Setting up form event listeners...");
        const submitBtn = document.getElementById('submit-btn');
        const saveDraftBtn = document.getElementById('save-draft-btn');

        if (submitBtn) {
            submitBtn.addEventListener('click', handleSubmit);
        } else {
            console.warn("Submit button not found.");
        }

        if (saveDraftBtn) {
            saveDraftBtn.addEventListener('click', handleSaveDraft);
        } else {
             console.warn("Save Draft button not found.");
        }

        // Add listeners for operator/period changes
        const operatorSelect = document.getElementById('operator');
        if(operatorSelect) {
            operatorSelect.addEventListener('change', handleOperatorChange);
        }
        // Add listener for reporting period if needed
        // const periodSelect = document.getElementById('reporting-period');
        // if(periodSelect) { periodSelect.addEventListener('change', handlePeriodChange); }
    }

    function setupRangeModalListeners() {
        const addRangeBtn = document.getElementById('add-range-btn');
        const saveRangeBtn = document.getElementById('save-range-btn');
        const rangeModalEl = document.getElementById('addRangeModal');

        if (!addRangeBtn || !saveRangeBtn || !rangeModalEl) {
             console.warn("Range modal elements not found.");
             return;
        }
        // Ensure modal instance is created correctly
        const rangeModal = bootstrap.Modal.getOrCreateInstance(rangeModalEl);

        addRangeBtn.addEventListener('click', () => {
            const rangeForm = document.getElementById('rangeForm');
            if (rangeForm) rangeForm.reset(); // Reset form
            document.getElementById('range-index').value = ''; // Clear index
            document.getElementById('range-validation-error')?.classList.add('d-none'); // Hide error
            populateNdcDropdown(); // Ensure NDC dropdown is populated/cleared
            rangeModal.show();
        });

        saveRangeBtn.addEventListener('click', handleSaveRange);

        // Add listeners for automatic calculation
        const startBlock = document.getElementById('start-block');
        const endBlock = document.getElementById('end-block');
        const activeSub = document.getElementById('active-subscriber');
        const activeNonSub = document.getElementById('active-non-subscriber');
        const inactive = document.getElementById('inactive');
        const reserved = document.getElementById('reserved');

        [startBlock, endBlock].forEach(el => el?.addEventListener('input', calculateTotalAllocated));
        [startBlock, endBlock, activeSub, activeNonSub, inactive, reserved].forEach(el => el?.addEventListener('input', validateRangeNumbers));
    }

     function setupPortingModalListeners() {
        const addPortingBtn = document.getElementById('add-porting-btn');
        const savePortingBtn = document.getElementById('save-porting-btn');
        const portingModalEl = document.getElementById('addPortingModal');

        if (!addPortingBtn || !savePortingBtn || !portingModalEl) {
             console.warn("Porting modal elements not found.");
             return;
        }
        const portingModal = bootstrap.Modal.getOrCreateInstance(portingModalEl);

        addPortingBtn.addEventListener('click', () => {
            const portingForm = document.getElementById('portingForm');
            if(portingForm) portingForm.reset();
            portingModal.show();
        });

        savePortingBtn.addEventListener('click', handleSavePortingRecord);
    }

    function setupImportModalListeners() {
        const importPortingBtn = document.getElementById('import-porting-btn');
        const importCsvBtn = document.getElementById('import-porting-csv-btn');
        const importModalEl = document.getElementById('importPortingModal');

         if (!importPortingBtn || !importCsvBtn || !importModalEl) {
             console.warn("Import modal elements not found.");
             return;
         }
        const importModal = bootstrap.Modal.getOrCreateInstance(importModalEl);

        importPortingBtn.addEventListener('click', () => {
            const fileInput = document.getElementById('porting-csv');
            if (fileInput) fileInput.value = ''; // Clear file input
            importModal.show();
        });

        importCsvBtn.addEventListener('click', handleImportPortingCsv);
    }

    function setupPreviewModalListeners() {
        const previewBtn = document.getElementById('preview-btn');
        const previewModalEl = document.getElementById('previewModal');

        if (!previewBtn || !previewModalEl) {
             console.warn("Preview modal elements not found.");
             return;
        }
        const previewModal = bootstrap.Modal.getOrCreateInstance(previewModalEl);

        previewBtn.addEventListener('click', () => {
            generatePreview(); // Function to generate content
            previewModal.show();
        });
    }

    function setupHistoryTableListeners() {
        const historyTableBody = document.querySelector('#history-table tbody');
        if (historyTableBody) {
             historyTableBody.addEventListener('click', (event) => {
                 const viewButton = event.target.closest('.view-submission-btn');
                 const editButton = event.target.closest('.edit-submission-btn'); // Example

                 if (viewButton) {
                     const submissionId = viewButton.dataset.id;
                     console.log("View submission clicked:", submissionId);
                     // Potentially navigate or open a modal
                     window.location.href = `/e164/submission/view/${submissionId}`; // Example navigation
                 } else if (editButton) {
                     const submissionId = editButton.dataset.id;
                     console.log("Edit submission clicked:", submissionId);
                      window.location.href = `/e164/submission/edit/${submissionId}`; // Example navigation
                 }
             });
        }
    }

    // =========================================================================
    // Operator and NDC Handling
    // =========================================================================
    /** Populates operator dropdowns based on appState.allOperators. */
     function populateOperatorDropdowns() {
        console.log("Populating operator dropdowns...");
        const operatorSelects = [
            document.getElementById('operator'),
            document.getElementById('porting-source-operator'),
            document.getElementById('porting-target-operator')
        ];
        const operators = appState.allOperators || [];

        operatorSelects.forEach(select => {
            if (!select) return;
            const currentVal = select.value; // Preserve selection if possible
            const firstOption = select.options[0]; // Keep placeholder
            select.innerHTML = '';
            if (firstOption && firstOption.value === "") { select.appendChild(firstOption); }

            operators.forEach(op => {
                const option = new Option(op.name, op.id);
                select.add(option);
            });
            select.value = currentVal; // Restore selection
        });
         console.log(`Populated ${operators.length} operators.`);
    }

    /** Handles change event on the main operator dropdown. Fetches NDCs. */
    async function handleOperatorChange() {
        const operatorSelect = document.getElementById('operator');
        const operatorId = operatorSelect?.value;
        appState.ranges = []; // Clear ranges when operator changes
        appState.portingData = []; // Clear porting data
        updateRangeTable();
        updatePortingTable();
        if (!operatorId) {
            clearNdcDropdown();
            return;
        }
        console.log(`Operator changed to: ${operatorId}`);
        await fetchAndPopulateNdcs(operatorId);
    }

    /** Fetches NDC data for a given operator ID (using cache). */
    async function fetchNdcsForOperator(operatorId) {
        if (appState.operatorNdcCache[operatorId]) {
            console.log(`Using cached NDCs for operator ${operatorId}`);
            return appState.operatorNdcCache[operatorId];
        }
        console.log(`Fetching NDCs for operator ${operatorId}...`);
        try {
            // IMPORTANT: Ensure this API route exists and returns {success: true, ndcs: [...]}
            const response = await fetch(`/e164/api/operator/${operatorId}/ndcs`);
            if (!response.ok) {
                throw new Error(`Failed to fetch NDCs: ${response.status} ${response.statusText}`);
            }
            const data = await response.json();
            if (data.success && Array.isArray(data.ndcs)) {
                appState.operatorNdcCache[operatorId] = data.ndcs;
                console.log(`Fetched NDCs:`, data.ndcs);
                return data.ndcs;
            } else {
                throw new Error(data.error || 'Invalid response format for NDCs');
            }
        } catch (error) {
            console.error(`Error fetching NDCs for operator ${operatorId}:`, error);
            showToast("Error", `Could not fetch NDCs for the selected operator. Please ensure the API route exists and is working.`, "danger");
            appState.operatorNdcCache[operatorId] = [];
            return [];
        }
    }

    /** Fetches NDCs and populates the NDC dropdown in the range modal. */
    async function fetchAndPopulateNdcs(operatorId) {
        const ndcs = await fetchNdcsForOperator(operatorId);
        populateNdcDropdown(ndcs);
    }

     /** Populates the NDC dropdown in the range modal. */
    function populateNdcDropdown(ndcs = []) {
        const ndcSelect = document.getElementById('ndc'); // In range modal
        if (!ndcSelect) return;
        const currentVal = ndcSelect.value;
        const firstOption = ndcSelect.options[0]; // Keep placeholder
        ndcSelect.innerHTML = '';
        if (firstOption && firstOption.value === "") { ndcSelect.appendChild(firstOption); }

        ndcs.forEach(ndc => {
            const option = new Option(ndc, ndc);
            ndcSelect.add(option);
        });
        ndcSelect.value = currentVal; // Restore selection if possible
        console.log(`Populated NDC dropdown with ${ndcs.length} NDCs.`);
    }

    /** Clears the NDC dropdown. */
    function clearNdcDropdown() {
         populateNdcDropdown([]);
    }

    // =========================================================================
    // Number Range Handling
    // =========================================================================
    /** Calculates total allocated numbers based on start/end blocks. */
    function calculateTotalAllocated() {
        const startBlockEl = document.getElementById('start-block');
        const endBlockEl = document.getElementById('end-block');
        const totalAllocatedEl = document.getElementById('total-allocated');
        if (!startBlockEl || !endBlockEl || !totalAllocatedEl) return;
        const start = parseInt(startBlockEl.value, 10);
        const end = parseInt(endBlockEl.value, 10);
        if (!isNaN(start) && !isNaN(end) && start >= 0 && end >= start) {
            const total = (end - start + 1) * 1000;
            totalAllocatedEl.value = total.toLocaleString();
        } else {
            totalAllocatedEl.value = '';
        }
        validateRangeNumbers(); // Validate after calculation
    }

    /** Validates that the sum of usage fields equals total allocated. */
    function validateRangeNumbers() {
         const totalAllocatedEl = document.getElementById('total-allocated');
         const activeSubEl = document.getElementById('active-subscriber');
         const activeNonSubEl = document.getElementById('active-non-subscriber');
         const inactiveEl = document.getElementById('inactive');
         const reservedEl = document.getElementById('reserved');
         const errorDiv = document.getElementById('range-validation-error');
         const saveBtn = document.getElementById('save-range-btn');
         if (!totalAllocatedEl || !activeSubEl || !activeNonSubEl || !inactiveEl || !reservedEl || !errorDiv || !saveBtn) return;

         const totalAllocated = parseInt(totalAllocatedEl.value.replace(/,/g, ''), 10);
         const activeSub = parseInt(activeSubEl.value, 10) || 0;
         const activeNonSub = parseInt(activeNonSubEl.value, 10) || 0;
         const inactive = parseInt(inactiveEl.value, 10) || 0;
         const reserved = parseInt(reservedEl.value, 10) || 0;

         let isValid = true;
         if (isNaN(totalAllocated)) {
             errorDiv.classList.add('d-none');
             isValid = false; // Cannot save if total is invalid
         } else {
             const sum = activeSub + activeNonSub + inactive + reserved;
             if (sum !== totalAllocated) {
                 errorDiv.textContent = `Usage numbers sum (${sum.toLocaleString()}) must equal Total Allocated (${totalAllocated.toLocaleString()}). Difference: ${(totalAllocated - sum).toLocaleString()}`;
                 errorDiv.classList.remove('d-none');
                 isValid = false;
             } else {
                 errorDiv.classList.add('d-none');
             }
         }
         saveBtn.disabled = !isValid; // Enable/disable save button
    }

    /** Handles saving or updating a number range from the modal. */
function handleSaveRange() {
        console.log("Attempting to save range...");
        const index = document.getElementById('range-index').value;
        const rangeData = {
            ndc: document.getElementById('ndc')?.value,
            type: document.getElementById('range-type')?.value,
            start_block: parseInt(document.getElementById('start-block')?.value, 10),
            end_block: parseInt(document.getElementById('end-block')?.value, 10),
            total_allocated: parseInt(document.getElementById('total-allocated')?.value.replace(/,/g, ''), 10),
            active_subscriber: parseInt(document.getElementById('active-subscriber')?.value, 10) || 0,
            active_non_subscriber: parseInt(document.getElementById('active-non-subscriber')?.value, 10) || 0,
            inactive: parseInt(document.getElementById('inactive')?.value, 10) || 0,
            reserved: parseInt(document.getElementById('reserved')?.value, 10) || 0,
        };

        // Validation
        if (!rangeData.ndc || !rangeData.type || isNaN(rangeData.start_block) || isNaN(rangeData.end_block) || rangeData.start_block < 0 || rangeData.end_block < rangeData.start_block) {
            showToast("Validation Error", "Please fill range fields correctly.", "warning"); return;
        }
        calculateTotalAllocated(); validateRangeNumbers(); // Re-validate
        if(document.getElementById('save-range-btn').disabled) {
             showToast("Validation Error", "Usage numbers don't match Total Allocated.", "warning"); return;
        }

        // Add or Update
        if (index === '') {
            appState.ranges.push(rangeData);
            showToast("Success", `Range for NDC ${rangeData.ndc} added.`, "success");
        } else {
            appState.ranges[parseInt(index, 10)] = rangeData;
            showToast("Success", `Range for NDC ${rangeData.ndc} updated.`, "success");
        }
        updateRangeTable();
        bootstrap.Modal.getInstance(document.getElementById('addRangeModal'))?.hide();
    }

    /** Updates the number ranges table in the UI. */
function updateRangeTable() {
        const tableBody = document.querySelector('#number-ranges-table tbody');
        if (!tableBody) return;
        tableBody.innerHTML = '';
        if (appState.ranges.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="10" class="text-center text-muted p-4">No number ranges added.</td></tr>'; return;
        }
        appState.ranges.forEach((range, index) => {
            const row = tableBody.insertRow(); // Use insertRow for better performance
            row.innerHTML = `
                <td>${range.ndc ?? ''}</td>
                <td>${range.type ?? ''}</td>
                <td>${range.start_block ?? ''}</td>
                <td>${range.end_block ?? ''}</td>
                <td>${range.total_allocated?.toLocaleString() ?? 'N/A'}</td>
                <td>${range.active_subscriber?.toLocaleString() ?? '0'}</td>
                <td>${range.active_non_subscriber?.toLocaleString() ?? '0'}</td>
                <td>${range.inactive?.toLocaleString() ?? '0'}</td>
                <td>${range.reserved?.toLocaleString() ?? '0'}</td>
                <td>
                    <button class="btn btn-sm btn-outline-primary edit-range-btn" data-index="${index}" title="Edit Range"><i class="fas fa-edit"></i></button>
                    <button class="btn btn-sm btn-outline-danger delete-range-btn" data-index="${index}" title="Delete Range"><i class="fas fa-trash"></i></button>
                </td>`;
        });
        addRangeTableButtonListeners(); // Re-attach listeners
    }

    /** Adds event listeners for edit/delete buttons in the ranges table. */
function addRangeTableButtonListeners() {
        document.querySelectorAll('.edit-range-btn').forEach(button => {
             // Remove old listener before adding new one to prevent duplicates
             button.replaceWith(button.cloneNode(true));
             document.querySelector(`.edit-range-btn[data-index="${button.dataset.index}"]`)?.addEventListener('click', handleEditRange);
        });
        document.querySelectorAll('.delete-range-btn').forEach(button => {
             button.replaceWith(button.cloneNode(true));
             document.querySelector(`.delete-range-btn[data-index="${button.dataset.index}"]`)?.addEventListener('click', handleDeleteRange);
        });
    }

    /** Handles clicking the edit button for a range. */
function handleEditRange(event) {
        const index = parseInt(event.currentTarget.dataset.index, 10);
        const range = appState.ranges[index];
        if (!range) return;
        // Populate modal form
        document.getElementById('range-index').value = index;
        document.getElementById('ndc').value = range.ndc;
        document.getElementById('range-type').value = range.type;
        document.getElementById('start-block').value = range.start_block;
        document.getElementById('end-block').value = range.end_block;
        document.getElementById('active-subscriber').value = range.active_subscriber ?? 0;
        document.getElementById('active-non-subscriber').value = range.active_non_subscriber ?? 0;
        document.getElementById('inactive').value = range.inactive ?? 0;
        document.getElementById('reserved').value = range.reserved ?? 0;
        calculateTotalAllocated(); // Calculate total allocated for display
        document.getElementById('range-validation-error')?.classList.add('d-none');
        document.getElementById('save-range-btn').disabled = false; // Enable save button
        bootstrap.Modal.getOrCreateInstance(document.getElementById('addRangeModal')).show();
    }

    /** Handles clicking the delete button for a range. */
function handleDeleteRange(event) {
        const index = parseInt(event.currentTarget.dataset.index, 10);
        const range = appState.ranges[index];
        if (confirm(`Delete range for NDC ${range?.ndc}?`)) {
            appState.ranges.splice(index, 1);
            updateRangeTable();
            showToast("Success", "Number range deleted.", "success");
        }
    }

    // =========================================================================
    // Porting Data Handling
    // =========================================================================
    /** Handles saving a porting record from the modal. */
function handleSavePortingRecord() {
        console.log("Attempting to save porting record...");
        const record = {
            ndc: document.getElementById('porting-ndc')?.value.trim(),
            range_type: document.getElementById('porting-range-type')?.value,
            source_operator: document.getElementById('porting-source-operator')?.value, // ID
            target_operator: document.getElementById('porting-target-operator')?.value, // ID
            count: parseInt(document.getElementById('porting-count')?.value, 10)
        };

        // Validation
        if (!record.ndc || !record.range_type || !record.source_operator || !record.target_operator || isNaN(record.count) || record.count <= 0) {
            showToast("Validation Error", "Please fill all porting fields correctly.", "warning"); return;
        }
        if (record.source_operator === record.target_operator) {
             showToast("Validation Error", "Source and Target operator cannot be the same.", "warning"); return;
        }

        // Get names for display/state
        const sourceName = appState.allOperators.find(op => op.id === record.source_operator)?.name || record.source_operator;
        const targetName = appState.allOperators.find(op => op.id === record.target_operator)?.name || record.target_operator;

        // Add to state (include names for easy display)
        appState.portingData.push({
            ...record,
            source_operator_name: sourceName,
            target_operator_name: targetName
        });
        showToast("Success", `Porting record added (${sourceName} -> ${targetName}).`, "success");
        updatePortingTable();
        bootstrap.Modal.getInstance(document.getElementById('addPortingModal'))?.hide();
    }

    /** Updates the porting data table in the UI. */
function updatePortingTable() {
        const tableBody = document.querySelector('#porting-data-table tbody');
        if (!tableBody) return;
        tableBody.innerHTML = '';
        if (appState.portingData.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="6" class="text-center text-muted p-4">No porting records added.</td></tr>'; return;
        }
        appState.portingData.forEach((record, index) => {
            const row = tableBody.insertRow();
            row.innerHTML = `
                <td>${record.ndc ?? ''}</td>
                <td>${record.range_type ?? ''}</td>
                <td>${record.source_operator_name ?? ''}</td>
                <td>${record.target_operator_name ?? ''}</td>
                <td>${record.count?.toLocaleString() ?? ''}</td>
                <td>
                    <button class="btn btn-sm btn-outline-danger delete-porting-btn" data-index="${index}" title="Delete Record"><i class="fas fa-trash"></i></button>
                </td>`;
        });
        addPortingTableButtonListeners(); // Re-attach listeners
    }

     /** Adds event listeners for delete buttons in the porting table. */
function addPortingTableButtonListeners() {
        document.querySelectorAll('.delete-porting-btn').forEach(button => {
            button.replaceWith(button.cloneNode(true)); // Prevent duplicate listeners
            document.querySelector(`.delete-porting-btn[data-index="${button.dataset.index}"]`)?.addEventListener('click', handleDeletePortingRecord);
        });
    }

    /** Handles clicking the delete button for a porting record. */
function handleDeletePortingRecord(event) {
        const index = parseInt(event.currentTarget.dataset.index, 10);
        const record = appState.portingData[index];
        if (confirm(`Delete porting record (${record?.source_operator_name} -> ${record?.target_operator_name})?`)) {
            appState.portingData.splice(index, 1);
            updatePortingTable();
            showToast("Success", "Porting record deleted.", "success");
        }
    }

    /** Handles importing porting data from a CSV file. */
function handleImportPortingCsv() {
        const fileInput = document.getElementById('porting-csv');
        if (!fileInput || !fileInput.files || fileInput.files.length === 0) {
            showToast("Error", "Please select a CSV file.", "warning"); return;
        }
        const file = fileInput.files[0];
        if (!file.type.includes('csv') && !file.name.toLowerCase().endsWith('.csv')) {
            showToast("Error", "Invalid file type. Please select a CSV file.", "danger"); return;
        }

        const reader = new FileReader();
        reader.onload = function(event) {
            try {
                const csv = event.target.result;
                const lines = csv.split(/\r?\n/);
                if (lines.length < 2) throw new Error("CSV is empty or has no data rows.");

                const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
                const requiredHeaders = ['ndc', 'rangetype', 'sourceoperator', 'targetoperator', 'count'];
                const headerIndices = {};
                requiredHeaders.forEach(reqHeader => {
                    let index = headers.indexOf(reqHeader);
                    // Check common alternatives
                    if (index === -1) {
                        if (reqHeader === 'rangetype') index = headers.indexOf('type');
                        if (reqHeader === 'sourceoperator') index = headers.indexOf('source');
                        if (reqHeader === 'targetoperator') index = headers.indexOf('target');
                    }
                    if (index === -1) throw new Error(`Missing required CSV header: ${reqHeader}`);
                    headerIndices[reqHeader] = index;
                });

                const importedData = [];
                const operatorNameMap = appState.allOperators.reduce((map, op) => { map[op.name.toLowerCase()] = op.id; return map; }, {});

                for (let i = 1; i < lines.length; i++) {
                    const line = lines[i].trim();
                    if (!line) continue;
                    const fields = line.split(',').map(f => f.trim());
                    if (fields.length !== headers.length) { console.warn(`Row ${i + 1}: Skipping due to incorrect field count.`); continue; }

                    const count = parseInt(fields[headerIndices['count']], 10);
                    if (isNaN(count) || count <= 0) { console.warn(`Row ${i + 1}: Skipping due to invalid count.`); continue; }

                    const sourceName = fields[headerIndices['sourceoperator']];
                    const targetName = fields[headerIndices['targetoperator']];
                    const sourceId = operatorNameMap[sourceName.toLowerCase()];
                    const targetId = operatorNameMap[targetName.toLowerCase()];

                    if (!sourceId) { console.warn(`Row ${i + 1}: Skipping due to unknown source operator "${sourceName}".`); continue; }
                    if (!targetId) { console.warn(`Row ${i + 1}: Skipping due to unknown target operator "${targetName}".`); continue; }
                    if (sourceId === targetId) { console.warn(`Row ${i + 1}: Skipping because source and target are the same.`); continue; }

                    const record = {
                        ndc: fields[headerIndices['ndc']],
                        range_type: fields[headerIndices['rangetype']],
                        source_operator: sourceId, // Store ID
                        target_operator: targetId, // Store ID
                        source_operator_name: sourceName,
                        target_operator_name: targetName,
                        count: count
                    };
                    if (!record.ndc || !record.range_type) { console.warn(`Row ${i + 1}: Skipping due to missing NDC or RangeType.`); continue; }
                    importedData.push(record);
                }

                if (importedData.length === 0) { showToast("Info", "No valid records found in CSV.", "info"); return; }
                if (confirm(`Found ${importedData.length} valid records. Add them?`)) {
                    appState.portingData = appState.portingData.concat(importedData);
                    updatePortingTable();
                    showToast("Success", `${importedData.length} records imported.`, "success");
                    bootstrap.Modal.getInstance(document.getElementById('importPortingModal'))?.hide();
                }
            } catch (error) {
                console.error("Error parsing CSV:", error);
                showToast("Import Error", `Error processing CSV: ${error.message}`, "danger");
            } finally { fileInput.value = ''; } // Reset file input
        };
        reader.onerror = () => { showToast("Import Error", "Could not read the file.", "danger"); fileInput.value = ''; };
        reader.readAsText(file);
    }

    // =========================================================================
    // Preview Generation
    // =========================================================================
function generatePreview() {
        const previewContent = document.getElementById('preview-content');
        if (!previewContent) return;
        previewContent.innerHTML = '<p class="text-center text-muted p-3">Generating preview...</p>';

        const operatorSelect = document.getElementById('operator');
        const periodSelect = document.getElementById('reporting-period');
        const contactEl = document.getElementById('submission-contact');
        const emailEl = document.getElementById('submission-email');
        const notesEl = document.getElementById('submission-notes');

        const operatorName = operatorSelect?.options[operatorSelect.selectedIndex]?.text || 'N/A';
        const reportingPeriod = periodSelect?.options[periodSelect.selectedIndex]?.text || 'N/A';
        const contactPerson = contactEl?.value || 'N/A';
        const contactEmail = emailEl?.value || 'N/A';
        const notes = notesEl?.value || 'None';

        // Helper to escape HTML
        const escapeHtml = (unsafe) => unsafe.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;").replace(/"/g, "&quot;").replace(/'/g, "&#039;");

        let html = `
            <h4>Submission Summary</h4><hr>
            <dl class="row">
                <dt class="col-sm-3">Operator:</dt><dd class="col-sm-9">${escapeHtml(operatorName)}</dd>
                <dt class="col-sm-3">Period:</dt><dd class="col-sm-9">${escapeHtml(reportingPeriod)}</dd>
                <dt class="col-sm-3">Contact:</dt><dd class="col-sm-9">${escapeHtml(contactPerson)}</dd>
                <dt class="col-sm-3">Email:</dt><dd class="col-sm-9">${escapeHtml(contactEmail)}</dd>
                <dt class="col-sm-3">Notes:</dt><dd class="col-sm-9">${escapeHtml(notes).replace(/\n/g, '<br>')}</dd>
            </dl><hr>`;

        // Ranges Table
        if (appState.ranges.length > 0) {
            html += `<h5>Number Ranges (${appState.ranges.length})</h5><div class="table-responsive mb-4"><table class="table table-sm table-bordered"><thead><tr>
                        <th>NDC</th><th>Type</th><th>Start</th><th>End</th><th>Allocated</th><th>Active Sub</th><th>Active Non-Sub</th><th>Inactive</th><th>Reserved</th>
                     </tr></thead><tbody>`;
            appState.ranges.forEach(r => {
                html += `<tr>
                            <td>${escapeHtml(r.ndc)}</td><td>${escapeHtml(r.type)}</td><td>${r.start_block}</td><td>${r.end_block}</td>
                            <td>${r.total_allocated?.toLocaleString() ?? ''}</td><td>${r.active_subscriber?.toLocaleString() ?? ''}</td>
                            <td>${r.active_non_subscriber?.toLocaleString() ?? ''}</td><td>${r.inactive?.toLocaleString() ?? ''}</td><td>${r.reserved?.toLocaleString() ?? ''}</td>
                         </tr>`;
            });
            html += `</tbody></table></div>`;
        } else { html += `<p class="text-muted">No number ranges.</p>`; }

        // Porting Table
        if (appState.portingData.length > 0) {
            html += `<h5>Porting Data (${appState.portingData.length})</h5><div class="table-responsive"><table class="table table-sm table-bordered"><thead><tr>
                        <th>NDC</th><th>Type</th><th>Source</th><th>Target</th><th>Count</th>
                     </tr></thead><tbody>`;
            appState.portingData.forEach(p => {
                 html += `<tr>
                            <td>${escapeHtml(p.ndc)}</td><td>${escapeHtml(p.range_type)}</td><td>${escapeHtml(p.source_operator_name)}</td>
                            <td>${escapeHtml(p.target_operator_name)}</td><td>${p.count.toLocaleString()}</td>
                          </tr>`;
            });
            html += `</tbody></table></div>`;
        } else { html += `<p class="text-muted">No porting data.</p>`; }

        previewContent.innerHTML = html;
    }

    // =========================================================================
    // Form Submission
    // =========================================================================
    /** Handles saving the form as a draft. */
function handleSaveDraft(event) {
         event.preventDefault();
         console.log("Save Draft clicked.");
         showToast("Info", "Save Draft not implemented.", "info");
         // TODO: Implement draft saving
    }

    /** Handles submitting the form to the backend. */
async function handleSubmit(event) {
        event.preventDefault();
        console.log("Submit clicked...");

        // Validation
        const operatorSelect = document.getElementById('operator');
        const periodSelect = document.getElementById('reporting-period');
        const operatorId = operatorSelect?.value;
        const operatorName = operatorSelect?.options[operatorSelect.selectedIndex]?.text;
        const reportingPeriod = periodSelect?.value;
        if (!operatorId || !reportingPeriod) { showToast("Validation Error", "Select Operator and Period.", "warning"); return; }
        if (appState.ranges.length === 0) { showToast("Validation Error", "Add at least one Number Range.", "warning"); return; }

        // Prepare Data (ensure porting data uses IDs if backend expects them)
        const finalPortingData = appState.portingData.map(p => ({
             ndc: p.ndc, range_type: p.range_type, source_operator: p.source_operator, // ID
             target_operator: p.target_operator, // ID
             count: p.count
        }));
        const submissionData = {
            operator_id: operatorId, operator: operatorName, reporting_period: reportingPeriod,
            contact_person: document.getElementById('submission-contact')?.value || '',
            contact_email: document.getElementById('submission-email')?.value || '',
            notes: document.getElementById('submission-notes')?.value || '',
            ranges: appState.ranges, porting_data: finalPortingData
        };
        console.log("Submitting Data:", JSON.stringify(submissionData).substring(0, 500) + "..."); // Log snippet

        // Submit to Server
        const submitBtn = document.getElementById('submit-btn');
        const originalBtnText = submitBtn.innerHTML;
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Submitting...';

        try {
            const response = await fetch('/e164/submission/create', { // Verify endpoint
                method: 'POST', headers: {'Content-Type': 'application/json'},
                body: JSON.stringify(submissionData)
            });
            const result = await response.json();
            if (!response.ok) throw new Error(result.error || `HTTP error ${response.status}`);
            if (!result.success) throw new Error(result.error || 'Submission failed.');

            showToast("Success", "Submission created successfully!", "success", 7000);
            if (result.redirect_url) {
                setTimeout(() => { window.location.href = result.redirect_url; }, 1500);
            } else {
                // Reset form state
                appState.ranges = []; appState.portingData = [];
                updateRangeTable(); updatePortingTable();
                const form = document.getElementById('submission');
                form?.querySelectorAll('input:not([type=button]):not([type=submit]), select, textarea').forEach(el => { el.value = ''; });
                if(operatorSelect) operatorSelect.selectedIndex = 0;
                if(periodSelect) periodSelect.selectedIndex = 0;
                submitBtn.innerHTML = originalBtnText; submitBtn.disabled = false;
            }
        } catch (error) {
            console.error('Submission Error:', error);
            showToast("Submission Error", `Error: ${error.message}`, "danger", 10000);
            submitBtn.innerHTML = originalBtnText; submitBtn.disabled = false; // Restore button
        }
    }

    // =========================================================================
    // Analytics / Charting (Only Sankey shown for brevity, others assumed complete)
    // =========================================================================

/** Checks if analytics tab is active and initializes charts appropriately for role. */
function initializeAnalyticsIfActive() {
    const analyticsPane = document.getElementById('analytics');
    // Check based on the active tab state variable or class on pane
    const isActive = appState.activeTab === 'analytics' || analyticsPane?.classList.contains('active');
    console.log("[Analytics] Checking if analytics tab is active:", isActive);

    if (isActive) {
        console.log("[Analytics] Analytics tab is active. Initializing components.");

        if (appState.isAdmin) {
            // ADMIN VIEW: Trigger initial data load using filters ('latest', 'all')
            console.log("Admin view: Triggering initial data refresh for default filters.");
            showChartLoadingIndicators();
            handleAdminAnalyticsRefresh(); // This fetches data AND calls all necessary render functions

        } else {
            // OPERATOR VIEW: Initialize using pre-loaded data
            console.log("Operator view: Initializing charts from pre-loaded operator data.");
            updateUtilizationMetrics();      // Uses appState.latestData/historicalData
            renderTrendChart();              // Uses appState.chartData (op specific)
            renderStatusDistributionChart(); // Uses appState.latestData
            renderMarketShareChart();      // Uses appState.marketShareResult (op specific)
            renderSankeyChart();           // Fetches op-specific data or uses initial if available
            // DO NOT call admin-specific charts like renderRangeUsageDetailChart here
        }
        console.log("Analytics components initialization triggered.");
        // setTimeout(markContentLoaded, 1000);
         // Example: hide after 1 second

    } else {
        console.log("[Analytics] Analytics tab is not active.");
    }
}





/**
 * Renders the Range Usage Detail chart (Stacked Horizontal Bar).
 * Displays Active (Sub/Non-Sub), Inactive, and Reserved numbers for each range.
 * Dynamically adjusts canvas height based on the number of ranges provided.
 * Expects rangeData to be provided by the calling function (e.g., handleAdminAnalyticsRefresh).
 * @param {Array|null} rangeData - Array of range detail objects from the API, or null/undefined.
 * Expected object structure: { ndc, type, total_allocated,
 * active_subscriber, active_non_subscriber, inactive, reserved }
 */
function renderRangeUsageDetailChart(rangeData = null) {
    const containerId = 'range-usage-detail-chart'; // ID from HTML
    const container = document.getElementById(containerId); // Get container div

    // Use helper to get/prepare canvas (destroys previous chart if exists)
    const canvas = prepareChartContainer(containerId);
    if (!canvas || !container) {
        console.warn(`Range Usage Chart: Container #${containerId} or canvas could not be prepared.`);
        return; // Exit if container/canvas setup failed
    }

    // Handle case where no data is passed or data is empty
    if (!rangeData || !Array.isArray(rangeData) || rangeData.length === 0) {
        console.log("Range Usage Chart: No data provided or data is empty. Displaying message.");
        container.innerHTML = '<div class="alert alert-info text-center p-4">No range usage data available for the selected filters. Use filters and click Refresh.</div>';
        // Reset container height if previously set
        container.style.height = 'auto'; // Or a default small height like '100px'
        return;
    }

    console.log(`Rendering Range Usage Detail chart with ${rangeData.length} ranges.`);

    // --- Dynamic Height Calculation ---
    const pixelsPerRange = 28; // Adjust for desired spacing
    const baseChartHeight = 100; // Base height for axes, legend, title
    const minChartHeight = 300; // Minimum chart height

    const calculatedHeight = baseChartHeight + (rangeData.length * pixelsPerRange);
    const desiredHeight = Math.max(minChartHeight, calculatedHeight);

    container.style.height = `${desiredHeight}px`;
    canvas.style.height = `${desiredHeight}px`;
    console.log(`Set range usage chart container/canvas height to ${desiredHeight}px.`);
    // --- End Dynamic Height ---

    // Prepare data for Chart.js
    const labels = rangeData.map(r => `${r.ndc || 'N/A'} (${r.type || 'N/A'})`);
    const activeSubData = rangeData.map(r => r.active_subscriber || 0);
    const activeNonSubData = rangeData.map(r => r.active_non_subscriber || 0);
    const inactiveData = rangeData.map(r => r.inactive || 0);
    const reservedData = rangeData.map(r => r.reserved || 0);
    // Data needed for tooltips
    const totalAllocatedData = rangeData.map(r => r.total_allocated || 0);
    const remainingData = rangeData.map(r => {
        const totalAllocated = r.total_allocated || 0;
        const used = (r.active_subscriber || 0) + (r.active_non_subscriber || 0) + (r.inactive || 0) + (r.reserved || 0);
        return Math.max(0, totalAllocated - used);
    });

    const datasets = [
        { label: 'Active Subscriber', data: activeSubData, backgroundColor: 'rgba(75, 192, 192, 0.7)', borderColor: 'rgb(75, 192, 192)', borderWidth: 1 },
        { label: 'Active Non-Subscriber', data: activeNonSubData, backgroundColor: 'rgba(54, 162, 235, 0.7)', borderColor: 'rgb(54, 162, 235)', borderWidth: 1 },
        { label: 'Inactive', data: inactiveData, backgroundColor: 'rgba(255, 159, 64, 0.7)', borderColor: 'rgb(255, 159, 64)', borderWidth: 1 },
        { label: 'Reserved', data: reservedData, backgroundColor: 'rgba(153, 102, 255, 0.7)', borderColor: 'rgb(153, 102, 255)', borderWidth: 1 }
        // Optional 'Remaining' dataset can be added here if desired
    ];

    // Create Chart instance
    appState.charts[containerId] = new Chart(canvas, {
        type: 'bar',
        data: { labels: labels, datasets: datasets },
        options: {
            indexAxis: 'y', // Horizontal bars
            responsive: true,
            maintainAspectRatio: false, // Allows height override
            plugins: {
                title: { display: true, text: 'Number Range Usage Breakdown by Category' },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) label += ': ';
                            if (context.parsed.x !== null) label += context.parsed.x.toLocaleString();
                            return label;
                        },
                        footer: function(tooltipItems) {
                            const index = tooltipItems[0]?.dataIndex;
                            if (index !== undefined && index < totalAllocatedData.length) {
                                const total = totalAllocatedData[index];
                                const remaining = remainingData[index];
                                // Calculate total used for verification
                                const used = tooltipItems.reduce((sum, item) => sum + (item.parsed.x || 0), 0);
                                return `Used: ${used.toLocaleString()}\nRemaining: ${remaining.toLocaleString()}\nTotal Allocated: ${total.toLocaleString()}`;
                            }
                            return '';
                        }
                    }
                },
                legend: { position: 'top' }
            },
            scales: {
                x: { stacked: true, title: { display: true, text: 'Number Count' } },
                y: { stacked: true }
            },
            // Optional performance tweaks
            animation: { duration: 0 },
            hover: { animationDuration: 0 },
            responsiveAnimationDuration: 0
        }
    });
    console.log("Rendered Range Usage Detail chart.");
}





/**
 * Updates the summary metric cards based on appState.latestData.summary
 * and calculates trends using appState.historicalData.
 * @param {Object} filteredMetrics - Optional metrics object from filtered API data
 */
function updateUtilizationMetrics(filteredMetrics = null) {
    console.log("Attempting to update utilization metric cards...");

    if (filteredMetrics) {
        console.log("Using filtered metrics for utilization cards:", filteredMetrics);
    } else {
        console.log("Using default metrics from appState:");
        console.log("Current appState.latestData:", appState.latestData);
        console.log("Current appState.historicalData:", appState.historicalData);
    }

    let utilizationRate = 'N/A', dormancyRate = 'N/A', reservationRate = 'N/A', growthRate = 'N/A';
    let utilizationChange = 0, dormancyChange = 0, reservationChange = 0, growthChange = 0; // Default to 0 change

    // --- Use filtered metrics if provided, otherwise use appState data ---
    if (filteredMetrics) {
        // Use filtered metrics from API
        utilizationRate = (filteredMetrics.utilization_rate === null || isNaN(parseFloat(filteredMetrics.utilization_rate))) ? 'N/A' : parseFloat(filteredMetrics.utilization_rate).toFixed(1);
        dormancyRate = (filteredMetrics.dormancy_rate === null || isNaN(parseFloat(filteredMetrics.dormancy_rate))) ? 'N/A' : parseFloat(filteredMetrics.dormancy_rate).toFixed(1);
        reservationRate = (filteredMetrics.reservation_rate === null || isNaN(parseFloat(filteredMetrics.reservation_rate))) ? 'N/A' : parseFloat(filteredMetrics.reservation_rate).toFixed(1);
        growthRate = (filteredMetrics.growth_rate === null || isNaN(parseFloat(filteredMetrics.growth_rate))) ? 'N/A' : parseFloat(filteredMetrics.growth_rate).toFixed(1);

        // Use changes from filtered metrics if available
        utilizationChange = (filteredMetrics.utilization_change === null || isNaN(parseFloat(filteredMetrics.utilization_change))) ? 0 : parseFloat(filteredMetrics.utilization_change);
        dormancyChange = (filteredMetrics.dormancy_change === null || isNaN(parseFloat(filteredMetrics.dormancy_change))) ? 0 : parseFloat(filteredMetrics.dormancy_change);
        reservationChange = (filteredMetrics.reservation_change === null || isNaN(parseFloat(filteredMetrics.reservation_change))) ? 0 : parseFloat(filteredMetrics.reservation_change);
    }
    // --- Read metrics from the latest data's summary ---
    else if (appState.latestData && appState.latestData.summary) {
        const summary = appState.latestData.summary;
        console.log("Found latest summary data:", summary);

        // Use parseFloat and provide default 'N/A' if metric is missing, null, undefined or not a number
        utilizationRate = (summary.utilization_rate === null || isNaN(parseFloat(summary.utilization_rate))) ? 'N/A' : parseFloat(summary.utilization_rate).toFixed(1);
        dormancyRate = (summary.dormancy_rate === null || isNaN(parseFloat(summary.dormancy_rate))) ? 'N/A' : parseFloat(summary.dormancy_rate).toFixed(1);
        reservationRate = (summary.reservation_rate === null || isNaN(parseFloat(summary.reservation_rate))) ? 'N/A' : parseFloat(summary.reservation_rate).toFixed(1);
        growthRate = (summary.growth_rate === null || isNaN(parseFloat(summary.growth_rate))) ? 'N/A' : parseFloat(summary.growth_rate).toFixed(1);

         // --- Calculate Trend/Change (Compare with previous historical entry if available) ---
         // Skip this section if we're using filtered metrics (changes already provided)
         if (!filteredMetrics) {
             // Ensure historicalData is sorted chronologically (oldest to newest) by the backend or sort here
             // Assuming backend passes it sorted newest first as before, let's reverse for easier trend calc
             //const sortedHistorical = [...appState.historicalData].reverse(); // Create a reversed copy

             // Check if historicalData is sorted correctly first. Assuming newest is first [0].
            if (appState.historicalData && appState.historicalData.length > 1) {
                // Latest data corresponds to historicalData[0]. Previous is historicalData[1].
                const previousData = appState.historicalData[1]; // Get the one before latest

                 if (previousData && previousData.summary) {
                    const prevSummary = previousData.summary;
                    console.log("Found previous summary for trend:", prevSummary);

                    // Safely parse previous values
                    const prevUtil = (prevSummary.utilization_rate === null || isNaN(parseFloat(prevSummary.utilization_rate))) ? null : parseFloat(prevSummary.utilization_rate);
                    const prevDorm = (prevSummary.dormancy_rate === null || isNaN(parseFloat(prevSummary.dormancy_rate))) ? null : parseFloat(prevSummary.dormancy_rate);
                    const prevRes = (prevSummary.reservation_rate === null || isNaN(parseFloat(prevSummary.reservation_rate))) ? null : parseFloat(prevSummary.reservation_rate);
                    const prevGrow = (prevSummary.growth_rate === null || isNaN(parseFloat(prevSummary.growth_rate))) ? null : parseFloat(prevSummary.growth_rate); // Growth might be null

                    if (prevUtil !== null && utilizationRate !== 'N/A') {
                        utilizationChange = (parseFloat(utilizationRate) - prevUtil).toFixed(1);
                    }
                 if (prevDorm !== null && dormancyRate !== 'N/A') {
                    dormancyChange = (parseFloat(dormancyRate) - prevDorm).toFixed(1);
                }
                 if (prevRes !== null && reservationRate !== 'N/A') {
                    reservationChange = (parseFloat(reservationRate) - prevRes).toFixed(1);
                }
                 if (prevGrow !== null && growthRate !== 'N/A') {
                    growthChange = (parseFloat(growthRate) - prevGrow).toFixed(1);
                }
            } else {
                 console.warn("Could not find previous summary data in historicalData[1] for trend calculation.");
            }
        } else {
             console.log("Not enough historical data (need at least 2 entries) to calculate trends.");
        }
        } // Close the if(!filteredMetrics) block

    } else {
        console.warn("appState.latestData or appState.latestData.summary is missing. Cannot update metric cards.");
    }

    console.log(`Updating cards with values: Util=${utilizationRate}, Dorm=${dormancyRate}, Res=${reservationRate}, Grow=${growthRate}`);
    console.log(`Updating cards with changes: Util=${utilizationChange}, Dorm=${dormancyChange}, Res=${reservationChange}, Grow=${growthChange}`);

    // Update DOM elements using helper
    updateMetricCard('utilization', utilizationRate, utilizationChange);
    updateMetricCard('dormancy', dormancyRate, dormancyChange);
    updateMetricCard('reservation', reservationRate, reservationChange);
    updateMetricCard('growth', growthRate, growthChange); // Assuming a 'growth' card exists
     console.log("Metric card update attempt finished.");
}

/**
 * Helper function to update a single metric card's value and trend display.
 * Assumes metric cards have IDs like 'metric-utilization', 'metric-dormancy', etc.
 * Handles 'N/A' values and calculates trend indicator style.
 * @param {string} metricType - 'utilization', 'dormancy', 'reservation', 'growth'
 * @param {string|number} value - The metric value (e.g., '55.1' or 'N/A')
 * @param {string|number} change - The change from the previous period (e.g., '1.2', '-0.5', 0, or null)
 */
function updateMetricCard(metricType, value, change) {
    // Use let instead of const because the fallback logic might reassign it (though primary path uses ID)
    let card = document.getElementById(`metric-${metricType}`); // e.g., id="metric-utilization"

    if (!card) {
        console.warn(`Metric card container with ID 'metric-${metricType}' not found.`);
        // Optional: Add fallback logic here if needed, but fixing HTML IDs is preferred.
        return; // Exit if card not found
    }

    const valueElement = card.querySelector('.metric-value'); // Find element with class 'metric-value' inside the card
    const trendElement = card.querySelector('.metric-trend'); // Find element with class 'metric-trend' inside the card

    // Update value display
    if (valueElement) {
        valueElement.textContent = (value === 'N/A' || value === null || value === undefined) ? 'N/A' : `${value}%`;
    } else {
        console.warn(`'.metric-value' element not found inside #metric-${metricType}`);
    }

    // Update trend display
    if (trendElement) {
        // Reset trend display first to default (e.g., neutral icon, muted text)
        trendElement.className = 'metric-trend text-muted small'; // Base classes
        trendElement.innerHTML = '<i class="fas fa-minus"></i> --'; // Default display for N/A or no change

        const numChange = parseFloat(change); // Convert change to number for comparison

        if (value !== 'N/A' && value !== null && value !== undefined && !isNaN(numChange) && numChange !== 0) {
            // Determine if change is positive or negative in terms of goal (lower dormancy is good)
            const isPositiveChange = (metricType === 'dormancy') ? numChange < 0 : numChange > 0;
            const trendClass = isPositiveChange ? 'text-success' : 'text-danger';
            const trendIcon = isPositiveChange ? 'fa-arrow-up' : 'fa-arrow-down';

            trendElement.classList.remove('text-muted'); // Remove default muted color
            trendElement.classList.add(trendClass); // Add success or danger color
            trendElement.innerHTML = `<i class="fas ${trendIcon}"></i> ${Math.abs(numChange).toFixed(1)}%`;
        } else if (value !== 'N/A' && value !== null && value !== undefined && numChange === 0) {
            // Explicitly show zero change with an equals sign
            trendElement.innerHTML = '<i class="fas fa-equals"></i> 0.0%';
        } // Otherwise, the default '--' remains for N/A value or invalid change
    } else {
        console.warn(`'.metric-trend' element not found inside #metric-${metricType}`);
    }
}

// --- ALSO NEED TREND CHART IMPLEMENTATION ADAPTED FOR appState ---

/**
 * Renders the Trend Chart as a grouped bar chart using Chart.js.
 * Reads data from appState.trendChartData which comes from window.E164Data.trendChartData.
 */
function renderTrendChart() {
    const containerId = 'trend-chart'; // Ensure this div exists in HTML
    const canvas = prepareChartContainer(containerId);
    if (!canvas) return;

    // Check if necessary chart data is available
    // First try to use appState.trendChartData, fall back to appState.chartData if needed
    const chartData = appState.trendChartData || appState.chartData;

        // *** ADD THIS LINE FOR DEBUGGING ***
    console.log("Data for Trend Chart:", JSON.stringify(chartData, null, 2));

    if (!chartData || !chartData.labels || chartData.labels.length === 0) {
        console.warn("Trend Chart: Data (labels) is missing or empty in appState.trendChartData/chartData.");
        document.getElementById(containerId).innerHTML = `<div class="alert alert-info">Not enough historical data for trend chart.</div>`;
        return;
    }

    console.log("Rendering trend chart with data:", chartData);

    // Create a new Chart.js instance with the bar chart configuration
    appState.charts[containerId] = new Chart(canvas, {
        type: 'bar', // Change to bar chart type for grouped bars
        data: {
            labels: chartData.labels, // Use labels from the chart data
            datasets: chartData.datasets || [] // Use datasets directly from the chart data
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' },
                title: { display: true, text: 'Metric Trends by Period' },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.dataset.label || '';
                            if (label) label += ': ';
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(1) + '%';
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: { display: true, text: 'Reporting Period' }
                },
                y: {
                    beginAtZero: true,
                    suggestedMax: 100, // For percentage values
                    title: { display: true, text: 'Rate (%)' },
                    ticks: {
                        // Format y-axis ticks as percentages
                        callback: function(value) { return value + '%'; }
                    }
                }
            }
        }
    });

    console.log("Rendered Trend Chart as grouped bar chart.");
}






// e164-audit.js

/**
 * Renders the Status Distribution Doughnut Chart.
 * Uses filtered statusData if provided (for admin refresh).
 * Otherwise, defaults to using appState.latestData.summary (for operator view AND admin initial load).
 * @param {Object|null} statusData - Optional filtered aggregate status counts from API.
 * Expected structure: { total_active_subscriber, total_active_non_subscriber, total_inactive, total_reserved }
 */
function renderStatusDistributionChart(statusData = null) {
    const containerId = 'status-distribution-chart';
    const canvas = prepareChartContainer(containerId);
    if (!canvas) return;

    let dataToUse = null;
    let dataSource = ""; // For logging

    // Priority 1: Use filtered data if provided
    if (statusData && typeof statusData === 'object' && statusData !== null) {
        console.log("Using filtered status data for distribution chart:", statusData);
        dataToUse = [
            statusData.total_active_subscriber || 0,
            statusData.total_active_non_subscriber || 0,
            statusData.total_inactive || 0,
            statusData.total_reserved || 0
        ];
        dataSource = "Filtered API Data";
    }
    // Priority 2: Use latestData summary if available (for Operator OR Admin initial load)
    else if (appState.latestData && appState.latestData.summary) {
        console.log("Using latestData summary for distribution chart.");
        const summary = appState.latestData.summary;
        dataToUse = [
            summary.total_active_subscriber || 0,
            summary.total_active_non_subscriber || 0,
            summary.total_inactive || 0,
            summary.total_reserved || 0
        ];
         dataSource = `Latest Submission Summary (${appState.isAdmin ? 'Admin Default' : 'Operator'})`;
    }

    // If no usable data found after checking both sources
    if (!dataToUse) {
        console.warn("Status Distribution: Required data (filtered or latest summary) is missing.");
        document.getElementById(containerId).innerHTML = '<div class="alert alert-info text-center p-4">Status distribution data is currently unavailable.</div>';
        return;
    }

    const labels = ['Active Subscriber', 'Active Non-Subscriber', 'Inactive', 'Reserved'];
    const data = dataToUse;
    const chartTitle = `Number Status Distribution (${dataSource})`; // Add source to title

    // Check if all data points are zero
    if (data.every(item => item === 0)) {
        console.log("Status Distribution: All data points are zero.");
        document.getElementById(containerId).innerHTML = '<div class="alert alert-info text-center p-4">No non-zero status counts available to display.</div>';
        return;
    }

    // --- Render Chart.js Doughnut Chart ---
    appState.charts[containerId] = new Chart(canvas, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                label: 'Number Status',
                data: data,
                backgroundColor: [
                    'rgb(75, 192, 192)', // Teal
                    'rgb(54, 162, 235)',  // Blue
                    'rgb(255, 159, 64)', // Orange
                    'rgb(153, 102, 255)'  // Purple
                ],
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: chartTitle // Use dynamic title
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            if (label) label += ': ';
                            const value = context.parsed || 0;
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = total > 0 ? ((value / total) * 100).toFixed(1) : 0;
                            label += `${value.toLocaleString()} (${percentage}%)`;
                            return label;
                        }
                    }
                }
            }
        }
    });
    console.log("Rendered Status Distribution Chart using:", dataSource);
}

/**
 * Renders the Market Share Pie Chart (Handles Admin vs Operator View).
 * Reads data from appState.marketShareResult.
 */
function renderMarketShareChart() {
    const containerId = 'market-share-chart'; // Use the correct ID from HTML
    const canvas = prepareChartContainer(containerId);
    if (!canvas) return;

    const marketData = appState.marketShareResult; // Get data from appState
    const currentUserOperatorId = appState.latestData?.operator_id; // Get current operator's ID if available

    console.log("Rendering Market Share Chart. Data:", marketData);
    console.log("Is Admin:", appState.isAdmin, "Current Operator ID:", currentUserOperatorId);


    // --- Basic Data Validation ---
    if (!marketData || !marketData.operator_shares || marketData.total_allocation === undefined || marketData.total_allocation <= 0) {
        console.warn("Market Share: Data is missing, empty, or total allocation is zero.");
        document.getElementById(containerId).innerHTML = `<div class="alert alert-info">Market share data is unavailable or total allocation is zero for the latest approved period.</div>`;
        return;
    }

    let labels = [];
    let data = [];
    let chartTitle = 'Market Share (Based on Total Allocated Numbers)'; // Default title

    // --- Generate Chart Data Based on Role ---
    if (appState.isAdmin) {
        // ADMIN VIEW: Show all operators
        chartTitle = `Market Share - ${marketData.period || 'Latest Period'}`;
        labels = marketData.operator_shares.map(op => op.operator_name || `Unknown (${op.operator_id?.slice(-4)})`);
        data = marketData.operator_shares.map(op => op.allocated || 0);

    } else {
        // OPERATOR VIEW: Show "My Share" vs "Others"
        chartTitle = `Your Market Share - ${marketData.period || 'Latest Period'}`;
        let myAllocation = 0;

        console.log("Building Operator View of Market Share Data");

        // Find the current operator's allocation
        const myShareData = marketData.operator_shares.find(op => op.operator_id === currentUserOperatorId);
        if (myShareData) {
            myAllocation = myShareData.allocated || 0;
        } else {
            console.warn(`Market Share: Could not find current operator (${currentUserOperatorId}) in share data.`);
            // Optionally still show the pie with others if needed, or display message
        }

        const othersAllocation = marketData.total_allocation - myAllocation;

        labels = ['My Share', 'Others'];
        data = [myAllocation, othersAllocation];

        // Handle case where operator has 0 share but others exist
        if (myAllocation <= 0 && othersAllocation > 0) {
             console.log("Operator has zero share, showing only 'Others'.");
             labels = ['Others'];
             data = [othersAllocation];
        } else if (myAllocation <=0 && othersAllocation <=0){
             // This case should be caught by the initial total_allocation check, but as safety:
             document.getElementById(containerId).innerHTML = `<div class="alert alert-info">No allocation data found for comparison.</div>`;
             return;
        }
    }

    // --- Render the Chart ---
    // Define a color palette
     const defaultColors = [
         '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b', '#858796',
         '#5a5c69', '#a073ff', '#6f42c1', '#d63384', '#fd7e14', '#20c997'
     ];
     // Use specific colors for Operator view, cycle defaults for Admin view
     const backgroundColors = appState.isAdmin
         ? data.map((_, i) => defaultColors[i % defaultColors.length])
         : ['#1cc88a', '#e0e0e0']; // Green for 'My Share', Grey for 'Others'


    appState.charts[containerId] = new Chart(canvas, {
        type: 'pie', // Or 'doughnut'
        data: {
            labels: labels,
            datasets: [{
                label: 'Total Allocated Numbers',
                data: data,
                backgroundColor: backgroundColors,
                hoverOffset: 4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                },
                title: {
                    display: true,
                    text: chartTitle // Use dynamic title
                },
                tooltip: {
                     callbacks: {
                        label: function(context) {
                            let label = context.label || '';
                            if (label) {
                                label += ': ';
                            }
                            const value = context.parsed || 0;
                            // Calculate percentage based on the total for the *rendered* chart
                            const totalRendered = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = totalRendered > 0 ? ((value / totalRendered) * 100).toFixed(1) : 0;
                            label += `${value.toLocaleString()} (${percentage}%)`;
                            // Optionally add overall market share % for admins
                            if(appState.isAdmin && marketData.total_allocation > 0){
                                const marketPercentage = ((value / marketData.total_allocation) * 100).toFixed(1);
                                label += ` [Market: ${marketPercentage}%]`
                            }
                            return label;
                        }
                    }
                }
            }
        }
    });
     console.log(`Rendered Market Share Chart for ${appState.isAdmin ? 'Admin' : 'Operator'}.`);
}


/**
 * Renders the Operator Comparison Bar Chart (Admin only).
 * Compares a primary metric (e.g., Total Allocated) across operators.
 * Assumes data comes from window.E164Data.operatorComparison or similar.
 */
function renderOperatorComparisonChart() {
    const containerId = 'operator-comparison-chart'; // Ensure this div exists
    const canvas = prepareChartContainer(containerId);
    if (!canvas) return;

    // Requires admin-specific comparison data
     let comparisonData = window.E164Data?.operatorComparison; // Adjust source if needed

    if (!comparisonData || comparisonData.length === 0) {
        console.warn("Operator Comparison: Data is missing or empty.");
        document.getElementById(containerId).innerHTML = `<div class="alert alert-info">Operator comparison data is unavailable.</div>`;
        return;
    }

    // Sort data for better visualization (e.g., descending by allocated)
    comparisonData.sort((a, b) => (b.total_allocated || 0) - (a.total_allocated || 0));

    const labels = comparisonData.map(op => op.operator || `Op ${op.operator_id?.slice(-4) || 'N/A'}`);
    const allocatedData = comparisonData.map(op => op.total_allocated || 0);
    const activeData = comparisonData.map(op => op.total_active || 0); // Example secondary metric

    appState.charts[containerId] = new Chart(canvas, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Total Allocated',
                    data: allocatedData,
                    backgroundColor: 'rgba(75, 192, 192, 0.6)', // Teal
                    borderColor: 'rgb(75, 192, 192)',
                    borderWidth: 1
                },
                {
                    label: 'Total Active',
                    data: activeData,
                    backgroundColor: 'rgba(54, 162, 235, 0.6)', // Blue
                    borderColor: 'rgb(54, 162, 235)',
                    borderWidth: 1,
                    hidden: true // Optionally hide secondary metric initially
                }
            ]
        },
        options: {
            indexAxis: 'y', // Bars run horizontally for better label readability
            responsive: true,
            maintainAspectRatio: false, // Allow chart to fill container height
            plugins: {
                legend: { position: 'top' },
                title: { display: true, text: 'Operator Comparison (Allocated & Active)' },
                tooltip: {
                    callbacks: {
                         label: function(context) {
                             let label = context.dataset.label || '';
                             if (label) { label += ': '; }
                             if (context.parsed.x !== null) { // Use 'x' for horizontal bars
                                 label += context.parsed.x.toLocaleString();
                             }
                             return label;
                         }
                     }
                }
            },
            scales: {
                x: { // Use 'x' for horizontal bars
                    beginAtZero: true,
                    title: { display: true, text: 'Number Count' }
                },
                y: { // Use 'y' for horizontal bars
                    beginAtZero: true,
                    // Optional: Limit number of operators shown directly on axis if too many
                }
            }
        }
    });
     console.log("Rendered Operator Comparison Chart.");
}

/**
 * Renders the Utilization Comparison Bar Chart (Admin only).
 * Compares utilization rates across operators.
 * Assumes data comes from window.E164Data.operatorComparison or similar.
 */
function renderUtilizationComparisonChart() {
    const containerId = 'utilization-comparison-chart'; // Ensure this div exists
    const canvas = prepareChartContainer(containerId);
    if (!canvas) return;

    // Requires admin-specific comparison data
     let comparisonData = window.E164Data?.operatorComparison; // Adjust source if needed

    if (!comparisonData || comparisonData.length === 0) {
        console.warn("Utilization Comparison: Data is missing or empty.");
         document.getElementById(containerId).innerHTML = `<div class="alert alert-info">Operator utilization data is unavailable.</div>`;
        return;
    }

    // Sort data for better visualization (e.g., descending by utilization)
    comparisonData.sort((a, b) => (b.utilization_rate || 0) - (a.utilization_rate || 0));

    const labels = comparisonData.map(op => op.operator || `Op ${op.operator_id?.slice(-4) || 'N/A'}`);
    const utilizationData = comparisonData.map(op => op.utilization_rate || 0);

     appState.charts[containerId] = new Chart(canvas, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [
                {
                    label: 'Utilization Rate (%)',
                    data: utilizationData,
                    backgroundColor: 'rgba(255, 159, 64, 0.6)', // Orange
                    borderColor: 'rgb(255, 159, 64)',
                    borderWidth: 1
                },
                // Add Dormancy Rate etc. if needed
                {
                    label: 'Dormancy Rate (%)',
                    data: comparisonData.map(op => op.dormancy_rate || 0),
                    backgroundColor: 'rgba(153, 102, 255, 0.6)', // Purple
                    borderColor: 'rgb(153, 102, 255)',
                    borderWidth: 1,
                    hidden: true
                }
            ]
        },
        options: {
            // indexAxis: 'y', // Optional: Use horizontal bars if many operators
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'top' },
                title: { display: true, text: 'Operator Utilization Rate Comparison' },
                tooltip: {
                    callbacks: {
                         label: function(context) {
                             let label = context.dataset.label || '';
                             if (label) { label += ': '; }
                             if (context.parsed.y !== null) { // Use 'y' for vertical bars
                                 label += context.parsed.y.toFixed(1) + '%';
                             }
                             return label;
                         }
                     }
                }
            },
            scales: {
                 y: { // Use 'y' for vertical bars
                     beginAtZero: true,
                     max: 100, // Utilization is a percentage
                     title: { display: true, text: 'Rate (%)' },
                     ticks: {
                          callback: function(value) { return value + '%' }
                      }
                 },
                 x: { // Use 'x' for vertical bars
                     // Optional: Rotate labels if they overlap
                     // ticks: { autoSkip: false, maxRotation: 90, minRotation: 45 }
                 }
            }
        }
    });
      console.log("Rendered Utilization Comparison Chart.");
}



    /** Creates the Sankey diagram for porting activity. (Enhanced Version) */
    function createSankeyChart() {
        const containerId = 'sankey-chart';
        const container = document.getElementById(containerId);
        console.log(`[Sankey] Attempting chart in #${containerId}...`);
        if (!container) { console.error(`[Sankey] Container #${containerId} not found.`); return; }

        container.innerHTML = `<div class="d-flex justify-content-center align-items-center p-5" style="min-height: 300px;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="ms-3 mb-0">Loading Porting Data...</p></div>`;

        if (typeof d3 === 'undefined' || typeof d3.sankey !== 'function') {
            console.error("[Sankey] D3.js / d3.sankey not loaded.");
            container.innerHTML = `<div class="alert alert-danger">Error: Charting library not loaded.</div>`; return;
        }

        fetch('/e164/api/porting-activity')
            .then(response => {
                if (!response.ok) { return response.text().then(text => { throw new Error(`Network error: ${response.status} ${response.statusText}. ${text}`); }); }
                return response.json();
            })
            .then(data => {
                console.log("[Sankey] Received data:", data);
                container.innerHTML = ''; // Clear loading

                if (!data || !data.success) { throw new Error(data?.error || 'API call failed'); }
                if (!data.nodes || !data.links || data.nodes.length === 0 || data.links.length === 0) {
                    container.innerHTML = `<div class="alert alert-info">No porting activity data found</div>`; return;
                }

                // --- D3 Rendering ---
                try {
                    console.log("[Sankey] Rendering...");
                    const margin = {top: 40, right: 20, bottom: 20, left: 20};
                    const containerWidth = container.clientWidth || 600;
                    const width = containerWidth - margin.left - margin.right;
                    const height = Math.max(350, Math.min(800, data.nodes.length * 25 + data.links.length * 5)) - margin.top - margin.bottom;

                    const svg = d3.select(container).append("svg")
                        .attr("width", width + margin.left + margin.right).attr("height", height + margin.top + margin.bottom)
                        .attr("viewBox", `0 0 ${width + margin.left + margin.right} ${height + margin.top + margin.bottom}`)
                        .attr("style", "max-width: 100%; height: auto; font: 10px sans-serif;");
                    const g = svg.append("g").attr("transform", `translate(${margin.left},${margin.top})`);

                    g.append("text").attr("x", width / 2).attr("y", 0 - (margin.top / 2)).attr("text-anchor", "middle")
                       .style("font-size", "16px").style("font-weight", "bold").style("fill", "#333")
                       .text(`Porting Flow: ${data.operator} (${data.period})`);

                    const sankey = d3.sankey().nodeWidth(15).nodePadding(12).nodeAlign(d3.sankeyJustify)
                                     .extent([[1, 1], [width - 1, height - 1]]);
                    const graph = sankey({ nodes: data.nodes.map(d => ({...d})), links: data.links.map(d => ({...d})) });
                    const color = d3.scaleOrdinal(d3.schemeTableau10);

                    // Links
                    const link = g.append("g").attr("class", "links").attr("fill", "none").attr("stroke-opacity", 0.45)
                      .selectAll("g").data(graph.links).join("g");
                    link.append("path").attr("d", d3.sankeyLinkHorizontal()).attr("stroke", d => color(d.source.name))
                        .attr("stroke-width", d => Math.max(1, d.width));
                    link.append("title").text(d => `${d.source.name} → ${d.target.name}\n--------------------\nValue: ${d.value.toLocaleString()}${d.details ? `\nRanges: ${d.details}` : ''}`);

                    // Nodes
                    const node = g.append("g").attr("class", "nodes").attr("stroke", "#333").attr("stroke-width", 0.5)
                      .selectAll("rect").data(graph.nodes).join("rect")
                        .attr("x", d => d.x0).attr("y", d => d.y0).attr("height", d => Math.max(1, d.y1 - d.y0))
                        .attr("width", d => d.x1 - d.x0).attr("fill", d => color(d.name)).attr("fill-opacity", 0.85);
                    node.append("title").text(d => `${d.name}\nTotal Flow: ${d.value.toLocaleString()}`);

                    // Labels
                    g.append("g").attr("class", "node-labels").style("font", "10px sans-serif").style("font-weight", "bold")
                      .selectAll("text").data(graph.nodes).join("text")
                        .attr("x", d => d.x0 < width / 2 ? d.x1 + 6 : d.x0 - 6).attr("y", d => (d.y1 + d.y0) / 2)
                        .attr("dy", "0.35em").attr("text-anchor", d => d.x0 < width / 2 ? "start" : "end")
                        .attr("fill", "#000").style("text-shadow", "0 1px 0 #fff, 1px 0 0 #fff, 0 -1px 0 #fff, -1px 0 0 #fff")
                        .text(d => d.name);

                     console.log("[Sankey] Render complete.");
                } catch (error) {
                    console.error("[Sankey] Render Error:", error);
                    container.innerHTML = `<div class="alert alert-danger">Error rendering chart: ${error.message}</div>`;
                }
            })
            .catch(error => {
                console.error("[Sankey] Fetch/Process Error:", error);
                container.innerHTML = `<div class="alert alert-danger">Failed to load data: ${error.message}</div>`;
            });
    }

    // =========================================================================
    // History Tab Handling
    // =========================================================================
    /** Fetches and updates the submission history table. */
    async function updateHistoryTable() {
        const tableBody = document.querySelector('#history-table tbody');
        if (!tableBody) return;
        const colspan = appState.isAdmin ? 7 : 6; // Adjust colspan based on admin view
        tableBody.innerHTML = `<tr><td colspan="${colspan}" class="text-center p-5"><span class="spinner-border spinner-border-sm"></span> Loading history...</td></tr>`;

        try {

            const historyApiUrl = '/e164/api/submissions/history'; // Ensure correct URL
            const response = await fetch(historyApiUrl);
            if (!response.ok) {
                 const errorData = await response.json().catch(() => ({ error: `HTTP error ${response.status}` }));
                 throw new Error(errorData.error || `HTTP error ${response.status}`);
            }
            const data = await response.json();

            if (data.success && Array.isArray(data.submissions)) {
                tableBody.innerHTML = ''; // Clear loading/previous data
                if (data.submissions.length === 0) {
                     tableBody.innerHTML = `<tr><td colspan="${colspan}" class="text-center text-muted p-4">No submission history found.</td></tr>`;
                     return;
                }

                data.submissions.forEach(sub => {
                    const row = tableBody.insertRow();
                    let submittedDate = 'N/A';
                    if (sub.submitted_at) {
                        try { submittedDate = new Date(sub.submitted_at).toLocaleString(); } catch (e) {}
                    }
                    let actionsHtml = `<a href="/e164/submission/view/${sub.id}" class="btn btn-sm btn-outline-info view-submission-btn" data-id="${sub.id}" title="View"><i class="fas fa-eye"></i></a>`;
                    if (sub.status === 'draft' && !appState.isAdmin) {
                         actionsHtml += `<a href="/e164/submission/edit/${sub.id}" class="btn btn-sm btn-outline-primary ms-1 edit-submission-btn" data-id="${sub.id}" title="Edit"><i class="fas fa-edit"></i></a>`;
                    }

                    // Construct row cells
                    let cells = '';
                    if (appState.isAdmin) cells += `<td>${sub.operator_name || 'N/A'}</td>`;
                    cells += `<td>${sub.reporting_period || 'N/A'}</td>`;
                    cells += `<td>${submittedDate}</td>`;
                    cells += `<td><span class="badge rounded-pill bg-${getStatusBadgeClass(sub.status)}">${sub.status || 'N/A'}</span></td>`;
                    cells += `<td>${sub.total_allocated != null ? sub.total_allocated.toLocaleString() : 'N/A'}</td>`;
                    cells += `<td>${sub.utilization_rate != null ? `${sub.utilization_rate.toFixed(1)}%` : 'N/A'}</td>`;
                    cells += `<td>${actionsHtml}</td>`;
                    row.innerHTML = cells;
                });
            } else {
                 throw new Error(data.error || 'Failed to load history.');
            }
        } catch (error) {
            console.error("Error fetching submission history:", error);
            tableBody.innerHTML = `<tr><td colspan="${colspan}" class="text-center text-danger p-4">Error loading history: ${error.message}</td></tr>`;
            showToast("Error", `Failed to load history: ${error.message}`, "danger");
        }
    }

    /** Helper to get badge class based on submission status */
    function getStatusBadgeClass(status) {
        switch (status?.toLowerCase()) {
            case 'approved': return 'success';
            case 'pending': return 'warning';
            case 'rejected': return 'danger';
            case 'draft': return 'secondary';
            default: return 'light text-dark';
        }
    }

    // =========================================================================
    // Start the application
    // =========================================================================
    init();

}); // End of DOMContentLoaded
