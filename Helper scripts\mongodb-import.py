from pymongo import MongoClient
import json
import sys
import os

def import_to_mongodb(json_file, connection_string, db_name="new_numbering", collection_name="audits"):
    """
    Import JSON audit data to MongoDB
    
    Args:
        json_file: Path to JSON file with audit documents
        connection_string: MongoDB connection string
        db_name: Database name
        collection_name: Collection name
    """
    # Load JSON data
    with open(json_file, 'r') as f:
        audit_documents = json.load(f)
    
    # Connect to MongoDB
    client = MongoClient(connection_string)
    db = client[db_name]
    collection = db[collection_name]
    
    # Insert documents
    if isinstance(audit_documents, list):
        result = collection.insert_many(audit_documents)
        print(f"Inserted {len(result.inserted_ids)} documents into {db_name}.{collection_name}")
    else:
        result = collection.insert_one(audit_documents)
        print(f"Inserted document with ID {result.inserted_id} into {db_name}.{collection_name}")
    
    client.close()

def main():
    # MongoDB connection string
    connection_string = "mongodb+srv://francisyiryel:<EMAIL>/new_numbering?retryWrites=true&w=majority&appName=Cluster0"
    
    if len(sys.argv) < 2:
        print("Usage: python import_script.py <json_file> [<db_name>] [<collection_name>]")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    # Optional parameters
    db_name = sys.argv[2] if len(sys.argv) > 2 else "new_numbering"
    collection_name = sys.argv[3] if len(sys.argv) > 3 else "audits"
    
    # Import data
    import_to_mongodb(json_file, connection_string, db_name, collection_name)

if __name__ == "__main__":
    main()
