{% extends "base.html" %}
{% block content %}
<div class="glass-card animated">
    <h3 class="mb-4">Create New User</h3>
    
    <form method="POST">
        {{ form.hidden_tag() }}
        
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="username" class="form-label">Username</label>
                {{ form.username(class="form-control", placeholder="Enter username") }}
                {% if form.username.errors %}
                    <div class="text-danger">{{ form.username.errors[0] }}</div>
                {% endif %}
            </div>
            <div class="col-md-6">
                <label for="email" class="form-label">Email</label>
                {{ form.email(class="form-control", placeholder="Enter email") }}
                {% if form.email.errors %}
                    <div class="text-danger">{{ form.email.errors[0] }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="password" class="form-label">Password</label>
                {{ form.password(class="form-control", placeholder="Enter password") }}
                {% if form.password.errors %}
                    <div class="text-danger">{{ form.password.errors[0] }}</div>
                {% endif %}
            </div>
            <div class="col-md-6">
                <label for="confirm_password" class="form-label">Confirm Password</label>
                {{ form.confirm_password(class="form-control", placeholder="Confirm password") }}
                {% if form.confirm_password.errors %}
                    <div class="text-danger">{{ form.confirm_password.errors[0] }}</div>
                {% endif %}
            </div>
        </div>
        
        <div class="row mb-3">
            <div class="col-md-6">
                <label for="user_type" class="form-label">User Type</label>
                {{ form.user_type(class="form-select", id="user_type") }}
            </div>
            
            <div class="col-md-6 operator-field" style="display: none;">
                <label for="operator_id" class="form-label">Operator</label>
                {{ form.operator_id(class="form-select") }}
            </div>
            
            <div class="col-md-6 vas-field" style="display: none;">
                <label for="vas_id" class="form-label">VAS ID</label>
                {{ form.vas_id(class="form-control", placeholder="Enter VAS ID") }}
            </div>
        </div>
        
        <div class="d-flex justify-content-between mt-4">
            <a href="{{ url_for('e164.list_users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to List
            </a>
            {{ form.submit(class="btn btn-primary") }}
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const userTypeSelect = document.getElementById('user_type');
    const operatorField = document.querySelector('.operator-field');
    const vasField = document.querySelector('.vas-field');
    
    userTypeSelect.addEventListener('change', function() {
        // Hide all conditional fields
        operatorField.style.display = 'none';
        vasField.style.display = 'none';
        
        // Show relevant field based on selection
        if (this.value === 'operator') {
            operatorField.style.display = 'block';
        } else if (this.value === 'vas') {
            vasField.style.display = 'block';
        }
    });
    
    // Initialize based on current selection
    userTypeSelect.dispatchEvent(new Event('change'));
});
</script>
{% endblock %}